{"ast": null, "code": "/**\n * Safe chained function\n *\n * Will only create a new function if needed,\n * otherwise will pass back existing functions or null.\n *\n * @param {function} functions to chain\n * @returns {function|null}\n */\nfunction createChainedFunction(...funcs) {\n  return funcs.filter(f => f != null).reduce((acc, f) => {\n    if (typeof f !== 'function') {\n      throw new Error('Invalid Argument Type, must only provide functions, undefined, or null.');\n    }\n    if (acc === null) return f;\n    return function chainedFunction(...args) {\n      // @ts-expect-error ignore \"this\" error\n      acc.apply(this, args);\n      // @ts-expect-error ignore \"this\" error\n      f.apply(this, args);\n    };\n  }, null);\n}\nexport default createChainedFunction;", "map": {"version": 3, "names": ["createChainedFunction", "funcs", "filter", "f", "reduce", "acc", "Error", "chainedFunction", "args", "apply"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/react-bootstrap/esm/createChainedFunction.js"], "sourcesContent": ["/**\n * Safe chained function\n *\n * Will only create a new function if needed,\n * otherwise will pass back existing functions or null.\n *\n * @param {function} functions to chain\n * @returns {function|null}\n */\nfunction createChainedFunction(...funcs) {\n  return funcs.filter(f => f != null).reduce((acc, f) => {\n    if (typeof f !== 'function') {\n      throw new Error('Invalid Argument Type, must only provide functions, undefined, or null.');\n    }\n    if (acc === null) return f;\n    return function chainedFunction(...args) {\n      // @ts-expect-error ignore \"this\" error\n      acc.apply(this, args);\n      // @ts-expect-error ignore \"this\" error\n      f.apply(this, args);\n    };\n  }, null);\n}\nexport default createChainedFunction;"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,qBAAqBA,CAAC,GAAGC,KAAK,EAAE;EACvC,OAAOA,KAAK,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,IAAI,IAAI,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEF,CAAC,KAAK;IACrD,IAAI,OAAOA,CAAC,KAAK,UAAU,EAAE;MAC3B,MAAM,IAAIG,KAAK,CAAC,yEAAyE,CAAC;IAC5F;IACA,IAAID,GAAG,KAAK,IAAI,EAAE,OAAOF,CAAC;IAC1B,OAAO,SAASI,eAAeA,CAAC,GAAGC,IAAI,EAAE;MACvC;MACAH,GAAG,CAACI,KAAK,CAAC,IAAI,EAAED,IAAI,CAAC;MACrB;MACAL,CAAC,CAACM,KAAK,CAAC,IAAI,EAAED,IAAI,CAAC;IACrB,CAAC;EACH,CAAC,EAAE,IAAI,CAAC;AACV;AACA,eAAeR,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}