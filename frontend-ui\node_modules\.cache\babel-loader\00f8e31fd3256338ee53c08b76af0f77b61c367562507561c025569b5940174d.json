{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\LaravelReact\\\\frontend-ui\\\\src\\\\shop\\\\LoadingSkeleton.jsx\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingSkeleton = ({\n  count\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"row g-4\",\n    children: [[...Array(count)].map((_, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-lg-4 col-md-6 col-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card border-0 shadow-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-skeleton\",\n          style: {\n            height: \"200px\",\n            background: \"#e0e0e0\",\n            animation: \"pulse 1.5s infinite\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-skeleton\",\n            style: {\n              height: \"20px\",\n              width: \"80%\",\n              background: \"#e0e0e0\",\n              marginBottom: \"10px\",\n              animation: \"pulse 1.5s infinite\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-skeleton\",\n            style: {\n              height: \"15px\",\n              width: \"60%\",\n              background: \"#e0e0e0\",\n              animation: \"pulse 1.5s infinite\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 11\n      }, this)\n    }, idx, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 9\n    }, this)), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n          @keyframes pulse {\n            0% { opacity: 0.6; }\n            50% { opacity: 1; }\n            100% { opacity: 0.6; }\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = LoadingSkeleton;\nexport default LoadingSkeleton;\nvar _c;\n$RefreshReg$(_c, \"LoadingSkeleton\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "LoadingSkeleton", "count", "className", "children", "Array", "map", "_", "idx", "style", "height", "background", "animation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "marginBottom", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/shop/LoadingSkeleton.jsx"], "sourcesContent": ["import React from \"react\";\n\nconst LoadingSkeleton = ({ count }) => {\n  return (\n    <div className=\"row g-4\">\n      {[...Array(count)].map((_, idx) => (\n        <div key={idx} className=\"col-lg-4 col-md-6 col-12\">\n          <div className=\"card border-0 shadow-sm\">\n            <div\n              className=\"loading-skeleton\"\n              style={{\n                height: \"200px\",\n                background: \"#e0e0e0\",\n                animation: \"pulse 1.5s infinite\",\n              }}\n            ></div>\n            <div className=\"card-body\">\n              <div\n                className=\"loading-skeleton\"\n                style={{\n                  height: \"20px\",\n                  width: \"80%\",\n                  background: \"#e0e0e0\",\n                  marginBottom: \"10px\",\n                  animation: \"pulse 1.5s infinite\",\n                }}\n              ></div>\n              <div\n                className=\"loading-skeleton\"\n                style={{\n                  height: \"15px\",\n                  width: \"60%\",\n                  background: \"#e0e0e0\",\n                  animation: \"pulse 1.5s infinite\",\n                }}\n              ></div>\n            </div>\n          </div>\n        </div>\n      ))}\n      <style>\n        {`\n          @keyframes pulse {\n            0% { opacity: 0.6; }\n            50% { opacity: 1; }\n            100% { opacity: 0.6; }\n          }\n        `}\n      </style>\n    </div>\n  );\n};\n\nexport default LoadingSkeleton;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EACrC,oBACEF,OAAA;IAAKG,SAAS,EAAC,SAAS;IAAAC,QAAA,GACrB,CAAC,GAAGC,KAAK,CAACH,KAAK,CAAC,CAAC,CAACI,GAAG,CAAC,CAACC,CAAC,EAAEC,GAAG,kBAC5BR,OAAA;MAAeG,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eACjDJ,OAAA;QAAKG,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtCJ,OAAA;UACEG,SAAS,EAAC,kBAAkB;UAC5BM,KAAK,EAAE;YACLC,MAAM,EAAE,OAAO;YACfC,UAAU,EAAE,SAAS;YACrBC,SAAS,EAAE;UACb;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPhB,OAAA;UAAKG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBJ,OAAA;YACEG,SAAS,EAAC,kBAAkB;YAC5BM,KAAK,EAAE;cACLC,MAAM,EAAE,MAAM;cACdO,KAAK,EAAE,KAAK;cACZN,UAAU,EAAE,SAAS;cACrBO,YAAY,EAAE,MAAM;cACpBN,SAAS,EAAE;YACb;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhB,OAAA;YACEG,SAAS,EAAC,kBAAkB;YAC5BM,KAAK,EAAE;cACLC,MAAM,EAAE,MAAM;cACdO,KAAK,EAAE,KAAK;cACZN,UAAU,EAAE,SAAS;cACrBC,SAAS,EAAE;YACb;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC,GA/BER,GAAG;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAgCR,CACN,CAAC,eACFhB,OAAA;MAAAI,QAAA,EACG;AACT;AACA;AACA;AACA;AACA;AACA;IAAS;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACG,EAAA,GAjDIlB,eAAe;AAmDrB,eAAeA,eAAe;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}