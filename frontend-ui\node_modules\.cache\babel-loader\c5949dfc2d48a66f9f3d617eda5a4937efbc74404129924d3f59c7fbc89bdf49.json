{"ast": null, "code": "import { g as getDocument, a as getWindow } from '../shared/ssr-window.esm.mjs';\nimport { b as elementParents, d as elementOffset } from '../shared/utils.mjs';\n\n/* eslint-disable consistent-return */\nfunction Keyboard(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const document = getDocument();\n  const window = getWindow();\n  swiper.keyboard = {\n    enabled: false\n  };\n  extendParams({\n    keyboard: {\n      enabled: false,\n      onlyInViewport: true,\n      pageUpDown: true\n    }\n  });\n  function handle(event) {\n    if (!swiper.enabled) return;\n    const {\n      rtlTranslate: rtl\n    } = swiper;\n    let e = event;\n    if (e.originalEvent) e = e.originalEvent; // jquery fix\n    const kc = e.keyCode || e.charCode;\n    const pageUpDown = swiper.params.keyboard.pageUpDown;\n    const isPageUp = pageUpDown && kc === 33;\n    const isPageDown = pageUpDown && kc === 34;\n    const isArrowLeft = kc === 37;\n    const isArrowRight = kc === 39;\n    const isArrowUp = kc === 38;\n    const isArrowDown = kc === 40;\n    // Directions locks\n    if (!swiper.allowSlideNext && (swiper.isHorizontal() && isArrowRight || swiper.isVertical() && isArrowDown || isPageDown)) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && (swiper.isHorizontal() && isArrowLeft || swiper.isVertical() && isArrowUp || isPageUp)) {\n      return false;\n    }\n    if (e.shiftKey || e.altKey || e.ctrlKey || e.metaKey) {\n      return undefined;\n    }\n    if (document.activeElement && document.activeElement.nodeName && (document.activeElement.nodeName.toLowerCase() === 'input' || document.activeElement.nodeName.toLowerCase() === 'textarea')) {\n      return undefined;\n    }\n    if (swiper.params.keyboard.onlyInViewport && (isPageUp || isPageDown || isArrowLeft || isArrowRight || isArrowUp || isArrowDown)) {\n      let inView = false;\n      // Check that swiper should be inside of visible area of window\n      if (elementParents(swiper.el, `.${swiper.params.slideClass}, swiper-slide`).length > 0 && elementParents(swiper.el, `.${swiper.params.slideActiveClass}`).length === 0) {\n        return undefined;\n      }\n      const el = swiper.el;\n      const swiperWidth = el.clientWidth;\n      const swiperHeight = el.clientHeight;\n      const windowWidth = window.innerWidth;\n      const windowHeight = window.innerHeight;\n      const swiperOffset = elementOffset(el);\n      if (rtl) swiperOffset.left -= el.scrollLeft;\n      const swiperCoord = [[swiperOffset.left, swiperOffset.top], [swiperOffset.left + swiperWidth, swiperOffset.top], [swiperOffset.left, swiperOffset.top + swiperHeight], [swiperOffset.left + swiperWidth, swiperOffset.top + swiperHeight]];\n      for (let i = 0; i < swiperCoord.length; i += 1) {\n        const point = swiperCoord[i];\n        if (point[0] >= 0 && point[0] <= windowWidth && point[1] >= 0 && point[1] <= windowHeight) {\n          if (point[0] === 0 && point[1] === 0) continue; // eslint-disable-line\n          inView = true;\n        }\n      }\n      if (!inView) return undefined;\n    }\n    if (swiper.isHorizontal()) {\n      if (isPageUp || isPageDown || isArrowLeft || isArrowRight) {\n        if (e.preventDefault) e.preventDefault();else e.returnValue = false;\n      }\n      if ((isPageDown || isArrowRight) && !rtl || (isPageUp || isArrowLeft) && rtl) swiper.slideNext();\n      if ((isPageUp || isArrowLeft) && !rtl || (isPageDown || isArrowRight) && rtl) swiper.slidePrev();\n    } else {\n      if (isPageUp || isPageDown || isArrowUp || isArrowDown) {\n        if (e.preventDefault) e.preventDefault();else e.returnValue = false;\n      }\n      if (isPageDown || isArrowDown) swiper.slideNext();\n      if (isPageUp || isArrowUp) swiper.slidePrev();\n    }\n    emit('keyPress', kc);\n    return undefined;\n  }\n  function enable() {\n    if (swiper.keyboard.enabled) return;\n    document.addEventListener('keydown', handle);\n    swiper.keyboard.enabled = true;\n  }\n  function disable() {\n    if (!swiper.keyboard.enabled) return;\n    document.removeEventListener('keydown', handle);\n    swiper.keyboard.enabled = false;\n  }\n  on('init', () => {\n    if (swiper.params.keyboard.enabled) {\n      enable();\n    }\n  });\n  on('destroy', () => {\n    if (swiper.keyboard.enabled) {\n      disable();\n    }\n  });\n  Object.assign(swiper.keyboard, {\n    enable,\n    disable\n  });\n}\nexport { Keyboard as default };", "map": {"version": 3, "names": ["g", "getDocument", "a", "getWindow", "b", "elementParents", "d", "elementOffset", "Keyboard", "_ref", "swiper", "extendParams", "on", "emit", "document", "window", "keyboard", "enabled", "onlyInViewport", "pageUpDown", "handle", "event", "rtlTranslate", "rtl", "e", "originalEvent", "kc", "keyCode", "charCode", "params", "isPageUp", "isPageDown", "isArrowLeft", "isArrowRight", "isArrowUp", "isArrowDown", "allowSlideNext", "isHorizontal", "isVertical", "allowSlidePrev", "shift<PERSON>ey", "altKey", "ctrl<PERSON>ey", "metaKey", "undefined", "activeElement", "nodeName", "toLowerCase", "inView", "el", "slideClass", "length", "slideActiveClass", "swiper<PERSON><PERSON><PERSON>", "clientWidth", "swiperHeight", "clientHeight", "windowWidth", "innerWidth", "windowHeight", "innerHeight", "swiperOffset", "left", "scrollLeft", "swiperCoord", "top", "i", "point", "preventDefault", "returnValue", "slideNext", "slidePrev", "enable", "addEventListener", "disable", "removeEventListener", "Object", "assign", "default"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/swiper/modules/keyboard.mjs"], "sourcesContent": ["import { g as getDocument, a as getWindow } from '../shared/ssr-window.esm.mjs';\nimport { b as elementParents, d as elementOffset } from '../shared/utils.mjs';\n\n/* eslint-disable consistent-return */\nfunction Keyboard(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const document = getDocument();\n  const window = getWindow();\n  swiper.keyboard = {\n    enabled: false\n  };\n  extendParams({\n    keyboard: {\n      enabled: false,\n      onlyInViewport: true,\n      pageUpDown: true\n    }\n  });\n  function handle(event) {\n    if (!swiper.enabled) return;\n    const {\n      rtlTranslate: rtl\n    } = swiper;\n    let e = event;\n    if (e.originalEvent) e = e.originalEvent; // jquery fix\n    const kc = e.keyCode || e.charCode;\n    const pageUpDown = swiper.params.keyboard.pageUpDown;\n    const isPageUp = pageUpDown && kc === 33;\n    const isPageDown = pageUpDown && kc === 34;\n    const isArrowLeft = kc === 37;\n    const isArrowRight = kc === 39;\n    const isArrowUp = kc === 38;\n    const isArrowDown = kc === 40;\n    // Directions locks\n    if (!swiper.allowSlideNext && (swiper.isHorizontal() && isArrowRight || swiper.isVertical() && isArrowDown || isPageDown)) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && (swiper.isHorizontal() && isArrowLeft || swiper.isVertical() && isArrowUp || isPageUp)) {\n      return false;\n    }\n    if (e.shiftKey || e.altKey || e.ctrlKey || e.metaKey) {\n      return undefined;\n    }\n    if (document.activeElement && document.activeElement.nodeName && (document.activeElement.nodeName.toLowerCase() === 'input' || document.activeElement.nodeName.toLowerCase() === 'textarea')) {\n      return undefined;\n    }\n    if (swiper.params.keyboard.onlyInViewport && (isPageUp || isPageDown || isArrowLeft || isArrowRight || isArrowUp || isArrowDown)) {\n      let inView = false;\n      // Check that swiper should be inside of visible area of window\n      if (elementParents(swiper.el, `.${swiper.params.slideClass}, swiper-slide`).length > 0 && elementParents(swiper.el, `.${swiper.params.slideActiveClass}`).length === 0) {\n        return undefined;\n      }\n      const el = swiper.el;\n      const swiperWidth = el.clientWidth;\n      const swiperHeight = el.clientHeight;\n      const windowWidth = window.innerWidth;\n      const windowHeight = window.innerHeight;\n      const swiperOffset = elementOffset(el);\n      if (rtl) swiperOffset.left -= el.scrollLeft;\n      const swiperCoord = [[swiperOffset.left, swiperOffset.top], [swiperOffset.left + swiperWidth, swiperOffset.top], [swiperOffset.left, swiperOffset.top + swiperHeight], [swiperOffset.left + swiperWidth, swiperOffset.top + swiperHeight]];\n      for (let i = 0; i < swiperCoord.length; i += 1) {\n        const point = swiperCoord[i];\n        if (point[0] >= 0 && point[0] <= windowWidth && point[1] >= 0 && point[1] <= windowHeight) {\n          if (point[0] === 0 && point[1] === 0) continue; // eslint-disable-line\n          inView = true;\n        }\n      }\n      if (!inView) return undefined;\n    }\n    if (swiper.isHorizontal()) {\n      if (isPageUp || isPageDown || isArrowLeft || isArrowRight) {\n        if (e.preventDefault) e.preventDefault();else e.returnValue = false;\n      }\n      if ((isPageDown || isArrowRight) && !rtl || (isPageUp || isArrowLeft) && rtl) swiper.slideNext();\n      if ((isPageUp || isArrowLeft) && !rtl || (isPageDown || isArrowRight) && rtl) swiper.slidePrev();\n    } else {\n      if (isPageUp || isPageDown || isArrowUp || isArrowDown) {\n        if (e.preventDefault) e.preventDefault();else e.returnValue = false;\n      }\n      if (isPageDown || isArrowDown) swiper.slideNext();\n      if (isPageUp || isArrowUp) swiper.slidePrev();\n    }\n    emit('keyPress', kc);\n    return undefined;\n  }\n  function enable() {\n    if (swiper.keyboard.enabled) return;\n    document.addEventListener('keydown', handle);\n    swiper.keyboard.enabled = true;\n  }\n  function disable() {\n    if (!swiper.keyboard.enabled) return;\n    document.removeEventListener('keydown', handle);\n    swiper.keyboard.enabled = false;\n  }\n  on('init', () => {\n    if (swiper.params.keyboard.enabled) {\n      enable();\n    }\n  });\n  on('destroy', () => {\n    if (swiper.keyboard.enabled) {\n      disable();\n    }\n  });\n  Object.assign(swiper.keyboard, {\n    enable,\n    disable\n  });\n}\n\nexport { Keyboard as default };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,SAAS,QAAQ,8BAA8B;AAC/E,SAASC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,aAAa,QAAQ,qBAAqB;;AAE7E;AACA,SAASC,QAAQA,CAACC,IAAI,EAAE;EACtB,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC,EAAE;IACFC;EACF,CAAC,GAAGJ,IAAI;EACR,MAAMK,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAMc,MAAM,GAAGZ,SAAS,CAAC,CAAC;EAC1BO,MAAM,CAACM,QAAQ,GAAG;IAChBC,OAAO,EAAE;EACX,CAAC;EACDN,YAAY,CAAC;IACXK,QAAQ,EAAE;MACRC,OAAO,EAAE,KAAK;MACdC,cAAc,EAAE,IAAI;MACpBC,UAAU,EAAE;IACd;EACF,CAAC,CAAC;EACF,SAASC,MAAMA,CAACC,KAAK,EAAE;IACrB,IAAI,CAACX,MAAM,CAACO,OAAO,EAAE;IACrB,MAAM;MACJK,YAAY,EAAEC;IAChB,CAAC,GAAGb,MAAM;IACV,IAAIc,CAAC,GAAGH,KAAK;IACb,IAAIG,CAAC,CAACC,aAAa,EAAED,CAAC,GAAGA,CAAC,CAACC,aAAa,CAAC,CAAC;IAC1C,MAAMC,EAAE,GAAGF,CAAC,CAACG,OAAO,IAAIH,CAAC,CAACI,QAAQ;IAClC,MAAMT,UAAU,GAAGT,MAAM,CAACmB,MAAM,CAACb,QAAQ,CAACG,UAAU;IACpD,MAAMW,QAAQ,GAAGX,UAAU,IAAIO,EAAE,KAAK,EAAE;IACxC,MAAMK,UAAU,GAAGZ,UAAU,IAAIO,EAAE,KAAK,EAAE;IAC1C,MAAMM,WAAW,GAAGN,EAAE,KAAK,EAAE;IAC7B,MAAMO,YAAY,GAAGP,EAAE,KAAK,EAAE;IAC9B,MAAMQ,SAAS,GAAGR,EAAE,KAAK,EAAE;IAC3B,MAAMS,WAAW,GAAGT,EAAE,KAAK,EAAE;IAC7B;IACA,IAAI,CAAChB,MAAM,CAAC0B,cAAc,KAAK1B,MAAM,CAAC2B,YAAY,CAAC,CAAC,IAAIJ,YAAY,IAAIvB,MAAM,CAAC4B,UAAU,CAAC,CAAC,IAAIH,WAAW,IAAIJ,UAAU,CAAC,EAAE;MACzH,OAAO,KAAK;IACd;IACA,IAAI,CAACrB,MAAM,CAAC6B,cAAc,KAAK7B,MAAM,CAAC2B,YAAY,CAAC,CAAC,IAAIL,WAAW,IAAItB,MAAM,CAAC4B,UAAU,CAAC,CAAC,IAAIJ,SAAS,IAAIJ,QAAQ,CAAC,EAAE;MACpH,OAAO,KAAK;IACd;IACA,IAAIN,CAAC,CAACgB,QAAQ,IAAIhB,CAAC,CAACiB,MAAM,IAAIjB,CAAC,CAACkB,OAAO,IAAIlB,CAAC,CAACmB,OAAO,EAAE;MACpD,OAAOC,SAAS;IAClB;IACA,IAAI9B,QAAQ,CAAC+B,aAAa,IAAI/B,QAAQ,CAAC+B,aAAa,CAACC,QAAQ,KAAKhC,QAAQ,CAAC+B,aAAa,CAACC,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,OAAO,IAAIjC,QAAQ,CAAC+B,aAAa,CAACC,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,UAAU,CAAC,EAAE;MAC5L,OAAOH,SAAS;IAClB;IACA,IAAIlC,MAAM,CAACmB,MAAM,CAACb,QAAQ,CAACE,cAAc,KAAKY,QAAQ,IAAIC,UAAU,IAAIC,WAAW,IAAIC,YAAY,IAAIC,SAAS,IAAIC,WAAW,CAAC,EAAE;MAChI,IAAIa,MAAM,GAAG,KAAK;MAClB;MACA,IAAI3C,cAAc,CAACK,MAAM,CAACuC,EAAE,EAAE,IAAIvC,MAAM,CAACmB,MAAM,CAACqB,UAAU,gBAAgB,CAAC,CAACC,MAAM,GAAG,CAAC,IAAI9C,cAAc,CAACK,MAAM,CAACuC,EAAE,EAAE,IAAIvC,MAAM,CAACmB,MAAM,CAACuB,gBAAgB,EAAE,CAAC,CAACD,MAAM,KAAK,CAAC,EAAE;QACtK,OAAOP,SAAS;MAClB;MACA,MAAMK,EAAE,GAAGvC,MAAM,CAACuC,EAAE;MACpB,MAAMI,WAAW,GAAGJ,EAAE,CAACK,WAAW;MAClC,MAAMC,YAAY,GAAGN,EAAE,CAACO,YAAY;MACpC,MAAMC,WAAW,GAAG1C,MAAM,CAAC2C,UAAU;MACrC,MAAMC,YAAY,GAAG5C,MAAM,CAAC6C,WAAW;MACvC,MAAMC,YAAY,GAAGtD,aAAa,CAAC0C,EAAE,CAAC;MACtC,IAAI1B,GAAG,EAAEsC,YAAY,CAACC,IAAI,IAAIb,EAAE,CAACc,UAAU;MAC3C,MAAMC,WAAW,GAAG,CAAC,CAACH,YAAY,CAACC,IAAI,EAAED,YAAY,CAACI,GAAG,CAAC,EAAE,CAACJ,YAAY,CAACC,IAAI,GAAGT,WAAW,EAAEQ,YAAY,CAACI,GAAG,CAAC,EAAE,CAACJ,YAAY,CAACC,IAAI,EAAED,YAAY,CAACI,GAAG,GAAGV,YAAY,CAAC,EAAE,CAACM,YAAY,CAACC,IAAI,GAAGT,WAAW,EAAEQ,YAAY,CAACI,GAAG,GAAGV,YAAY,CAAC,CAAC;MAC1O,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,WAAW,CAACb,MAAM,EAAEe,CAAC,IAAI,CAAC,EAAE;QAC9C,MAAMC,KAAK,GAAGH,WAAW,CAACE,CAAC,CAAC;QAC5B,IAAIC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIV,WAAW,IAAIU,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIR,YAAY,EAAE;UACzF,IAAIQ,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,SAAS,CAAC;UAChDnB,MAAM,GAAG,IAAI;QACf;MACF;MACA,IAAI,CAACA,MAAM,EAAE,OAAOJ,SAAS;IAC/B;IACA,IAAIlC,MAAM,CAAC2B,YAAY,CAAC,CAAC,EAAE;MACzB,IAAIP,QAAQ,IAAIC,UAAU,IAAIC,WAAW,IAAIC,YAAY,EAAE;QACzD,IAAIT,CAAC,CAAC4C,cAAc,EAAE5C,CAAC,CAAC4C,cAAc,CAAC,CAAC,CAAC,KAAK5C,CAAC,CAAC6C,WAAW,GAAG,KAAK;MACrE;MACA,IAAI,CAACtC,UAAU,IAAIE,YAAY,KAAK,CAACV,GAAG,IAAI,CAACO,QAAQ,IAAIE,WAAW,KAAKT,GAAG,EAAEb,MAAM,CAAC4D,SAAS,CAAC,CAAC;MAChG,IAAI,CAACxC,QAAQ,IAAIE,WAAW,KAAK,CAACT,GAAG,IAAI,CAACQ,UAAU,IAAIE,YAAY,KAAKV,GAAG,EAAEb,MAAM,CAAC6D,SAAS,CAAC,CAAC;IAClG,CAAC,MAAM;MACL,IAAIzC,QAAQ,IAAIC,UAAU,IAAIG,SAAS,IAAIC,WAAW,EAAE;QACtD,IAAIX,CAAC,CAAC4C,cAAc,EAAE5C,CAAC,CAAC4C,cAAc,CAAC,CAAC,CAAC,KAAK5C,CAAC,CAAC6C,WAAW,GAAG,KAAK;MACrE;MACA,IAAItC,UAAU,IAAII,WAAW,EAAEzB,MAAM,CAAC4D,SAAS,CAAC,CAAC;MACjD,IAAIxC,QAAQ,IAAII,SAAS,EAAExB,MAAM,CAAC6D,SAAS,CAAC,CAAC;IAC/C;IACA1D,IAAI,CAAC,UAAU,EAAEa,EAAE,CAAC;IACpB,OAAOkB,SAAS;EAClB;EACA,SAAS4B,MAAMA,CAAA,EAAG;IAChB,IAAI9D,MAAM,CAACM,QAAQ,CAACC,OAAO,EAAE;IAC7BH,QAAQ,CAAC2D,gBAAgB,CAAC,SAAS,EAAErD,MAAM,CAAC;IAC5CV,MAAM,CAACM,QAAQ,CAACC,OAAO,GAAG,IAAI;EAChC;EACA,SAASyD,OAAOA,CAAA,EAAG;IACjB,IAAI,CAAChE,MAAM,CAACM,QAAQ,CAACC,OAAO,EAAE;IAC9BH,QAAQ,CAAC6D,mBAAmB,CAAC,SAAS,EAAEvD,MAAM,CAAC;IAC/CV,MAAM,CAACM,QAAQ,CAACC,OAAO,GAAG,KAAK;EACjC;EACAL,EAAE,CAAC,MAAM,EAAE,MAAM;IACf,IAAIF,MAAM,CAACmB,MAAM,CAACb,QAAQ,CAACC,OAAO,EAAE;MAClCuD,MAAM,CAAC,CAAC;IACV;EACF,CAAC,CAAC;EACF5D,EAAE,CAAC,SAAS,EAAE,MAAM;IAClB,IAAIF,MAAM,CAACM,QAAQ,CAACC,OAAO,EAAE;MAC3ByD,OAAO,CAAC,CAAC;IACX;EACF,CAAC,CAAC;EACFE,MAAM,CAACC,MAAM,CAACnE,MAAM,CAACM,QAAQ,EAAE;IAC7BwD,MAAM;IACNE;EACF,CAAC,CAAC;AACJ;AAEA,SAASlE,QAAQ,IAAIsE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}