import React, { useState, useEffect } from 'react';
import axios from '../utils/axios';
import { toast } from 'react-toastify';
import '../styles/dashboard.css';

function Dashboard() {
  const [stats, setStats] = useState({
    books: { total: 0, loading: true },
    orders: { total: 0, loading: true },
    users: { total: 0, loading: true },
    revenue: { total: 0, loading: true }
  });
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch dashboard stats using Promise.all to fetch all stats in parallel
        const [booksResponse, ordersResponse, usersResponse] = await Promise.all([
          axios.get('/books'),
          axios.get('/orders'),
          axios.get('/users')
        ]);

        console.log('Books Response:', booksResponse.data);
        console.log('Orders Response:', ordersResponse.data);
        console.log('Users Response:', usersResponse.data);

        // Extract data and calculate totals
        const booksTotal = booksResponse.data?.data?.meta?.total || 
                          booksResponse.data?.meta?.total || 
                          booksResponse.data?.data?.length || 0;

        const ordersTotal = ordersResponse.data?.data?.meta?.total || 
                           ordersResponse.data?.meta?.total || 
                           ordersResponse.data?.data?.length || 0;

        const usersTotal = usersResponse.data?.data?.total || 
                          usersResponse.data?.total || 
                          (usersResponse.data?.data?.data || []).length || 0;

        // Calculate total revenue from orders
        const orders = ordersResponse.data?.data?.data || 
                      ordersResponse.data?.data || [];
        const totalRevenue = orders.reduce((sum, order) => {
          const amount = parseFloat(order.total_amount || order.TotalAmount || 0);
          return sum + (isNaN(amount) ? 0 : amount);
        }, 0);

        console.log('Calculated Totals:', {
          books: booksTotal,
          orders: ordersTotal,
          users: usersTotal,
          revenue: totalRevenue
        });

        // Update stats
        setStats({
          books: { 
            total: booksTotal,
            loading: false 
          },
          orders: { 
            total: ordersTotal,
            loading: false 
          },
          users: { 
            total: usersTotal,
            loading: false 
          },
          revenue: { 
            total: totalRevenue,
            loading: false 
          }
        });

        // Create recent activities from the latest data
        const recentActivities = [
          ...orders.slice(0, 3).map((order, index) => ({
            id: `order-${order.id || order.OrderID || `temp-${Date.now()}-${index}`}`,
            type: 'order',
            message: `New order #${order.id || order.OrderID || 'Unknown'} received`,
            created_at: order.created_at || order.CreatedAt || new Date().toISOString()
          })),
          ...(usersResponse.data?.data?.data || []).slice(0, 3).map((user, index) => ({
            id: `user-${user.UserID || user.id || `temp-${Date.now()}-${index + 3}`}`,
            type: 'user',
            message: `New user ${user.FirstName || user.LastName ? `${user.FirstName || ''} ${user.LastName || ''}`.trim() : user.Email || 'Unknown'} registered`,
            created_at: user.CreatedAt || user.created_at || new Date().toISOString()
          }))
        ]
        .filter(activity => activity.id && activity.message)
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, 5);

        console.log('Recent Activities:', recentActivities);
        
        setActivities(recentActivities.map(activity => ({
          ...activity,
          icon: getActivityIcon(activity.type)
        })));

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        toast.error('Failed to fetch dashboard data. Please try again later.');
        
        // Reset stats on error
        setStats({
          books: { total: 0, loading: false },
          orders: { total: 0, loading: false },
          users: { total: 0, loading: false },
          revenue: { total: 0, loading: false }
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const getActivityIcon = (type) => {
    switch (type) {
      case 'order':
        return 'ri-shopping-cart-2-line';
      case 'user':
        return 'ri-user-2-line';
      case 'book':
        return 'ri-book-2-line';
      default:
        return 'ri-notification-2-line';
    }
  };

  const formatTimeAgo = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const seconds = Math.floor((now - date) / 1000);

    if (seconds < 60) return 'just now';
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    const days = Math.floor(hours / 24);
    return `${days} day${days > 1 ? 's' : ''} ago`;
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <div className="dashboard-container">
      {/* Stats Grid */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-info">
            <h3>Total Books</h3>
            {stats.books.loading ? (
              <div className="spinner-border spinner-border-sm" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            ) : (
              <p className="value">{stats.books.total}</p>
            )}
          </div>
          <div className="stat-icon books">
            <i className="ri-book-2-line"></i>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-info">
            <h3>Orders</h3>
            {stats.orders.loading ? (
              <div className="spinner-border spinner-border-sm" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            ) : (
              <p className="value">{stats.orders.total}</p>
            )}
          </div>
          <div className="stat-icon orders">
            <i className="ri-shopping-cart-2-line"></i>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-info">
            <h3>Users</h3>
            {stats.users.loading ? (
              <div className="spinner-border spinner-border-sm" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            ) : (
              <p className="value">{stats.users.total}</p>
            )}
          </div>
          <div className="stat-icon users">
            <i className="ri-user-2-line"></i>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-info">
            <h3>Revenue</h3>
            {stats.revenue.loading ? (
              <div className="spinner-border spinner-border-sm" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            ) : (
              <p className="value">{formatCurrency(stats.revenue.total)}</p>
            )}
          </div>
          <div className="stat-icon revenue">
            <i className="ri-money-dollar-circle-line"></i>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="recent-activity">
        <h2>Recent Activity</h2>
        <div className="activity-list">
          {loading ? (
            <div className="text-center py-4">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          ) : activities.length === 0 ? (
            <div className="text-center py-4">No recent activities</div>
          ) : (
            activities.map((activity) => (
              <div key={activity.id} className="activity-item">
                <div className="activity-icon">
                  <i className={activity.icon}></i>
                </div>
                <div className="activity-content">
                  <h3>{activity.message}</h3>
                  <p>{formatTimeAgo(activity.created_at)}</p>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}

export default Dashboard; 