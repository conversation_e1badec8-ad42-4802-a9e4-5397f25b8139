{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\LaravelReact\\\\frontend-ui\\\\src\\\\components\\\\SelectedCategory.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { fetchProducts } from \"../utilis/fetchProducts\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SelectedCategory = () => {\n  _s();\n  const [categories, setCategories] = useState([]);\n  useEffect(() => {\n    const getCategories = async () => {\n      try {\n        const products = await fetchProducts();\n\n        // Extract categories from products and get unique values\n        const uniqueCategories = [\"All Categories\", ...new Set(products.map(product => product.category))];\n        setCategories(uniqueCategories);\n      } catch (error) {\n        console.error(\"Error fetching categories:\", error);\n      }\n    };\n    getCategories();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"select\", {\n    children: categories.map((category, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n      value: category,\n      children: category\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n_s(SelectedCategory, \"+ijgB8ROEl0Dkz53OTIi8GynN6s=\");\n_c = SelectedCategory;\nexport default SelectedCategory;\nvar _c;\n$RefreshReg$(_c, \"SelectedCategory\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "fetchProducts", "jsxDEV", "_jsxDEV", "SelectedCategory", "_s", "categories", "setCategories", "getCategories", "products", "uniqueCategories", "Set", "map", "product", "category", "error", "console", "children", "index", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/components/SelectedCategory.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { fetchProducts } from \"../utilis/fetchProducts\";\n\nconst SelectedCategory = () => {\n  const [categories, setCategories] = useState([]);\n\n  useEffect(() => {\n    const getCategories = async () => {\n      try {\n        const products = await fetchProducts();\n\n        // Extract categories from products and get unique values\n        const uniqueCategories = [\n          \"All Categories\",\n          ...new Set(products.map((product) => product.category)),\n        ];\n\n        setCategories(uniqueCategories);\n      } catch (error) {\n        console.error(\"Error fetching categories:\", error);\n      }\n    };\n\n    getCategories();\n  }, []);\n\n  return (\n    <select>\n      {categories.map((category, index) => (\n        <option key={index} value={category}>\n          {category}\n        </option>\n      ))}\n    </select>\n  );\n};\n\nexport default SelectedCategory;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACd,MAAMQ,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMR,aAAa,CAAC,CAAC;;QAEtC;QACA,MAAMS,gBAAgB,GAAG,CACvB,gBAAgB,EAChB,GAAG,IAAIC,GAAG,CAACF,QAAQ,CAACG,GAAG,CAAEC,OAAO,IAAKA,OAAO,CAACC,QAAQ,CAAC,CAAC,CACxD;QAEDP,aAAa,CAACG,gBAAgB,CAAC;MACjC,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD;IACF,CAAC;IAEDP,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEL,OAAA;IAAAc,QAAA,EACGX,UAAU,CAACM,GAAG,CAAC,CAACE,QAAQ,EAAEI,KAAK,kBAC9Bf,OAAA;MAAoBgB,KAAK,EAAEL,QAAS;MAAAG,QAAA,EACjCH;IAAQ,GADEI,KAAK;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEV,CACT;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEb,CAAC;AAAClB,EAAA,CAhCID,gBAAgB;AAAAoB,EAAA,GAAhBpB,gBAAgB;AAkCtB,eAAeA,gBAAgB;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}