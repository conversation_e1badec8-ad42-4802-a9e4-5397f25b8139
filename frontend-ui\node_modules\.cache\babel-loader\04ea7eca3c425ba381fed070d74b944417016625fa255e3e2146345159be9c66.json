{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\LaravelReact\\\\frontend-ui\\\\src\\\\blog\\\\Blog.jsx\";\nimport React from \"react\";\nimport PageHeader from \"../components/PageHeader\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Blog = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(PageHeader, {\n      title: \"Blog Page\",\n      curPage: \"Blog\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), \" \"]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Blog;\nexport default Blog;\nvar _c;\n$RefreshReg$(_c, \"Blog\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Blog", "children", "title", "curPage", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/blog/Blog.jsx"], "sourcesContent": ["import React from \"react\";\nimport PageHeader from \"../components/PageHeader\";\n\nconst Blog = () => {\n  return (\n    <div>\n      <PageHeader title=\"Blog Page\" curPage=\"Blog\" />{\" \"}\n      {/* Changed from \"Blogs\" to \"Blog\" */}\n    </div>\n  );\n};\n\nexport default Blog;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,oBACED,OAAA;IAAAE,QAAA,gBACEF,OAAA,CAACF,UAAU;MAACK,KAAK,EAAC,WAAW;MAACC,OAAO,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAAC,GAAG;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEhD,CAAC;AAEV,CAAC;AAACC,EAAA,GAPIR,IAAI;AASV,eAAeA,IAAI;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}