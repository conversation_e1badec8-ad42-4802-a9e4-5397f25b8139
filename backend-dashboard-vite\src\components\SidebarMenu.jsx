import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import '../styles/sidebar.css';

function SidebarMenu() {
  const location = useLocation();

  const menuItems = [
    { path: '/', icon: 'ri-dashboard-line', label: 'Dashboard' },
    { path: '/orders', icon: 'ri-shopping-cart-line', label: 'Orders' },
    { path: '/books', icon: 'ri-book-line', label: 'Books' },
    { path: '/categories', icon: 'ri-folder-line', label: 'Categories' },
    { path: '/purchases', icon: 'ri-shopping-bag-line', label: 'Purchases' },
    { path: '/users', icon: 'ri-user-line', label: 'Users' },
    { path: '/pages', icon: 'ri-file-list-line', label: 'Pages' },
  ];

  return (
    <nav className="sidebar-nav">
      {menuItems.map((item) => (
        <Link
          key={item.path}
          to={item.path}
          className={`nav-link ${location.pathname === item.path ? 'active' : ''}`}
        >
          <i className={item.icon}></i>
          <span>{item.label}</span>
        </Link>
      ))}
    </nav>
  );
}

export default SidebarMenu; 