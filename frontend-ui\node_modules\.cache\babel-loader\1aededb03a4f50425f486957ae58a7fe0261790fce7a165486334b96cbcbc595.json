{"ast": null, "code": "import ownerDocument from 'dom-helpers/ownerDocument';\nimport canUseDOM from 'dom-helpers/canUseDOM';\nimport { useState, useEffect } from 'react';\nimport useWindow from './useWindow';\nexport const resolveContainerRef = (ref, document) => {\n  if (!canUseDOM) return null;\n  if (ref == null) return (document || ownerDocument()).body;\n  if (typeof ref === 'function') ref = ref();\n  if (ref && 'current' in ref) ref = ref.current;\n  if (ref && ('nodeType' in ref || ref.getBoundingClientRect)) return ref;\n  return null;\n};\nexport default function useWaitForDOMRef(ref, onResolved) {\n  const window = useWindow();\n  const [resolvedRef, setRef] = useState(() => resolveContainerRef(ref, window == null ? void 0 : window.document));\n  if (!resolvedRef) {\n    const earlyRef = resolveContainerRef(ref);\n    if (earlyRef) setRef(earlyRef);\n  }\n  useEffect(() => {\n    if (onResolved && resolvedRef) {\n      onResolved(resolvedRef);\n    }\n  }, [onResolved, resolvedRef]);\n  useEffect(() => {\n    const nextRef = resolveContainerRef(ref);\n    if (nextRef !== resolvedRef) {\n      setRef(nextRef);\n    }\n  }, [ref, resolvedRef]);\n  return resolvedRef;\n}", "map": {"version": 3, "names": ["ownerDocument", "canUseDOM", "useState", "useEffect", "useWindow", "resolveContainerRef", "ref", "document", "body", "current", "getBoundingClientRect", "useWaitForDOMRef", "onResolved", "window", "resolvedRef", "setRef", "earlyRef", "nextRef"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/@restart/ui/esm/useWaitForDOMRef.js"], "sourcesContent": ["import ownerDocument from 'dom-helpers/ownerDocument';\nimport canUseDOM from 'dom-helpers/canUseDOM';\nimport { useState, useEffect } from 'react';\nimport useWindow from './useWindow';\nexport const resolveContainerRef = (ref, document) => {\n  if (!canUseDOM) return null;\n  if (ref == null) return (document || ownerDocument()).body;\n  if (typeof ref === 'function') ref = ref();\n  if (ref && 'current' in ref) ref = ref.current;\n  if (ref && ('nodeType' in ref || ref.getBoundingClientRect)) return ref;\n  return null;\n};\nexport default function useWaitForDOMRef(ref, onResolved) {\n  const window = useWindow();\n  const [resolvedRef, setRef] = useState(() => resolveContainerRef(ref, window == null ? void 0 : window.document));\n  if (!resolvedRef) {\n    const earlyRef = resolveContainerRef(ref);\n    if (earlyRef) setRef(earlyRef);\n  }\n  useEffect(() => {\n    if (onResolved && resolvedRef) {\n      onResolved(resolvedRef);\n    }\n  }, [onResolved, resolvedRef]);\n  useEffect(() => {\n    const nextRef = resolveContainerRef(ref);\n    if (nextRef !== resolvedRef) {\n      setRef(nextRef);\n    }\n  }, [ref, resolvedRef]);\n  return resolvedRef;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,2BAA2B;AACrD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAO,MAAMC,mBAAmB,GAAGA,CAACC,GAAG,EAAEC,QAAQ,KAAK;EACpD,IAAI,CAACN,SAAS,EAAE,OAAO,IAAI;EAC3B,IAAIK,GAAG,IAAI,IAAI,EAAE,OAAO,CAACC,QAAQ,IAAIP,aAAa,CAAC,CAAC,EAAEQ,IAAI;EAC1D,IAAI,OAAOF,GAAG,KAAK,UAAU,EAAEA,GAAG,GAAGA,GAAG,CAAC,CAAC;EAC1C,IAAIA,GAAG,IAAI,SAAS,IAAIA,GAAG,EAAEA,GAAG,GAAGA,GAAG,CAACG,OAAO;EAC9C,IAAIH,GAAG,KAAK,UAAU,IAAIA,GAAG,IAAIA,GAAG,CAACI,qBAAqB,CAAC,EAAE,OAAOJ,GAAG;EACvE,OAAO,IAAI;AACb,CAAC;AACD,eAAe,SAASK,gBAAgBA,CAACL,GAAG,EAAEM,UAAU,EAAE;EACxD,MAAMC,MAAM,GAAGT,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACU,WAAW,EAAEC,MAAM,CAAC,GAAGb,QAAQ,CAAC,MAAMG,mBAAmB,CAACC,GAAG,EAAEO,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACN,QAAQ,CAAC,CAAC;EACjH,IAAI,CAACO,WAAW,EAAE;IAChB,MAAME,QAAQ,GAAGX,mBAAmB,CAACC,GAAG,CAAC;IACzC,IAAIU,QAAQ,EAAED,MAAM,CAACC,QAAQ,CAAC;EAChC;EACAb,SAAS,CAAC,MAAM;IACd,IAAIS,UAAU,IAAIE,WAAW,EAAE;MAC7BF,UAAU,CAACE,WAAW,CAAC;IACzB;EACF,CAAC,EAAE,CAACF,UAAU,EAAEE,WAAW,CAAC,CAAC;EAC7BX,SAAS,CAAC,MAAM;IACd,MAAMc,OAAO,GAAGZ,mBAAmB,CAACC,GAAG,CAAC;IACxC,IAAIW,OAAO,KAAKH,WAAW,EAAE;MAC3BC,MAAM,CAACE,OAAO,CAAC;IACjB;EACF,CAAC,EAAE,CAACX,GAAG,EAAEQ,WAAW,CAAC,CAAC;EACtB,OAAOA,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}