{"ast": null, "code": "/**\n * API Configuration\n *\n * This file contains API configuration values.\n * NOTE: This file should be added to .gitignore to avoid committing sensitive information\n */\n\n// API Base URL\nexport const API_BASE_URL = process.env.REACT_APP_API_URL || \"http://18.142.55.142/api\";\n\n// API Key - should be loaded from env variable in production\nexport const API_KEY = process.env.REACT_APP_API_KEY || \"3EaR78ULtCRLyykSeCENE7E3WStGHqKrFiSppycQwcNj2cLvolcknKemzjnO\";\n\n// Default request timeout in milliseconds\nexport const API_TIMEOUT = 10000;\n\n// Default headers for API requests\nexport const DEFAULT_HEADERS = {\n  \"Content-Type\": \"application/json\",\n  Accept: \"application/json\",\n  \"X-Requested-With\": \"XMLHttpRequest\",\n  \"X-API-Key\": API_KEY\n};\n\n/**\n * Get headers for API calls\n * This ensures API key is included in fetch/axios calls\n */\nexport const getApiHeaders = () => {\n  return {\n    \"X-API-Key\": API_KEY,\n    Accept: \"application/json\",\n    \"Content-Type\": \"application/json\"\n  };\n};\nexport default {\n  API_BASE_URL,\n  API_KEY,\n  API_TIMEOUT,\n  DEFAULT_HEADERS,\n  getApiHeaders\n};", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "API_KEY", "REACT_APP_API_KEY", "API_TIMEOUT", "DEFAULT_HEADERS", "Accept", "getApiHeaders"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/config/api.config.js"], "sourcesContent": ["/**\n * API Configuration\n *\n * This file contains API configuration values.\n * NOTE: This file should be added to .gitignore to avoid committing sensitive information\n */\n\n// API Base URL\nexport const API_BASE_URL =\n  process.env.REACT_APP_API_URL || \"http://18.142.55.142/api\";\n\n// API Key - should be loaded from env variable in production\nexport const API_KEY =\n  process.env.REACT_APP_API_KEY ||\n  \"3EaR78ULtCRLyykSeCENE7E3WStGHqKrFiSppycQwcNj2cLvolcknKemzjnO\";\n\n// Default request timeout in milliseconds\nexport const API_TIMEOUT = 10000;\n\n// Default headers for API requests\nexport const DEFAULT_HEADERS = {\n  \"Content-Type\": \"application/json\",\n  Accept: \"application/json\",\n  \"X-Requested-With\": \"XMLHttpRequest\",\n  \"X-API-Key\": API_KEY,\n};\n\n/**\n * Get headers for API calls\n * This ensures API key is included in fetch/axios calls\n */\nexport const getApiHeaders = () => {\n  return {\n    \"X-API-Key\": API_KEY,\n    Accept: \"application/json\",\n    \"Content-Type\": \"application/json\",\n  };\n};\n\nexport default {\n  API_BASE_URL,\n  API_KEY,\n  API_TIMEOUT,\n  DEFAULT_HEADERS,\n  getApiHeaders,\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,OAAO,MAAMA,YAAY,GACvBC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,0BAA0B;;AAE7D;AACA,OAAO,MAAMC,OAAO,GAClBH,OAAO,CAACC,GAAG,CAACG,iBAAiB,IAC7B,8DAA8D;;AAEhE;AACA,OAAO,MAAMC,WAAW,GAAG,KAAK;;AAEhC;AACA,OAAO,MAAMC,eAAe,GAAG;EAC7B,cAAc,EAAE,kBAAkB;EAClCC,MAAM,EAAE,kBAAkB;EAC1B,kBAAkB,EAAE,gBAAgB;EACpC,WAAW,EAAEJ;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMK,aAAa,GAAGA,CAAA,KAAM;EACjC,OAAO;IACL,WAAW,EAAEL,OAAO;IACpBI,MAAM,EAAE,kBAAkB;IAC1B,cAAc,EAAE;EAClB,CAAC;AACH,CAAC;AAED,eAAe;EACbR,YAAY;EACZI,OAAO;EACPE,WAAW;EACXC,eAAe;EACfE;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}