{"ast": null, "code": "import * as React from 'react';\nimport Button from './Button';\nimport usePlaceholder from './usePlaceholder';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst PlaceholderButton = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const placeholderProps = usePlaceholder(props);\n  return /*#__PURE__*/_jsx(Button, {\n    ...placeholderProps,\n    ref: ref,\n    disabled: true,\n    tabIndex: -1\n  });\n});\nPlaceholderButton.displayName = 'PlaceholderButton';\nexport default PlaceholderButton;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "usePlaceholder", "jsx", "_jsx", "Placeholder<PERSON><PERSON><PERSON>", "forwardRef", "props", "ref", "placeholderProps", "disabled", "tabIndex", "displayName"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/react-bootstrap/esm/PlaceholderButton.js"], "sourcesContent": ["import * as React from 'react';\nimport Button from './Button';\nimport usePlaceholder from './usePlaceholder';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst PlaceholderButton = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const placeholderProps = usePlaceholder(props);\n  return /*#__PURE__*/_jsx(Button, {\n    ...placeholderProps,\n    ref: ref,\n    disabled: true,\n    tabIndex: -1\n  });\n});\nPlaceholderButton.displayName = 'PlaceholderButton';\nexport default PlaceholderButton;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACtE,MAAMC,gBAAgB,GAAGP,cAAc,CAACK,KAAK,CAAC;EAC9C,OAAO,aAAaH,IAAI,CAACH,MAAM,EAAE;IAC/B,GAAGQ,gBAAgB;IACnBD,GAAG,EAAEA,GAAG;IACRE,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,CAAC;EACb,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,iBAAiB,CAACO,WAAW,GAAG,mBAAmB;AACnD,eAAeP,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}