{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\LaravelReact\\\\frontend-ui\\\\src\\\\components\\\\Loading.jsx\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Loading = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"loading-container d-flex align-items-center justify-content-center min-vh-100\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-grow text-primary\",\n        role: \"status\",\n        style: {\n          width: \"3rem\",\n          height: \"3rem\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n        className: \"mt-3 text-primary\",\n        children: \"Loading Product...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Loading;\nexport default Loading;\nvar _c;\n$RefreshReg$(_c, \"Loading\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Loading", "className", "children", "role", "style", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/components/Loading.jsx"], "sourcesContent": ["import React from \"react\";\n\nconst Loading = () => {\n  return (\n    <div className=\"loading-container d-flex align-items-center justify-content-center min-vh-100\">\n      <div className=\"text-center\">\n        <div\n          className=\"spinner-grow text-primary\"\n          role=\"status\"\n          style={{ width: \"3rem\", height: \"3rem\" }}\n        >\n          <span className=\"visually-hidden\">Loading...</span>\n        </div>\n        <h5 className=\"mt-3 text-primary\">Loading Product...</h5>\n      </div>\n    </div>\n  );\n};\n\nexport default Loading;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,OAAO,GAAGA,CAAA,KAAM;EACpB,oBACED,OAAA;IAAKE,SAAS,EAAC,+EAA+E;IAAAC,QAAA,eAC5FH,OAAA;MAAKE,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BH,OAAA;QACEE,SAAS,EAAC,2BAA2B;QACrCE,IAAI,EAAC,QAAQ;QACbC,KAAK,EAAE;UAAEC,KAAK,EAAE,MAAM;UAAEC,MAAM,EAAE;QAAO,CAAE;QAAAJ,QAAA,eAEzCH,OAAA;UAAME,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAU;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACNX,OAAA;QAAIE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAC;MAAkB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GAfIX,OAAO;AAiBb,eAAeA,OAAO;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}