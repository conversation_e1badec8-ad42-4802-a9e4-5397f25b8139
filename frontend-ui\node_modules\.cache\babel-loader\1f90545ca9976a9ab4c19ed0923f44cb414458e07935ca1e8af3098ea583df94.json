{"ast": null, "code": "import * as React from 'react';\nimport usePlaceholder from './usePlaceholder';\nimport PlaceholderButton from './PlaceholderButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Placeholder = /*#__PURE__*/React.forwardRef(({\n  as: Component = 'span',\n  ...props\n}, ref) => {\n  const placeholderProps = usePlaceholder(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...placeholderProps,\n    ref: ref\n  });\n});\nPlaceholder.displayName = 'Placeholder';\nexport default Object.assign(Placeholder, {\n  Button: PlaceholderButton\n});", "map": {"version": 3, "names": ["React", "usePlaceholder", "Placeholder<PERSON><PERSON><PERSON>", "jsx", "_jsx", "Placeholder", "forwardRef", "as", "Component", "props", "ref", "placeholderProps", "displayName", "Object", "assign", "<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/react-bootstrap/esm/Placeholder.js"], "sourcesContent": ["import * as React from 'react';\nimport usePlaceholder from './usePlaceholder';\nimport PlaceholderButton from './PlaceholderButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Placeholder = /*#__PURE__*/React.forwardRef(({\n  as: Component = 'span',\n  ...props\n}, ref) => {\n  const placeholderProps = usePlaceholder(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...placeholderProps,\n    ref: ref\n  });\n});\nPlaceholder.displayName = 'Placeholder';\nexport default Object.assign(Placeholder, {\n  Button: PlaceholderButton\n});"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,WAAW,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAC;EACjDC,EAAE,EAAEC,SAAS,GAAG,MAAM;EACtB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,gBAAgB,GAAGV,cAAc,CAACQ,KAAK,CAAC;EAC9C,OAAO,aAAaL,IAAI,CAACI,SAAS,EAAE;IAClC,GAAGG,gBAAgB;IACnBD,GAAG,EAAEA;EACP,CAAC,CAAC;AACJ,CAAC,CAAC;AACFL,WAAW,CAACO,WAAW,GAAG,aAAa;AACvC,eAAeC,MAAM,CAACC,MAAM,CAACT,WAAW,EAAE;EACxCU,MAAM,EAAEb;AACV,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}