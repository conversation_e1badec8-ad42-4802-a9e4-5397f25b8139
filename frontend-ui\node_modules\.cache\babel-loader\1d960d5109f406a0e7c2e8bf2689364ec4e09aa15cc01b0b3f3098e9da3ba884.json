{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\LaravelReact\\\\frontend-ui\\\\src\\\\index.js\";\nimport React from \"react\";\nimport ReactDom from \"react-dom/client\";\nimport App from \"./App.jsx\";\nimport \"./index.css\";\nimport \"swiper/css\";\n\n// bootstrap css\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport \"bootstrap/dist/js/bootstrap.min.js\";\n\n// fonts and icons\nimport \"././assets/css/icofont.min.css\";\nimport \"././assets/css/animate.css\";\nimport \"././assets/css/style.min.css\";\nimport { createBrowserRouter, RouterProvider } from \"react-router-dom\";\nimport Home from \"./home/<USER>\";\nimport Blog from \"./blog/Blog.jsx\";\nimport Shop from \"./shop/Shop.jsx\";\nimport SingleProduct from \"./shop/SingleProduct.jsx\";\nimport CartPage from \"./shop/CartPage.jsx\";\nimport OrderSuccess from \"./shop/OrderSuccess.jsx\";\nimport About from \"./about/About.jsx\";\nimport Contact from \"./contactPage/Contact.jsx\";\nimport PageRenderer from \"./components/PageRenderer\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst router = createBrowserRouter([{\n  path: \"/\",\n  element: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 14\n  }, this),\n  children: [{\n    path: \"/\",\n    element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 29\n    }, this)\n  }, {\n    path: \"/blog\",\n    element: /*#__PURE__*/_jsxDEV(Blog, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 33\n    }, this)\n  }, {\n    path: \"/shop\",\n    element: /*#__PURE__*/_jsxDEV(Shop, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 33\n    }, this)\n  }, {\n    path: \"shop/:id\",\n    element: /*#__PURE__*/_jsxDEV(SingleProduct, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 36\n    }, this)\n  }, {\n    path: \"/cart-page\",\n    element: /*#__PURE__*/_jsxDEV(CartPage, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 38\n    }, this)\n  }, {\n    path: \"/order-success\",\n    element: /*#__PURE__*/_jsxDEV(OrderSuccess, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 42\n    }, this)\n  }, {\n    path: \"/about\",\n    element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 34\n    }, this)\n  }, {\n    path: \"/contact\",\n    element: /*#__PURE__*/_jsxDEV(Contact, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 36\n    }, this)\n  }, {\n    path: \"/page/:slug\",\n    element: /*#__PURE__*/_jsxDEV(PageRenderer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 39\n    }, this)\n  }]\n}]);\nReactDom.createRoot(document.getElementById(\"root\")).render(/*#__PURE__*/_jsxDEV(RouterProvider, {\n  router: router\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 46,\n  columnNumber: 3\n}, this));", "map": {"version": 3, "names": ["React", "ReactDom", "App", "createBrowserRouter", "RouterProvider", "Home", "Blog", "Shop", "SingleProduct", "CartPage", "OrderSuccess", "About", "Contact", "<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "router", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "createRoot", "document", "getElementById", "render"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/index.js"], "sourcesContent": ["import React from \"react\";\nimport ReactDom from \"react-dom/client\";\nimport App from \"./App.jsx\";\nimport \"./index.css\";\nimport \"swiper/css\";\n\n// bootstrap css\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport \"bootstrap/dist/js/bootstrap.min.js\";\n\n// fonts and icons\nimport \"././assets/css/icofont.min.css\";\nimport \"././assets/css/animate.css\";\nimport \"././assets/css/style.min.css\";\n\nimport { createBrowserRouter, RouterProvider } from \"react-router-dom\";\nimport Home from \"./home/<USER>\";\nimport Blog from \"./blog/Blog.jsx\";\nimport Shop from \"./shop/Shop.jsx\";\nimport SingleProduct from \"./shop/SingleProduct.jsx\";\nimport CartPage from \"./shop/CartPage.jsx\";\nimport OrderSuccess from \"./shop/OrderSuccess.jsx\";\nimport About from \"./about/About.jsx\";\nimport Contact from \"./contactPage/Contact.jsx\";\nimport PageRenderer from \"./components/PageRenderer\";\n\nconst router = createBrowserRouter([\n  {\n    path: \"/\",\n    element: <App />,\n    children: [\n      { path: \"/\", element: <Home /> },\n      { path: \"/blog\", element: <Blog /> },\n      { path: \"/shop\", element: <Shop /> },\n      { path: \"shop/:id\", element: <SingleProduct /> },\n      { path: \"/cart-page\", element: <CartPage /> },\n      { path: \"/order-success\", element: <OrderSuccess /> },\n      { path: \"/about\", element: <About /> },\n      { path: \"/contact\", element: <Contact /> },\n      { path: \"/page/:slug\", element: <PageRenderer /> },\n    ],\n  },\n]);\n\nReactDom.createRoot(document.getElementById(\"root\")).render(\n  <RouterProvider router={router} />\n);\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAO,aAAa;AACpB,OAAO,YAAY;;AAEnB;AACA,OAAO,sCAAsC;AAC7C,OAAO,oCAAoC;;AAE3C;AACA,OAAO,gCAAgC;AACvC,OAAO,4BAA4B;AACnC,OAAO,8BAA8B;AAErC,SAASC,mBAAmB,EAAEC,cAAc,QAAQ,kBAAkB;AACtE,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,OAAO,MAAM,2BAA2B;AAC/C,OAAOC,YAAY,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,MAAM,GAAGb,mBAAmB,CAAC,CACjC;EACEc,IAAI,EAAE,GAAG;EACTC,OAAO,eAAEH,OAAA,CAACb,GAAG;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAChBC,QAAQ,EAAE,CACR;IAAEN,IAAI,EAAE,GAAG;IAAEC,OAAO,eAAEH,OAAA,CAACV,IAAI;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAChC;IAAEL,IAAI,EAAE,OAAO;IAAEC,OAAO,eAAEH,OAAA,CAACT,IAAI;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACpC;IAAEL,IAAI,EAAE,OAAO;IAAEC,OAAO,eAAEH,OAAA,CAACR,IAAI;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACpC;IAAEL,IAAI,EAAE,UAAU;IAAEC,OAAO,eAAEH,OAAA,CAACP,aAAa;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAChD;IAAEL,IAAI,EAAE,YAAY;IAAEC,OAAO,eAAEH,OAAA,CAACN,QAAQ;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC7C;IAAEL,IAAI,EAAE,gBAAgB;IAAEC,OAAO,eAAEH,OAAA,CAACL,YAAY;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACrD;IAAEL,IAAI,EAAE,QAAQ;IAAEC,OAAO,eAAEH,OAAA,CAACJ,KAAK;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACtC;IAAEL,IAAI,EAAE,UAAU;IAAEC,OAAO,eAAEH,OAAA,CAACH,OAAO;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC1C;IAAEL,IAAI,EAAE,aAAa;IAAEC,OAAO,eAAEH,OAAA,CAACF,YAAY;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;AAEtD,CAAC,CACF,CAAC;AAEFrB,QAAQ,CAACuB,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC,CAACC,MAAM,cACzDZ,OAAA,CAACX,cAAc;EAACY,MAAM,EAAEA;AAAO;EAAAG,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAE,CACnC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}