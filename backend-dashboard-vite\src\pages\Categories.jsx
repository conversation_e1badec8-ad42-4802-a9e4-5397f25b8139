import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from '../utils/axios';
import { toast } from 'react-toastify';

function Categories() {
  const navigate = useNavigate();
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    perPage: 10,
    total: 0,
    lastPage: 1
  });

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/categories', {
          params: {
            page: pagination.currentPage,
            per_page: pagination.perPage
          }
        });

        // Log the response structure for debugging
        console.log('Categories API Response:', response.data);

        // Handle the API response structure
        let categoriesData = [];
        if (response.data?.success && Array.isArray(response.data.data)) {
          // Direct array in data field
          categoriesData = response.data.data;
        } else if (response.data?.data?.data && Array.isArray(response.data.data.data)) {
          // Nested data structure
          categoriesData = response.data.data.data;
        }

        // Transform the categories data
        const transformedCategories = categoriesData.map(category => ({
          id: category.CategoryID || category.id || Math.random().toString(36).substr(2, 9),
          name: category.Name || category.name || '-',
          description: category.Description || category.description || '-',
          books_count: parseInt(category.BooksCount || category.books_count || 0),
          created_at: category.CreatedAt || category.created_at || new Date().toISOString()
        }));

        console.log('Transformed Categories:', transformedCategories);
        setCategories(transformedCategories);

        // Update pagination
        const total = transformedCategories.length;
        setPagination(prev => ({
          ...prev,
          total,
          lastPage: Math.ceil(total / prev.perPage)
        }));
      } catch (error) {
        console.error('Error fetching categories:', error);
        toast.error('Failed to fetch categories. Please try again.');
        setCategories([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, [pagination.currentPage, pagination.perPage]);

  const handlePageChange = (page) => {
    setPagination(prev => ({
      ...prev,
      currentPage: page
    }));
  };

  // Handle edit category
  const handleEdit = (categoryId) => {
    navigate(`/categories/${categoryId}/edit`);
  };

  // Handle delete category
  const handleDelete = async (categoryId) => {
    if (window.confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      try {
        setLoading(true);
        const response = await axios.delete(`/categories/${categoryId}`);
        
        if (response.data?.success) {
          toast.success('Category deleted successfully');
          // Refresh the list
          const updatedResponse = await axios.get('/categories', {
            params: {
              page: pagination.currentPage,
              per_page: pagination.perPage
            }
          });

          let categoriesData = [];
          if (updatedResponse.data?.success && Array.isArray(updatedResponse.data.data)) {
            categoriesData = updatedResponse.data.data;
          } else if (updatedResponse.data?.data?.data && Array.isArray(updatedResponse.data.data.data)) {
            categoriesData = updatedResponse.data.data.data;
          }

          const transformedCategories = categoriesData.map(category => ({
            id: category.CategoryID || category.id || Math.random().toString(36).substr(2, 9),
            name: category.Name || category.name || '-',
            description: category.Description || category.description || '-',
            books_count: parseInt(category.BooksCount || category.books_count || 0),
            created_at: category.CreatedAt || category.created_at || new Date().toISOString()
          }));

          setCategories(transformedCategories);
          
          // Update pagination if needed
          const total = transformedCategories.length;
          const lastPage = Math.ceil(total / pagination.perPage);
          
          // If we're on a page that no longer exists, go to the last page
          if (pagination.currentPage > lastPage && lastPage > 0) {
            setPagination(prev => ({
              ...prev,
              currentPage: lastPage,
              total,
              lastPage
            }));
          } else {
            setPagination(prev => ({
              ...prev,
              total,
              lastPage
            }));
          }
        } else {
          toast.error(response.data?.message || 'Failed to delete category');
        }
      } catch (error) {
        console.error('Error deleting category:', error);
        const errorMessage = error.response?.data?.message 
          || error.response?.data?.error 
          || 'Failed to delete category. Please try again.';
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <div className="page-content">
      <div className="page-header mb-4">
        <h1>Categories</h1>
      </div>

      <div className="card">
        <div className="card-header d-flex justify-content-between align-items-center">
          <h5 className="card-title mb-0">Category List</h5>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/categories/new')}
          >
            <i className="ri-add-line me-1"></i>
            Add Category
          </button>
        </div>
        <div className="card-body">
          <div className="table-responsive">
            <table className="table table-hover">
              <thead className="bg-light">
                <tr>
                  <th>ID</th>
                  <th>Name</th>
                  <th>Description</th>
                  <th>Books</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="6" className="text-center py-4">
                      <div className="spinner-border text-primary" role="status">
                        <span className="visually-hidden">Loading...</span>
                      </div>
                    </td>
                  </tr>
                ) : categories.length === 0 ? (
                  <tr>
                    <td colSpan="6" className="text-center py-4">
                      No categories found
                    </td>
                  </tr>
                ) : (
                  categories.map((category) => (
                    <tr key={`category-${category.id}`}>
                      <td>#{category.id}</td>
                      <td>{category.name}</td>
                      <td>{category.description}</td>
                      <td>
                        <span className="badge bg-primary">
                          {category.books_count}
                        </span>
                      </td>
                      <td>
                        {new Date(category.created_at).toLocaleDateString()}
                      </td>
                      <td>
                        <button 
                          className="btn btn-sm btn-light me-2" 
                          title="Edit"
                          onClick={() => handleEdit(category.id)}
                        >
                          <i className="ri-edit-line"></i>
                        </button>
                        <button 
                          className="btn btn-sm btn-light" 
                          title="Delete"
                          onClick={() => handleDelete(category.id)}
                        >
                          <i className="ri-delete-bin-line"></i>
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {!loading && categories.length > 0 && (
            <div className="d-flex justify-content-between align-items-center mt-4">
              <div>
                Showing {((pagination.currentPage - 1) * pagination.perPage) + 1} to {Math.min(pagination.currentPage * pagination.perPage, pagination.total)} of {pagination.total} entries
              </div>
              <div className="d-flex gap-2">
                <button
                  className="btn btn-light"
                  onClick={() => handlePageChange(pagination.currentPage - 1)}
                  disabled={pagination.currentPage === 1}
                >
                  Previous
                </button>
                {Array.from({ length: pagination.lastPage }, (_, i) => i + 1)
                  .filter(page => {
                    const current = pagination.currentPage;
                    return page === 1 || 
                           page === pagination.lastPage || 
                           (page >= current - 1 && page <= current + 1);
                  })
                  .map((page, index, array) => (
                    <React.Fragment key={page}>
                      {index > 0 && array[index - 1] !== page - 1 && (
                        <span className="btn btn-light disabled">...</span>
                      )}
                      <button
                        className={`btn ${pagination.currentPage === page ? 'btn-primary' : 'btn-light'}`}
                        onClick={() => handlePageChange(page)}
                      >
                        {page}
                      </button>
                    </React.Fragment>
                  ))}
                <button
                  className="btn btn-light"
                  onClick={() => handlePageChange(pagination.currentPage + 1)}
                  disabled={pagination.currentPage === pagination.lastPage}
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Categories; 