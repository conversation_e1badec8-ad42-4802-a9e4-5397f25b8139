import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import axios from '../../utils/axios';
import { toast } from 'react-toastify';

function CategoryForm() {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    Name: '',
    Description: '',
    Status: 'active'
  });
  const [errors, setErrors] = useState({});

  // Fetch category data if editing
  useEffect(() => {
    const fetchCategory = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const response = await axios.get(`/categories/${id}`);
        
        if (response.data?.success) {
          const category = response.data.data;
          setFormData({
            Name: category.Name || category.name || '',
            Description: category.Description || category.description || '',
            Status: category.Status || category.status || 'active'
          });
        } else {
          toast.error('Failed to fetch category details');
          navigate('/categories');
        }
      } catch (error) {
        console.error('Error fetching category:', error);
        toast.error('Failed to fetch category details');
        navigate('/categories');
      } finally {
        setLoading(false);
      }
    };

    fetchCategory();
  }, [id, navigate]);

  // Form validation
  const validateForm = () => {
    const newErrors = {};

    if (!formData.Name.trim()) {
      newErrors.Name = "Category name is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      // Prepare data
      const processedData = {
        Name: formData.Name.trim(),
        Description: formData.Description.trim() || null,
        Status: formData.Status
      };

      console.log('Submitting category data:', processedData);

      // Submit to API
      const response = id
        ? await axios.put(`/categories/${id}`, processedData)
        : await axios.post('/categories', processedData);

      console.log('API Response:', response.data);

      if (response.data.success) {
        toast.success(id ? 'Category updated successfully' : 'Category created successfully');
        navigate('/categories');
      } else {
        if (response.data.errors) {
          const errorMessages = Object.values(response.data.errors).flat();
          errorMessages.forEach(message => toast.error(message));
        } else {
          toast.error(response.data.message || `Failed to ${id ? 'update' : 'create'} category`);
        }
      }
    } catch (error) {
      console.error('Error saving category:', error);
      
      if (error.response?.status === 422 && error.response?.data?.errors) {
        const validationErrors = error.response.data.errors;
        Object.values(validationErrors).flat().forEach(message => {
          toast.error(message);
        });
      } else {
        const errorMessage = error.response?.data?.message 
          || error.response?.data?.error 
          || `Failed to ${id ? 'update' : 'create'} category. Please try again.`;
        toast.error(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  if (loading && id) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      <div className="row">
        <div className="col-12">
          <div className="card">
            <div className="card-header">
              <h5 className="card-title mb-0">{id ? 'Edit Category' : 'Add New Category'}</h5>
            </div>
            <div className="card-body">
              <form onSubmit={handleSubmit}>
                <div className="mb-3">
                  <label className="form-label">Category Name</label>
                  <input
                    type="text"
                    className={`form-control ${errors.Name ? 'is-invalid' : ''}`}
                    name="Name"
                    value={formData.Name}
                    onChange={(e) => setFormData(prev => ({ ...prev, Name: e.target.value }))}
                    required
                  />
                  {errors.Name && <div className="invalid-feedback">{errors.Name}</div>}
                </div>

                <div className="mb-3">
                  <label className="form-label">Description</label>
                  <textarea
                    className="form-control"
                    name="Description"
                    rows="3"
                    value={formData.Description}
                    onChange={(e) => setFormData(prev => ({ ...prev, Description: e.target.value }))}
                  ></textarea>
                </div>

                <div className="mb-3">
                  <label className="form-label">Status</label>
                  <select
                    className="form-select"
                    name="Status"
                    value={formData.Status}
                    onChange={(e) => setFormData(prev => ({ ...prev, Status: e.target.value }))}
                  >
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                </div>

                <div className="text-end">
                  <button
                    type="button"
                    className="btn btn-secondary me-2"
                    onClick={() => navigate('/categories')}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                        {id ? 'Updating...' : 'Saving...'}
                      </>
                    ) : (
                      id ? 'Update Category' : 'Save Category'
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CategoryForm; 