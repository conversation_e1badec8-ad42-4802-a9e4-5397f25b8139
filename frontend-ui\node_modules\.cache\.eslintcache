[{"C:\\Users\\<USER>\\source\\frontend-ui\\src\\index.js": "1", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\App.jsx": "2", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\home\\Home.jsx": "3", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\blog\\Blog.jsx": "4", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\Shop.jsx": "5", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\SingleProduct.jsx": "6", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\CartPage.jsx": "7", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\about\\About.jsx": "8", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\contactPage\\Contact.jsx": "9", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\components\\NavItems.jsx": "10", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\components\\Footer.jsx": "11", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\components\\PageRenderer\\index.jsx": "12", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\home\\Register.jsx": "13", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\home\\HomeCategory.jsx": "14", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\home\\Banner.jsx": "15", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\utilis\\fetchProducts.js": "16", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\components\\PageHeader.jsx": "17", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\ProductCards.jsx": "18", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\utilis\\apiService.js": "19", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\ShopCategory.jsx": "20", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\Pagination.jsx": "21", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\Search.jsx": "22", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\LoadingSkeleton.jsx": "23", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\components\\Loading.jsx": "24", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\ProductDisplay.jsx": "25", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\CheckOutPage.jsx": "26", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\components\\GoogleMap.jsx": "27", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\utilis\\scrollToTop.js": "28", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\components\\DynamicPage.jsx": "29", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\components\\SelectedCategory.jsx": "30", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\config\\api.config.js": "31", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\components\\PuckRenderer.jsx": "32", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\hooks\\useCart.js": "33", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\utilis\\cartUtils.js": "34", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\utilis\\shippingUtils.js": "35", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\OrderSuccess.jsx": "36", "C:\\Users\\<USER>\\source\\frontend-ui\\src\\components\\CartToast.jsx": "37", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\index.js": "38", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\App.jsx": "39", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\Shop.jsx": "40", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\blog\\Blog.jsx": "41", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\CartPage.jsx": "42", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\SingleProduct.jsx": "43", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\about\\About.jsx": "44", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\OrderSuccess.jsx": "45", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\contactPage\\Contact.jsx": "46", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\home\\Home.jsx": "47", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\components\\CartToast.jsx": "48", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\components\\NavItems.jsx": "49", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\components\\Footer.jsx": "50", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\components\\PageRenderer\\index.jsx": "51", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\components\\PageHeader.jsx": "52", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\Pagination.jsx": "53", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\ProductCards.jsx": "54", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\ShopCategory.jsx": "55", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\Search.jsx": "56", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\LoadingSkeleton.jsx": "57", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\components\\Loading.jsx": "58", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\CheckOutPage.jsx": "59", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\utilis\\fetchProducts.js": "60", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\utilis\\apiService.js": "61", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\hooks\\useCart.js": "62", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\home\\Banner.jsx": "63", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\ProductDisplay.jsx": "64", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\home\\HomeCategory.jsx": "65", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\home\\Register.jsx": "66", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\components\\GoogleMap.jsx": "67", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\utilis\\scrollToTop.js": "68", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\components\\DynamicPage.jsx": "69", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\utilis\\cartUtils.js": "70", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\components\\SelectedCategory.jsx": "71", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\config\\api.config.js": "72", "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\components\\PuckRenderer.jsx": "73"}, {"size": 1525, "mtime": 1749183541233, "results": "74", "hashOfConfig": "75"}, {"size": 1605, "mtime": 1749184550610, "results": "76", "hashOfConfig": "75"}, {"size": 437, "mtime": 1748768243000, "results": "77", "hashOfConfig": "75"}, {"size": 266, "mtime": 1748768243000, "results": "78", "hashOfConfig": "75"}, {"size": 6656, "mtime": 1749179436412, "results": "79", "hashOfConfig": "75"}, {"size": 14538, "mtime": 1748768243000, "results": "80", "hashOfConfig": "75"}, {"size": 12700, "mtime": 1749184378287, "results": "81", "hashOfConfig": "75"}, {"size": 3087, "mtime": 1748768243000, "results": "82", "hashOfConfig": "75"}, {"size": 4237, "mtime": 1748768243000, "results": "83", "hashOfConfig": "75"}, {"size": 6268, "mtime": 1749184472604, "results": "84", "hashOfConfig": "75"}, {"size": 5597, "mtime": 1748768243000, "results": "85", "hashOfConfig": "75"}, {"size": 297, "mtime": 1748768243000, "results": "86", "hashOfConfig": "75"}, {"size": 1670, "mtime": 1748768243000, "results": "87", "hashOfConfig": "75"}, {"size": 5470, "mtime": 1749178891154, "results": "88", "hashOfConfig": "75"}, {"size": 2730, "mtime": 1748768243000, "results": "89", "hashOfConfig": "75"}, {"size": 2903, "mtime": 1748768243000, "results": "90", "hashOfConfig": "75"}, {"size": 2188, "mtime": 1748768243000, "results": "91", "hashOfConfig": "75"}, {"size": 5280, "mtime": 1749180913838, "results": "92", "hashOfConfig": "75"}, {"size": 4537, "mtime": 1748768243000, "results": "93", "hashOfConfig": "75"}, {"size": 735, "mtime": 1748768243000, "results": "94", "hashOfConfig": "75"}, {"size": 2178, "mtime": 1748768243000, "results": "95", "hashOfConfig": "75"}, {"size": 2641, "mtime": 1748768243000, "results": "96", "hashOfConfig": "75"}, {"size": 1471, "mtime": 1748768243000, "results": "97", "hashOfConfig": "75"}, {"size": 546, "mtime": 1748768243000, "results": "98", "hashOfConfig": "75"}, {"size": 4045, "mtime": 1748768243000, "results": "99", "hashOfConfig": "75"}, {"size": 31247, "mtime": 1749183995720, "results": "100", "hashOfConfig": "75"}, {"size": 539, "mtime": 1748768243000, "results": "101", "hashOfConfig": "75"}, {"size": 100, "mtime": 1748768243000, "results": "102", "hashOfConfig": "75"}, {"size": 2089, "mtime": 1748768243000, "results": "103", "hashOfConfig": "75"}, {"size": 918, "mtime": 1748768243000, "results": "104", "hashOfConfig": "75"}, {"size": 1097, "mtime": 1749173686000, "results": "105", "hashOfConfig": "75"}, {"size": 420, "mtime": 1748777991000, "results": "106", "hashOfConfig": "75"}, {"size": 7541, "mtime": 1749184925839, "results": "107", "hashOfConfig": "75"}, {"size": 3974, "mtime": 1749177449192, "results": "108", "hashOfConfig": "75"}, {"size": 5182, "mtime": 1749179331604, "results": "109", "hashOfConfig": "75"}, {"size": 13592, "mtime": 1749184007353, "results": "110", "hashOfConfig": "75"}, {"size": 2601, "mtime": 1749184489009, "results": "111", "hashOfConfig": "75"}, {"size": 1525, "mtime": 1749183541233, "results": "112", "hashOfConfig": "113"}, {"size": 1605, "mtime": 1749184550610, "results": "114", "hashOfConfig": "113"}, {"size": 6656, "mtime": 1749179436412, "results": "115", "hashOfConfig": "113"}, {"size": 266, "mtime": 1748768243000, "results": "116", "hashOfConfig": "113"}, {"size": 12700, "mtime": 1749184378287, "results": "117", "hashOfConfig": "113"}, {"size": 14538, "mtime": 1748768243000, "results": "118", "hashOfConfig": "113"}, {"size": 3087, "mtime": 1748768243000, "results": "119", "hashOfConfig": "113"}, {"size": 14111, "mtime": 1749971065156, "results": "120", "hashOfConfig": "113"}, {"size": 4237, "mtime": 1748768243000, "results": "121", "hashOfConfig": "113"}, {"size": 437, "mtime": 1748768243000, "results": "122", "hashOfConfig": "113"}, {"size": 2601, "mtime": 1749184489009, "results": "123", "hashOfConfig": "113"}, {"size": 6463, "mtime": 1749971105626, "results": "124", "hashOfConfig": "113"}, {"size": 5597, "mtime": 1748768243000, "results": "125", "hashOfConfig": "113"}, {"size": 297, "mtime": 1748768243000, "results": "126", "hashOfConfig": "113"}, {"size": 2188, "mtime": 1748768243000, "results": "127", "hashOfConfig": "113"}, {"size": 2178, "mtime": 1748768243000, "results": "128", "hashOfConfig": "113"}, {"size": 5280, "mtime": 1749180913838, "results": "129", "hashOfConfig": "113"}, {"size": 735, "mtime": 1748768243000, "results": "130", "hashOfConfig": "113"}, {"size": 2641, "mtime": 1748768243000, "results": "131", "hashOfConfig": "113"}, {"size": 1471, "mtime": 1748768243000, "results": "132", "hashOfConfig": "113"}, {"size": 546, "mtime": 1748768243000, "results": "133", "hashOfConfig": "113"}, {"size": 31247, "mtime": 1749183995720, "results": "134", "hashOfConfig": "113"}, {"size": 2903, "mtime": 1748768243000, "results": "135", "hashOfConfig": "113"}, {"size": 4537, "mtime": 1748768243000, "results": "136", "hashOfConfig": "113"}, {"size": 7541, "mtime": 1749184925839, "results": "137", "hashOfConfig": "113"}, {"size": 2730, "mtime": 1748768243000, "results": "138", "hashOfConfig": "113"}, {"size": 4045, "mtime": 1748768243000, "results": "139", "hashOfConfig": "113"}, {"size": 5470, "mtime": 1749178891154, "results": "140", "hashOfConfig": "113"}, {"size": 1670, "mtime": 1748768243000, "results": "141", "hashOfConfig": "113"}, {"size": 539, "mtime": 1748768243000, "results": "142", "hashOfConfig": "113"}, {"size": 100, "mtime": 1748768243000, "results": "143", "hashOfConfig": "113"}, {"size": 2089, "mtime": 1748768243000, "results": "144", "hashOfConfig": "113"}, {"size": 3974, "mtime": 1749177449192, "results": "145", "hashOfConfig": "113"}, {"size": 918, "mtime": 1748768243000, "results": "146", "hashOfConfig": "113"}, {"size": 1097, "mtime": 1749971267390, "results": "147", "hashOfConfig": "113"}, {"size": 420, "mtime": 1748777991000, "results": "148", "hashOfConfig": "113"}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1uilz35", {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ulidw8", {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\source\\frontend-ui\\src\\index.js", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\home\\Home.jsx", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\blog\\Blog.jsx", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\Shop.jsx", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\SingleProduct.jsx", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\CartPage.jsx", ["368", "369", "370", "371"], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\about\\About.jsx", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\contactPage\\Contact.jsx", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\components\\NavItems.jsx", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\components\\Footer.jsx", ["372", "373", "374", "375", "376"], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\components\\PageRenderer\\index.jsx", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\home\\Register.jsx", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\home\\HomeCategory.jsx", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\home\\Banner.jsx", ["377"], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\utilis\\fetchProducts.js", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\components\\PageHeader.jsx", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\ProductCards.jsx", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\utilis\\apiService.js", ["378", "379"], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\ShopCategory.jsx", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\Pagination.jsx", ["380", "381"], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\Search.jsx", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\LoadingSkeleton.jsx", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\components\\Loading.jsx", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\ProductDisplay.jsx", ["382"], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\CheckOutPage.jsx", ["383", "384", "385"], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\components\\GoogleMap.jsx", ["386"], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\utilis\\scrollToTop.js", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\components\\DynamicPage.jsx", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\components\\SelectedCategory.jsx", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\config\\api.config.js", ["387"], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\components\\PuckRenderer.jsx", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\hooks\\useCart.js", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\utilis\\cartUtils.js", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\utilis\\shippingUtils.js", [], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\shop\\OrderSuccess.jsx", ["388"], [], "C:\\Users\\<USER>\\source\\frontend-ui\\src\\components\\CartToast.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\index.js", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\Shop.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\blog\\Blog.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\CartPage.jsx", ["389", "390", "391", "392"], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\SingleProduct.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\about\\About.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\OrderSuccess.jsx", ["393"], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\contactPage\\Contact.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\home\\Home.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\components\\CartToast.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\components\\NavItems.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\components\\Footer.jsx", ["394", "395", "396", "397", "398"], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\components\\PageRenderer\\index.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\components\\PageHeader.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\Pagination.jsx", ["399", "400"], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\ProductCards.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\ShopCategory.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\Search.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\LoadingSkeleton.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\components\\Loading.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\CheckOutPage.jsx", ["401", "402", "403"], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\utilis\\fetchProducts.js", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\utilis\\apiService.js", ["404", "405"], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\hooks\\useCart.js", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\home\\Banner.jsx", ["406"], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\shop\\ProductDisplay.jsx", ["407"], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\home\\HomeCategory.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\home\\Register.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\components\\GoogleMap.jsx", ["408"], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\utilis\\scrollToTop.js", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\components\\DynamicPage.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\utilis\\cartUtils.js", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\components\\SelectedCategory.jsx", [], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\config\\api.config.js", ["409"], [], "C:\\Users\\<USER>\\source\\LaravelReact\\frontend-ui\\src\\components\\PuckRenderer.jsx", [], [], {"ruleId": "410", "severity": 1, "message": "411", "line": 16, "column": 5, "nodeType": "412", "messageId": "413", "endLine": 16, "endColumn": 14}, {"ruleId": "410", "severity": 1, "message": "414", "line": 17, "column": 5, "nodeType": "412", "messageId": "413", "endLine": 17, "endColumn": 14}, {"ruleId": "410", "severity": 1, "message": "415", "line": 27, "column": 10, "nodeType": "412", "messageId": "413", "endLine": 27, "endColumn": 22}, {"ruleId": "410", "severity": 1, "message": "416", "line": 88, "column": 9, "nodeType": "412", "messageId": "413", "endLine": 88, "endColumn": 33}, {"ruleId": "410", "severity": 1, "message": "417", "line": 10, "column": 7, "nodeType": "412", "messageId": "413", "endLine": 10, "endColumn": 17}, {"ruleId": "410", "severity": 1, "message": "418", "line": 85, "column": 7, "nodeType": "412", "messageId": "413", "endLine": 85, "endColumn": 16}, {"ruleId": "419", "severity": 1, "message": "420", "line": 90, "column": 22, "nodeType": "421", "endLine": 90, "endColumn": 34}, {"ruleId": "419", "severity": 1, "message": "420", "line": 99, "column": 22, "nodeType": "421", "endLine": 99, "endColumn": 34}, {"ruleId": "410", "severity": 1, "message": "422", "line": 106, "column": 7, "nodeType": "412", "messageId": "413", "endLine": 106, "endColumn": 23}, {"ruleId": "410", "severity": 1, "message": "423", "line": 13, "column": 7, "nodeType": "412", "messageId": "413", "endLine": 13, "endColumn": 17}, {"ruleId": "410", "severity": 1, "message": "424", "line": 1, "column": 33, "nodeType": "412", "messageId": "413", "endLine": 1, "endColumn": 46}, {"ruleId": "425", "severity": 1, "message": "426", "line": 159, "column": 1, "nodeType": "427", "endLine": 166, "endColumn": 3}, {"ruleId": "419", "severity": 1, "message": "420", "line": 32, "column": 11, "nodeType": "421", "endLine": 40, "endColumn": 12}, {"ruleId": "419", "severity": 1, "message": "420", "line": 61, "column": 11, "nodeType": "421", "endLine": 69, "endColumn": 12}, {"ruleId": "410", "severity": 1, "message": "428", "line": 5, "column": 36, "nodeType": "412", "messageId": "413", "endLine": 5, "endColumn": 41}, {"ruleId": "410", "severity": 1, "message": "429", "line": 31, "column": 9, "nodeType": "412", "messageId": "413", "endLine": 31, "endColumn": 13}, {"ruleId": "410", "severity": 1, "message": "430", "line": 235, "column": 9, "nodeType": "412", "messageId": "413", "endLine": 235, "endColumn": 27}, {"ruleId": "431", "severity": 1, "message": "432", "line": 400, "column": 6, "nodeType": "433", "endLine": 400, "endColumn": 42, "suggestions": "434"}, {"ruleId": "435", "severity": 1, "message": "436", "line": 7, "column": 17, "nodeType": "421", "endLine": 7, "endColumn": 325}, {"ruleId": "425", "severity": 1, "message": "426", "line": 40, "column": 1, "nodeType": "427", "endLine": 46, "endColumn": 3}, {"ruleId": "410", "severity": 1, "message": "437", "line": 66, "column": 5, "nodeType": "412", "messageId": "413", "endLine": 66, "endColumn": 12}, {"ruleId": "410", "severity": 1, "message": "411", "line": 16, "column": 5, "nodeType": "412", "messageId": "413", "endLine": 16, "endColumn": 14}, {"ruleId": "410", "severity": 1, "message": "414", "line": 17, "column": 5, "nodeType": "412", "messageId": "413", "endLine": 17, "endColumn": 14}, {"ruleId": "410", "severity": 1, "message": "415", "line": 27, "column": 10, "nodeType": "412", "messageId": "413", "endLine": 27, "endColumn": 22}, {"ruleId": "410", "severity": 1, "message": "416", "line": 88, "column": 9, "nodeType": "412", "messageId": "413", "endLine": 88, "endColumn": 33}, {"ruleId": "410", "severity": 1, "message": "437", "line": 66, "column": 5, "nodeType": "412", "messageId": "413", "endLine": 66, "endColumn": 12}, {"ruleId": "410", "severity": 1, "message": "417", "line": 10, "column": 7, "nodeType": "412", "messageId": "413", "endLine": 10, "endColumn": 17}, {"ruleId": "410", "severity": 1, "message": "418", "line": 85, "column": 7, "nodeType": "412", "messageId": "413", "endLine": 85, "endColumn": 16}, {"ruleId": "419", "severity": 1, "message": "420", "line": 90, "column": 22, "nodeType": "421", "endLine": 90, "endColumn": 34}, {"ruleId": "419", "severity": 1, "message": "420", "line": 99, "column": 22, "nodeType": "421", "endLine": 99, "endColumn": 34}, {"ruleId": "410", "severity": 1, "message": "422", "line": 106, "column": 7, "nodeType": "412", "messageId": "413", "endLine": 106, "endColumn": 23}, {"ruleId": "419", "severity": 1, "message": "420", "line": 32, "column": 11, "nodeType": "421", "endLine": 40, "endColumn": 12}, {"ruleId": "419", "severity": 1, "message": "420", "line": 61, "column": 11, "nodeType": "421", "endLine": 69, "endColumn": 12}, {"ruleId": "410", "severity": 1, "message": "429", "line": 31, "column": 9, "nodeType": "412", "messageId": "413", "endLine": 31, "endColumn": 13}, {"ruleId": "410", "severity": 1, "message": "430", "line": 235, "column": 9, "nodeType": "412", "messageId": "413", "endLine": 235, "endColumn": 27}, {"ruleId": "431", "severity": 1, "message": "432", "line": 400, "column": 6, "nodeType": "433", "endLine": 400, "endColumn": 42, "suggestions": "438"}, {"ruleId": "410", "severity": 1, "message": "424", "line": 1, "column": 33, "nodeType": "412", "messageId": "413", "endLine": 1, "endColumn": 46}, {"ruleId": "425", "severity": 1, "message": "426", "line": 159, "column": 1, "nodeType": "427", "endLine": 166, "endColumn": 3}, {"ruleId": "410", "severity": 1, "message": "423", "line": 13, "column": 7, "nodeType": "412", "messageId": "413", "endLine": 13, "endColumn": 17}, {"ruleId": "410", "severity": 1, "message": "428", "line": 5, "column": 36, "nodeType": "412", "messageId": "413", "endLine": 5, "endColumn": 41}, {"ruleId": "435", "severity": 1, "message": "436", "line": 7, "column": 17, "nodeType": "421", "endLine": 7, "endColumn": 325}, {"ruleId": "425", "severity": 1, "message": "426", "line": 40, "column": 1, "nodeType": "427", "endLine": 46, "endColumn": 3}, "no-unused-vars", "'cartCount' is assigned a value but never used.", "Identifier", "unusedVar", "'cartTotal' is assigned a value but never used.", "'shippingInfo' is assigned a value but never used.", "'handleShippingInfoChange' is assigned a value but never used.", "'tweetTitle' is assigned a value but never used.", "'tweetList' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'footerbottomList' is assigned a value but never used.", "'bannerList' is assigned a value but never used.", "'getApiHeaders' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'stock' is assigned a value but never used.", "'from' is assigned a value but never used.", "'handleOrderConfirm' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handlePayPalSuccess'. Either include it or remove the dependency array.", "ArrayExpression", ["439"], "jsx-a11y/iframe-has-title", "<iframe> elements must have a unique title property.", "'orderId' is assigned a value but never used.", ["440"], {"desc": "441", "fix": "442"}, {"desc": "441", "fix": "443"}, "Update the dependencies array to be: [activeTab, orderTotal, isFormValid, handlePayPalSuccess]", {"range": "444", "text": "445"}, {"range": "446", "text": "445"}, [14789, 14825], "[activeTab, orderTotal, isFormValid, handlePayPalSuccess]", [14789, 14825]]