{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport Image, { propTypes as imagePropTypes } from './Image';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FigureImage = /*#__PURE__*/React.forwardRef(({\n  className,\n  fluid = true,\n  ...props\n}, ref) => /*#__PURE__*/_jsx(Image, {\n  ref: ref,\n  ...props,\n  fluid: fluid,\n  className: classNames(className, 'figure-img')\n}));\nFigureImage.displayName = 'FigureImage';\nFigureImage.propTypes = imagePropTypes;\nexport default FigureImage;", "map": {"version": 3, "names": ["classNames", "React", "Image", "propTypes", "imagePropTypes", "jsx", "_jsx", "FigureImage", "forwardRef", "className", "fluid", "props", "ref", "displayName"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/react-bootstrap/esm/FigureImage.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport Image, { propTypes as imagePropTypes } from './Image';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FigureImage = /*#__PURE__*/React.forwardRef(({\n  className,\n  fluid = true,\n  ...props\n}, ref) => /*#__PURE__*/_jsx(Image, {\n  ref: ref,\n  ...props,\n  fluid: fluid,\n  className: classNames(className, 'figure-img')\n}));\nFigureImage.displayName = 'FigureImage';\nFigureImage.propTypes = imagePropTypes;\nexport default FigureImage;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,KAAK,IAAIC,SAAS,IAAIC,cAAc,QAAQ,SAAS;AAC5D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,WAAW,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAC;EACjDC,SAAS;EACTC,KAAK,GAAG,IAAI;EACZ,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK,aAAaN,IAAI,CAACJ,KAAK,EAAE;EAClCU,GAAG,EAAEA,GAAG;EACR,GAAGD,KAAK;EACRD,KAAK,EAAEA,KAAK;EACZD,SAAS,EAAET,UAAU,CAACS,SAAS,EAAE,YAAY;AAC/C,CAAC,CAAC,CAAC;AACHF,WAAW,CAACM,WAAW,GAAG,aAAa;AACvCN,WAAW,CAACJ,SAAS,GAAGC,cAAc;AACtC,eAAeG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}