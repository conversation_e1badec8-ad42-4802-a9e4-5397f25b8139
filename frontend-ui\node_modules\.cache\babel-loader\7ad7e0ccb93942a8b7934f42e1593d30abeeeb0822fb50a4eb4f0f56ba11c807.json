{"ast": null, "code": "/**\n * Swiper React 11.2.8\n * Most modern mobile touch slider and framework with hardware accelerated transitions\n * https://swiperjs.com\n *\n * Copyright 2014-2025 <PERSON>\n *\n * Released under the MIT License\n *\n * Released on: May 23, 2025\n */\n\nimport React, { useEffect, useLayoutEffect, useContext, createContext, forwardRef, useState, useRef } from 'react';\nimport { S as Swiper$1 } from './shared/swiper-core.mjs';\nimport { g as getParams, m as mountSwiper, a as getChangedParams, u as updateOnVirtualData } from './shared/update-on-virtual-data.mjs';\nimport { d as uniqueClasses, w as wrapperClass, n as needsNavigation, b as needsScrollbar, a as needsPagination, e as extend, u as updateSwiper } from './shared/update-swiper.mjs';\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction isChildSwiperSlide(child) {\n  return child.type && child.type.displayName && child.type.displayName.includes('SwiperSlide');\n}\nfunction processChildren(c) {\n  const slides = [];\n  React.Children.toArray(c).forEach(child => {\n    if (isChildSwiperSlide(child)) {\n      slides.push(child);\n    } else if (child.props && child.props.children) {\n      processChildren(child.props.children).forEach(slide => slides.push(slide));\n    }\n  });\n  return slides;\n}\nfunction getChildren(c) {\n  const slides = [];\n  const slots = {\n    'container-start': [],\n    'container-end': [],\n    'wrapper-start': [],\n    'wrapper-end': []\n  };\n  React.Children.toArray(c).forEach(child => {\n    if (isChildSwiperSlide(child)) {\n      slides.push(child);\n    } else if (child.props && child.props.slot && slots[child.props.slot]) {\n      slots[child.props.slot].push(child);\n    } else if (child.props && child.props.children) {\n      const foundSlides = processChildren(child.props.children);\n      if (foundSlides.length > 0) {\n        foundSlides.forEach(slide => slides.push(slide));\n      } else {\n        slots['container-end'].push(child);\n      }\n    } else {\n      slots['container-end'].push(child);\n    }\n  });\n  return {\n    slides,\n    slots\n  };\n}\nfunction renderVirtual(swiper, slides, virtualData) {\n  if (!virtualData) return null;\n  const getSlideIndex = index => {\n    let slideIndex = index;\n    if (index < 0) {\n      slideIndex = slides.length + index;\n    } else if (slideIndex >= slides.length) {\n      // eslint-disable-next-line\n      slideIndex = slideIndex - slides.length;\n    }\n    return slideIndex;\n  };\n  const style = swiper.isHorizontal() ? {\n    [swiper.rtlTranslate ? 'right' : 'left']: `${virtualData.offset}px`\n  } : {\n    top: `${virtualData.offset}px`\n  };\n  const {\n    from,\n    to\n  } = virtualData;\n  const loopFrom = swiper.params.loop ? -slides.length : 0;\n  const loopTo = swiper.params.loop ? slides.length * 2 : slides.length;\n  const slidesToRender = [];\n  for (let i = loopFrom; i < loopTo; i += 1) {\n    if (i >= from && i <= to) {\n      slidesToRender.push(slides[getSlideIndex(i)]);\n    }\n  }\n  return slidesToRender.map((child, index) => {\n    return /*#__PURE__*/React.cloneElement(child, {\n      swiper,\n      style,\n      key: child.props.virtualIndex || child.key || `slide-${index}`\n    });\n  });\n}\nfunction useIsomorphicLayoutEffect(callback, deps) {\n  // eslint-disable-next-line\n  if (typeof window === 'undefined') return useEffect(callback, deps);\n  return useLayoutEffect(callback, deps);\n}\nconst SwiperSlideContext = /*#__PURE__*/createContext(null);\nconst useSwiperSlide = () => {\n  return useContext(SwiperSlideContext);\n};\nconst SwiperContext = /*#__PURE__*/createContext(null);\nconst useSwiper = () => {\n  return useContext(SwiperContext);\n};\nconst Swiper = /*#__PURE__*/forwardRef(function (_temp, externalElRef) {\n  let {\n    className,\n    tag: Tag = 'div',\n    wrapperTag: WrapperTag = 'div',\n    children,\n    onSwiper,\n    ...rest\n  } = _temp === void 0 ? {} : _temp;\n  let eventsAssigned = false;\n  const [containerClasses, setContainerClasses] = useState('swiper');\n  const [virtualData, setVirtualData] = useState(null);\n  const [breakpointChanged, setBreakpointChanged] = useState(false);\n  const initializedRef = useRef(false);\n  const swiperElRef = useRef(null);\n  const swiperRef = useRef(null);\n  const oldPassedParamsRef = useRef(null);\n  const oldSlides = useRef(null);\n  const nextElRef = useRef(null);\n  const prevElRef = useRef(null);\n  const paginationElRef = useRef(null);\n  const scrollbarElRef = useRef(null);\n  const {\n    params: swiperParams,\n    passedParams,\n    rest: restProps,\n    events\n  } = getParams(rest);\n  const {\n    slides,\n    slots\n  } = getChildren(children);\n  const onBeforeBreakpoint = () => {\n    setBreakpointChanged(!breakpointChanged);\n  };\n  Object.assign(swiperParams.on, {\n    _containerClasses(swiper, classes) {\n      setContainerClasses(classes);\n    }\n  });\n  const initSwiper = () => {\n    // init swiper\n    Object.assign(swiperParams.on, events);\n    eventsAssigned = true;\n    const passParams = {\n      ...swiperParams\n    };\n    delete passParams.wrapperClass;\n    swiperRef.current = new Swiper$1(passParams);\n    if (swiperRef.current.virtual && swiperRef.current.params.virtual.enabled) {\n      swiperRef.current.virtual.slides = slides;\n      const extendWith = {\n        cache: false,\n        slides,\n        renderExternal: setVirtualData,\n        renderExternalUpdate: false\n      };\n      extend(swiperRef.current.params.virtual, extendWith);\n      extend(swiperRef.current.originalParams.virtual, extendWith);\n    }\n  };\n  if (!swiperElRef.current) {\n    initSwiper();\n  }\n\n  // Listen for breakpoints change\n  if (swiperRef.current) {\n    swiperRef.current.on('_beforeBreakpoint', onBeforeBreakpoint);\n  }\n  const attachEvents = () => {\n    if (eventsAssigned || !events || !swiperRef.current) return;\n    Object.keys(events).forEach(eventName => {\n      swiperRef.current.on(eventName, events[eventName]);\n    });\n  };\n  const detachEvents = () => {\n    if (!events || !swiperRef.current) return;\n    Object.keys(events).forEach(eventName => {\n      swiperRef.current.off(eventName, events[eventName]);\n    });\n  };\n  useEffect(() => {\n    return () => {\n      if (swiperRef.current) swiperRef.current.off('_beforeBreakpoint', onBeforeBreakpoint);\n    };\n  });\n\n  // set initialized flag\n  useEffect(() => {\n    if (!initializedRef.current && swiperRef.current) {\n      swiperRef.current.emitSlidesClasses();\n      initializedRef.current = true;\n    }\n  });\n\n  // mount swiper\n  useIsomorphicLayoutEffect(() => {\n    if (externalElRef) {\n      externalElRef.current = swiperElRef.current;\n    }\n    if (!swiperElRef.current) return;\n    if (swiperRef.current.destroyed) {\n      initSwiper();\n    }\n    mountSwiper({\n      el: swiperElRef.current,\n      nextEl: nextElRef.current,\n      prevEl: prevElRef.current,\n      paginationEl: paginationElRef.current,\n      scrollbarEl: scrollbarElRef.current,\n      swiper: swiperRef.current\n    }, swiperParams);\n    if (onSwiper && !swiperRef.current.destroyed) onSwiper(swiperRef.current);\n    // eslint-disable-next-line\n    return () => {\n      if (swiperRef.current && !swiperRef.current.destroyed) {\n        swiperRef.current.destroy(true, false);\n      }\n    };\n  }, []);\n\n  // watch for params change\n  useIsomorphicLayoutEffect(() => {\n    attachEvents();\n    const changedParams = getChangedParams(passedParams, oldPassedParamsRef.current, slides, oldSlides.current, c => c.key);\n    oldPassedParamsRef.current = passedParams;\n    oldSlides.current = slides;\n    if (changedParams.length && swiperRef.current && !swiperRef.current.destroyed) {\n      updateSwiper({\n        swiper: swiperRef.current,\n        slides,\n        passedParams,\n        changedParams,\n        nextEl: nextElRef.current,\n        prevEl: prevElRef.current,\n        scrollbarEl: scrollbarElRef.current,\n        paginationEl: paginationElRef.current\n      });\n    }\n    return () => {\n      detachEvents();\n    };\n  });\n\n  // update on virtual update\n  useIsomorphicLayoutEffect(() => {\n    updateOnVirtualData(swiperRef.current);\n  }, [virtualData]);\n\n  // bypass swiper instance to slides\n  function renderSlides() {\n    if (swiperParams.virtual) {\n      return renderVirtual(swiperRef.current, slides, virtualData);\n    }\n    return slides.map((child, index) => {\n      return /*#__PURE__*/React.cloneElement(child, {\n        swiper: swiperRef.current,\n        swiperSlideIndex: index\n      });\n    });\n  }\n  return /*#__PURE__*/React.createElement(Tag, _extends({\n    ref: swiperElRef,\n    className: uniqueClasses(`${containerClasses}${className ? ` ${className}` : ''}`)\n  }, restProps), /*#__PURE__*/React.createElement(SwiperContext.Provider, {\n    value: swiperRef.current\n  }, slots['container-start'], /*#__PURE__*/React.createElement(WrapperTag, {\n    className: wrapperClass(swiperParams.wrapperClass)\n  }, slots['wrapper-start'], renderSlides(), slots['wrapper-end']), needsNavigation(swiperParams) && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    ref: prevElRef,\n    className: \"swiper-button-prev\"\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    ref: nextElRef,\n    className: \"swiper-button-next\"\n  })), needsScrollbar(swiperParams) && /*#__PURE__*/React.createElement(\"div\", {\n    ref: scrollbarElRef,\n    className: \"swiper-scrollbar\"\n  }), needsPagination(swiperParams) && /*#__PURE__*/React.createElement(\"div\", {\n    ref: paginationElRef,\n    className: \"swiper-pagination\"\n  }), slots['container-end']));\n});\nSwiper.displayName = 'Swiper';\nconst SwiperSlide = /*#__PURE__*/forwardRef(function (_temp, externalRef) {\n  let {\n    tag: Tag = 'div',\n    children,\n    className = '',\n    swiper,\n    zoom,\n    lazy,\n    virtualIndex,\n    swiperSlideIndex,\n    ...rest\n  } = _temp === void 0 ? {} : _temp;\n  const slideElRef = useRef(null);\n  const [slideClasses, setSlideClasses] = useState('swiper-slide');\n  const [lazyLoaded, setLazyLoaded] = useState(false);\n  function updateClasses(_s, el, classNames) {\n    if (el === slideElRef.current) {\n      setSlideClasses(classNames);\n    }\n  }\n  useIsomorphicLayoutEffect(() => {\n    if (typeof swiperSlideIndex !== 'undefined') {\n      slideElRef.current.swiperSlideIndex = swiperSlideIndex;\n    }\n    if (externalRef) {\n      externalRef.current = slideElRef.current;\n    }\n    if (!slideElRef.current || !swiper) {\n      return;\n    }\n    if (swiper.destroyed) {\n      if (slideClasses !== 'swiper-slide') {\n        setSlideClasses('swiper-slide');\n      }\n      return;\n    }\n    swiper.on('_slideClass', updateClasses);\n    // eslint-disable-next-line\n    return () => {\n      if (!swiper) return;\n      swiper.off('_slideClass', updateClasses);\n    };\n  });\n  useIsomorphicLayoutEffect(() => {\n    if (swiper && slideElRef.current && !swiper.destroyed) {\n      setSlideClasses(swiper.getSlideClasses(slideElRef.current));\n    }\n  }, [swiper]);\n  const slideData = {\n    isActive: slideClasses.indexOf('swiper-slide-active') >= 0,\n    isVisible: slideClasses.indexOf('swiper-slide-visible') >= 0,\n    isPrev: slideClasses.indexOf('swiper-slide-prev') >= 0,\n    isNext: slideClasses.indexOf('swiper-slide-next') >= 0\n  };\n  const renderChildren = () => {\n    return typeof children === 'function' ? children(slideData) : children;\n  };\n  const onLoad = () => {\n    setLazyLoaded(true);\n  };\n  return /*#__PURE__*/React.createElement(Tag, _extends({\n    ref: slideElRef,\n    className: uniqueClasses(`${slideClasses}${className ? ` ${className}` : ''}`),\n    \"data-swiper-slide-index\": virtualIndex,\n    onLoad: onLoad\n  }, rest), zoom && /*#__PURE__*/React.createElement(SwiperSlideContext.Provider, {\n    value: slideData\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"swiper-zoom-container\",\n    \"data-swiper-zoom\": typeof zoom === 'number' ? zoom : undefined\n  }, renderChildren(), lazy && !lazyLoaded && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"swiper-lazy-preloader\"\n  }))), !zoom && /*#__PURE__*/React.createElement(SwiperSlideContext.Provider, {\n    value: slideData\n  }, renderChildren(), lazy && !lazyLoaded && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"swiper-lazy-preloader\"\n  })));\n});\nSwiperSlide.displayName = 'SwiperSlide';\nexport { Swiper, SwiperSlide, useSwiper, useSwiperSlide };", "map": {"version": 3, "names": ["React", "useEffect", "useLayoutEffect", "useContext", "createContext", "forwardRef", "useState", "useRef", "S", "Swiper$1", "g", "getParams", "m", "mountSwiper", "a", "getChangedParams", "u", "updateOnVirtualData", "d", "uniqueClasses", "w", "wrapperClass", "n", "needsNavigation", "b", "needsScrollbar", "needsPagination", "e", "extend", "updateSwiper", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "isChildSwiperSlide", "child", "type", "displayName", "includes", "processChildren", "c", "slides", "Children", "toArray", "for<PERSON>ach", "push", "props", "children", "slide", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slots", "slot", "foundSlides", "renderVirtual", "swiper", "virtualData", "getSlideIndex", "index", "slideIndex", "style", "isHorizontal", "rtlTranslate", "offset", "top", "from", "to", "loopFrom", "params", "loop", "loopTo", "slidesToRender", "map", "cloneElement", "virtualIndex", "useIsomorphicLayoutEffect", "callback", "deps", "window", "SwiperSlideContext", "useSwiperSlide", "SwiperContext", "useSwiper", "Swiper", "_temp", "externalElRef", "className", "tag", "Tag", "wrapperTag", "WrapperTag", "onSwiper", "rest", "eventsAssigned", "containerClasses", "setContainerClasses", "setVirtualData", "breakpointChanged", "setBreakpointChanged", "initializedRef", "swiperElRef", "swiperRef", "oldPassedParamsRef", "oldSlides", "nextElRef", "prevElRef", "paginationElRef", "scrollbarElRef", "swiperParams", "passedParams", "restProps", "events", "onBeforeBreakpoint", "on", "_containerClasses", "classes", "initSwiper", "passParams", "current", "virtual", "enabled", "extendWith", "cache", "renderExternal", "renderExternalUpdate", "originalParams", "attachEvents", "keys", "eventName", "detachEvents", "off", "emitSlidesClasses", "destroyed", "el", "nextEl", "prevEl", "paginationEl", "scrollbarEl", "destroy", "changedParams", "renderSlides", "swiperSlideIndex", "createElement", "ref", "Provider", "value", "Fragment", "SwiperSlide", "externalRef", "zoom", "lazy", "slideElRef", "slideClasses", "setSlideClasses", "lazyLoaded", "setLazyLoaded", "updateClasses", "_s", "classNames", "getSlideClasses", "slideData", "isActive", "indexOf", "isVisible", "isPrev", "isNext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onLoad", "undefined"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/swiper/swiper-react.mjs"], "sourcesContent": ["/**\n * Swiper React 11.2.8\n * Most modern mobile touch slider and framework with hardware accelerated transitions\n * https://swiperjs.com\n *\n * Copyright 2014-2025 <PERSON>\n *\n * Released under the MIT License\n *\n * Released on: May 23, 2025\n */\n\nimport React, { useEffect, useLayoutEffect, useContext, createContext, forwardRef, useState, useRef } from 'react';\nimport { S as Swiper$1 } from './shared/swiper-core.mjs';\nimport { g as getParams, m as mountSwiper, a as getChangedParams, u as updateOnVirtualData } from './shared/update-on-virtual-data.mjs';\nimport { d as uniqueClasses, w as wrapperClass, n as needsNavigation, b as needsScrollbar, a as needsPagination, e as extend, u as updateSwiper } from './shared/update-swiper.mjs';\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\nfunction isChildSwiperSlide(child) {\n  return child.type && child.type.displayName && child.type.displayName.includes('SwiperSlide');\n}\nfunction processChildren(c) {\n  const slides = [];\n  React.Children.toArray(c).forEach(child => {\n    if (isChildSwiperSlide(child)) {\n      slides.push(child);\n    } else if (child.props && child.props.children) {\n      processChildren(child.props.children).forEach(slide => slides.push(slide));\n    }\n  });\n  return slides;\n}\nfunction getChildren(c) {\n  const slides = [];\n  const slots = {\n    'container-start': [],\n    'container-end': [],\n    'wrapper-start': [],\n    'wrapper-end': []\n  };\n  React.Children.toArray(c).forEach(child => {\n    if (isChildSwiperSlide(child)) {\n      slides.push(child);\n    } else if (child.props && child.props.slot && slots[child.props.slot]) {\n      slots[child.props.slot].push(child);\n    } else if (child.props && child.props.children) {\n      const foundSlides = processChildren(child.props.children);\n      if (foundSlides.length > 0) {\n        foundSlides.forEach(slide => slides.push(slide));\n      } else {\n        slots['container-end'].push(child);\n      }\n    } else {\n      slots['container-end'].push(child);\n    }\n  });\n  return {\n    slides,\n    slots\n  };\n}\n\nfunction renderVirtual(swiper, slides, virtualData) {\n  if (!virtualData) return null;\n  const getSlideIndex = index => {\n    let slideIndex = index;\n    if (index < 0) {\n      slideIndex = slides.length + index;\n    } else if (slideIndex >= slides.length) {\n      // eslint-disable-next-line\n      slideIndex = slideIndex - slides.length;\n    }\n    return slideIndex;\n  };\n  const style = swiper.isHorizontal() ? {\n    [swiper.rtlTranslate ? 'right' : 'left']: `${virtualData.offset}px`\n  } : {\n    top: `${virtualData.offset}px`\n  };\n  const {\n    from,\n    to\n  } = virtualData;\n  const loopFrom = swiper.params.loop ? -slides.length : 0;\n  const loopTo = swiper.params.loop ? slides.length * 2 : slides.length;\n  const slidesToRender = [];\n  for (let i = loopFrom; i < loopTo; i += 1) {\n    if (i >= from && i <= to) {\n      slidesToRender.push(slides[getSlideIndex(i)]);\n    }\n  }\n  return slidesToRender.map((child, index) => {\n    return /*#__PURE__*/React.cloneElement(child, {\n      swiper,\n      style,\n      key: child.props.virtualIndex || child.key || `slide-${index}`\n    });\n  });\n}\n\nfunction useIsomorphicLayoutEffect(callback, deps) {\n  // eslint-disable-next-line\n  if (typeof window === 'undefined') return useEffect(callback, deps);\n  return useLayoutEffect(callback, deps);\n}\n\nconst SwiperSlideContext = /*#__PURE__*/createContext(null);\nconst useSwiperSlide = () => {\n  return useContext(SwiperSlideContext);\n};\nconst SwiperContext = /*#__PURE__*/createContext(null);\nconst useSwiper = () => {\n  return useContext(SwiperContext);\n};\n\nconst Swiper = /*#__PURE__*/forwardRef(function (_temp, externalElRef) {\n  let {\n    className,\n    tag: Tag = 'div',\n    wrapperTag: WrapperTag = 'div',\n    children,\n    onSwiper,\n    ...rest\n  } = _temp === void 0 ? {} : _temp;\n  let eventsAssigned = false;\n  const [containerClasses, setContainerClasses] = useState('swiper');\n  const [virtualData, setVirtualData] = useState(null);\n  const [breakpointChanged, setBreakpointChanged] = useState(false);\n  const initializedRef = useRef(false);\n  const swiperElRef = useRef(null);\n  const swiperRef = useRef(null);\n  const oldPassedParamsRef = useRef(null);\n  const oldSlides = useRef(null);\n  const nextElRef = useRef(null);\n  const prevElRef = useRef(null);\n  const paginationElRef = useRef(null);\n  const scrollbarElRef = useRef(null);\n  const {\n    params: swiperParams,\n    passedParams,\n    rest: restProps,\n    events\n  } = getParams(rest);\n  const {\n    slides,\n    slots\n  } = getChildren(children);\n  const onBeforeBreakpoint = () => {\n    setBreakpointChanged(!breakpointChanged);\n  };\n  Object.assign(swiperParams.on, {\n    _containerClasses(swiper, classes) {\n      setContainerClasses(classes);\n    }\n  });\n  const initSwiper = () => {\n    // init swiper\n    Object.assign(swiperParams.on, events);\n    eventsAssigned = true;\n    const passParams = {\n      ...swiperParams\n    };\n    delete passParams.wrapperClass;\n    swiperRef.current = new Swiper$1(passParams);\n    if (swiperRef.current.virtual && swiperRef.current.params.virtual.enabled) {\n      swiperRef.current.virtual.slides = slides;\n      const extendWith = {\n        cache: false,\n        slides,\n        renderExternal: setVirtualData,\n        renderExternalUpdate: false\n      };\n      extend(swiperRef.current.params.virtual, extendWith);\n      extend(swiperRef.current.originalParams.virtual, extendWith);\n    }\n  };\n  if (!swiperElRef.current) {\n    initSwiper();\n  }\n\n  // Listen for breakpoints change\n  if (swiperRef.current) {\n    swiperRef.current.on('_beforeBreakpoint', onBeforeBreakpoint);\n  }\n  const attachEvents = () => {\n    if (eventsAssigned || !events || !swiperRef.current) return;\n    Object.keys(events).forEach(eventName => {\n      swiperRef.current.on(eventName, events[eventName]);\n    });\n  };\n  const detachEvents = () => {\n    if (!events || !swiperRef.current) return;\n    Object.keys(events).forEach(eventName => {\n      swiperRef.current.off(eventName, events[eventName]);\n    });\n  };\n  useEffect(() => {\n    return () => {\n      if (swiperRef.current) swiperRef.current.off('_beforeBreakpoint', onBeforeBreakpoint);\n    };\n  });\n\n  // set initialized flag\n  useEffect(() => {\n    if (!initializedRef.current && swiperRef.current) {\n      swiperRef.current.emitSlidesClasses();\n      initializedRef.current = true;\n    }\n  });\n\n  // mount swiper\n  useIsomorphicLayoutEffect(() => {\n    if (externalElRef) {\n      externalElRef.current = swiperElRef.current;\n    }\n    if (!swiperElRef.current) return;\n    if (swiperRef.current.destroyed) {\n      initSwiper();\n    }\n    mountSwiper({\n      el: swiperElRef.current,\n      nextEl: nextElRef.current,\n      prevEl: prevElRef.current,\n      paginationEl: paginationElRef.current,\n      scrollbarEl: scrollbarElRef.current,\n      swiper: swiperRef.current\n    }, swiperParams);\n    if (onSwiper && !swiperRef.current.destroyed) onSwiper(swiperRef.current);\n    // eslint-disable-next-line\n    return () => {\n      if (swiperRef.current && !swiperRef.current.destroyed) {\n        swiperRef.current.destroy(true, false);\n      }\n    };\n  }, []);\n\n  // watch for params change\n  useIsomorphicLayoutEffect(() => {\n    attachEvents();\n    const changedParams = getChangedParams(passedParams, oldPassedParamsRef.current, slides, oldSlides.current, c => c.key);\n    oldPassedParamsRef.current = passedParams;\n    oldSlides.current = slides;\n    if (changedParams.length && swiperRef.current && !swiperRef.current.destroyed) {\n      updateSwiper({\n        swiper: swiperRef.current,\n        slides,\n        passedParams,\n        changedParams,\n        nextEl: nextElRef.current,\n        prevEl: prevElRef.current,\n        scrollbarEl: scrollbarElRef.current,\n        paginationEl: paginationElRef.current\n      });\n    }\n    return () => {\n      detachEvents();\n    };\n  });\n\n  // update on virtual update\n  useIsomorphicLayoutEffect(() => {\n    updateOnVirtualData(swiperRef.current);\n  }, [virtualData]);\n\n  // bypass swiper instance to slides\n  function renderSlides() {\n    if (swiperParams.virtual) {\n      return renderVirtual(swiperRef.current, slides, virtualData);\n    }\n    return slides.map((child, index) => {\n      return /*#__PURE__*/React.cloneElement(child, {\n        swiper: swiperRef.current,\n        swiperSlideIndex: index\n      });\n    });\n  }\n  return /*#__PURE__*/React.createElement(Tag, _extends({\n    ref: swiperElRef,\n    className: uniqueClasses(`${containerClasses}${className ? ` ${className}` : ''}`)\n  }, restProps), /*#__PURE__*/React.createElement(SwiperContext.Provider, {\n    value: swiperRef.current\n  }, slots['container-start'], /*#__PURE__*/React.createElement(WrapperTag, {\n    className: wrapperClass(swiperParams.wrapperClass)\n  }, slots['wrapper-start'], renderSlides(), slots['wrapper-end']), needsNavigation(swiperParams) && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    ref: prevElRef,\n    className: \"swiper-button-prev\"\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    ref: nextElRef,\n    className: \"swiper-button-next\"\n  })), needsScrollbar(swiperParams) && /*#__PURE__*/React.createElement(\"div\", {\n    ref: scrollbarElRef,\n    className: \"swiper-scrollbar\"\n  }), needsPagination(swiperParams) && /*#__PURE__*/React.createElement(\"div\", {\n    ref: paginationElRef,\n    className: \"swiper-pagination\"\n  }), slots['container-end']));\n});\nSwiper.displayName = 'Swiper';\n\nconst SwiperSlide = /*#__PURE__*/forwardRef(function (_temp, externalRef) {\n  let {\n    tag: Tag = 'div',\n    children,\n    className = '',\n    swiper,\n    zoom,\n    lazy,\n    virtualIndex,\n    swiperSlideIndex,\n    ...rest\n  } = _temp === void 0 ? {} : _temp;\n  const slideElRef = useRef(null);\n  const [slideClasses, setSlideClasses] = useState('swiper-slide');\n  const [lazyLoaded, setLazyLoaded] = useState(false);\n  function updateClasses(_s, el, classNames) {\n    if (el === slideElRef.current) {\n      setSlideClasses(classNames);\n    }\n  }\n  useIsomorphicLayoutEffect(() => {\n    if (typeof swiperSlideIndex !== 'undefined') {\n      slideElRef.current.swiperSlideIndex = swiperSlideIndex;\n    }\n    if (externalRef) {\n      externalRef.current = slideElRef.current;\n    }\n    if (!slideElRef.current || !swiper) {\n      return;\n    }\n    if (swiper.destroyed) {\n      if (slideClasses !== 'swiper-slide') {\n        setSlideClasses('swiper-slide');\n      }\n      return;\n    }\n    swiper.on('_slideClass', updateClasses);\n    // eslint-disable-next-line\n    return () => {\n      if (!swiper) return;\n      swiper.off('_slideClass', updateClasses);\n    };\n  });\n  useIsomorphicLayoutEffect(() => {\n    if (swiper && slideElRef.current && !swiper.destroyed) {\n      setSlideClasses(swiper.getSlideClasses(slideElRef.current));\n    }\n  }, [swiper]);\n  const slideData = {\n    isActive: slideClasses.indexOf('swiper-slide-active') >= 0,\n    isVisible: slideClasses.indexOf('swiper-slide-visible') >= 0,\n    isPrev: slideClasses.indexOf('swiper-slide-prev') >= 0,\n    isNext: slideClasses.indexOf('swiper-slide-next') >= 0\n  };\n  const renderChildren = () => {\n    return typeof children === 'function' ? children(slideData) : children;\n  };\n  const onLoad = () => {\n    setLazyLoaded(true);\n  };\n  return /*#__PURE__*/React.createElement(Tag, _extends({\n    ref: slideElRef,\n    className: uniqueClasses(`${slideClasses}${className ? ` ${className}` : ''}`),\n    \"data-swiper-slide-index\": virtualIndex,\n    onLoad: onLoad\n  }, rest), zoom && /*#__PURE__*/React.createElement(SwiperSlideContext.Provider, {\n    value: slideData\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"swiper-zoom-container\",\n    \"data-swiper-zoom\": typeof zoom === 'number' ? zoom : undefined\n  }, renderChildren(), lazy && !lazyLoaded && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"swiper-lazy-preloader\"\n  }))), !zoom && /*#__PURE__*/React.createElement(SwiperSlideContext.Provider, {\n    value: slideData\n  }, renderChildren(), lazy && !lazyLoaded && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"swiper-lazy-preloader\"\n  })));\n});\nSwiperSlide.displayName = 'SwiperSlide';\n\nexport { Swiper, SwiperSlide, useSwiper, useSwiperSlide };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,eAAe,EAAEC,UAAU,EAAEC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAClH,SAASC,CAAC,IAAIC,QAAQ,QAAQ,0BAA0B;AACxD,SAASC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,mBAAmB,QAAQ,qCAAqC;AACvI,SAASC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,cAAc,EAAEX,CAAC,IAAIY,eAAe,EAAEC,CAAC,IAAIC,MAAM,EAAEZ,CAAC,IAAIa,YAAY,QAAQ,4BAA4B;AAEnL,SAASC,QAAQA,CAAA,EAAG;EAClBA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAClE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACzC,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MACzB,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QACtB,IAAIP,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;UACrDL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAC3B;MACF;IACF;IACA,OAAOL,MAAM;EACf,CAAC;EACD,OAAOJ,QAAQ,CAACa,KAAK,CAAC,IAAI,EAAEP,SAAS,CAAC;AACxC;AAEA,SAASQ,kBAAkBA,CAACC,KAAK,EAAE;EACjC,OAAOA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAACC,WAAW,IAAIF,KAAK,CAACC,IAAI,CAACC,WAAW,CAACC,QAAQ,CAAC,aAAa,CAAC;AAC/F;AACA,SAASC,eAAeA,CAACC,CAAC,EAAE;EAC1B,MAAMC,MAAM,GAAG,EAAE;EACjBnD,KAAK,CAACoD,QAAQ,CAACC,OAAO,CAACH,CAAC,CAAC,CAACI,OAAO,CAACT,KAAK,IAAI;IACzC,IAAID,kBAAkB,CAACC,KAAK,CAAC,EAAE;MAC7BM,MAAM,CAACI,IAAI,CAACV,KAAK,CAAC;IACpB,CAAC,MAAM,IAAIA,KAAK,CAACW,KAAK,IAAIX,KAAK,CAACW,KAAK,CAACC,QAAQ,EAAE;MAC9CR,eAAe,CAACJ,KAAK,CAACW,KAAK,CAACC,QAAQ,CAAC,CAACH,OAAO,CAACI,KAAK,IAAIP,MAAM,CAACI,IAAI,CAACG,KAAK,CAAC,CAAC;IAC5E;EACF,CAAC,CAAC;EACF,OAAOP,MAAM;AACf;AACA,SAASQ,WAAWA,CAACT,CAAC,EAAE;EACtB,MAAMC,MAAM,GAAG,EAAE;EACjB,MAAMS,KAAK,GAAG;IACZ,iBAAiB,EAAE,EAAE;IACrB,eAAe,EAAE,EAAE;IACnB,eAAe,EAAE,EAAE;IACnB,aAAa,EAAE;EACjB,CAAC;EACD5D,KAAK,CAACoD,QAAQ,CAACC,OAAO,CAACH,CAAC,CAAC,CAACI,OAAO,CAACT,KAAK,IAAI;IACzC,IAAID,kBAAkB,CAACC,KAAK,CAAC,EAAE;MAC7BM,MAAM,CAACI,IAAI,CAACV,KAAK,CAAC;IACpB,CAAC,MAAM,IAAIA,KAAK,CAACW,KAAK,IAAIX,KAAK,CAACW,KAAK,CAACK,IAAI,IAAID,KAAK,CAACf,KAAK,CAACW,KAAK,CAACK,IAAI,CAAC,EAAE;MACrED,KAAK,CAACf,KAAK,CAACW,KAAK,CAACK,IAAI,CAAC,CAACN,IAAI,CAACV,KAAK,CAAC;IACrC,CAAC,MAAM,IAAIA,KAAK,CAACW,KAAK,IAAIX,KAAK,CAACW,KAAK,CAACC,QAAQ,EAAE;MAC9C,MAAMK,WAAW,GAAGb,eAAe,CAACJ,KAAK,CAACW,KAAK,CAACC,QAAQ,CAAC;MACzD,IAAIK,WAAW,CAACzB,MAAM,GAAG,CAAC,EAAE;QAC1ByB,WAAW,CAACR,OAAO,CAACI,KAAK,IAAIP,MAAM,CAACI,IAAI,CAACG,KAAK,CAAC,CAAC;MAClD,CAAC,MAAM;QACLE,KAAK,CAAC,eAAe,CAAC,CAACL,IAAI,CAACV,KAAK,CAAC;MACpC;IACF,CAAC,MAAM;MACLe,KAAK,CAAC,eAAe,CAAC,CAACL,IAAI,CAACV,KAAK,CAAC;IACpC;EACF,CAAC,CAAC;EACF,OAAO;IACLM,MAAM;IACNS;EACF,CAAC;AACH;AAEA,SAASG,aAAaA,CAACC,MAAM,EAAEb,MAAM,EAAEc,WAAW,EAAE;EAClD,IAAI,CAACA,WAAW,EAAE,OAAO,IAAI;EAC7B,MAAMC,aAAa,GAAGC,KAAK,IAAI;IAC7B,IAAIC,UAAU,GAAGD,KAAK;IACtB,IAAIA,KAAK,GAAG,CAAC,EAAE;MACbC,UAAU,GAAGjB,MAAM,CAACd,MAAM,GAAG8B,KAAK;IACpC,CAAC,MAAM,IAAIC,UAAU,IAAIjB,MAAM,CAACd,MAAM,EAAE;MACtC;MACA+B,UAAU,GAAGA,UAAU,GAAGjB,MAAM,CAACd,MAAM;IACzC;IACA,OAAO+B,UAAU;EACnB,CAAC;EACD,MAAMC,KAAK,GAAGL,MAAM,CAACM,YAAY,CAAC,CAAC,GAAG;IACpC,CAACN,MAAM,CAACO,YAAY,GAAG,OAAO,GAAG,MAAM,GAAG,GAAGN,WAAW,CAACO,MAAM;EACjE,CAAC,GAAG;IACFC,GAAG,EAAE,GAAGR,WAAW,CAACO,MAAM;EAC5B,CAAC;EACD,MAAM;IACJE,IAAI;IACJC;EACF,CAAC,GAAGV,WAAW;EACf,MAAMW,QAAQ,GAAGZ,MAAM,CAACa,MAAM,CAACC,IAAI,GAAG,CAAC3B,MAAM,CAACd,MAAM,GAAG,CAAC;EACxD,MAAM0C,MAAM,GAAGf,MAAM,CAACa,MAAM,CAACC,IAAI,GAAG3B,MAAM,CAACd,MAAM,GAAG,CAAC,GAAGc,MAAM,CAACd,MAAM;EACrE,MAAM2C,cAAc,GAAG,EAAE;EACzB,KAAK,IAAI7C,CAAC,GAAGyC,QAAQ,EAAEzC,CAAC,GAAG4C,MAAM,EAAE5C,CAAC,IAAI,CAAC,EAAE;IACzC,IAAIA,CAAC,IAAIuC,IAAI,IAAIvC,CAAC,IAAIwC,EAAE,EAAE;MACxBK,cAAc,CAACzB,IAAI,CAACJ,MAAM,CAACe,aAAa,CAAC/B,CAAC,CAAC,CAAC,CAAC;IAC/C;EACF;EACA,OAAO6C,cAAc,CAACC,GAAG,CAAC,CAACpC,KAAK,EAAEsB,KAAK,KAAK;IAC1C,OAAO,aAAanE,KAAK,CAACkF,YAAY,CAACrC,KAAK,EAAE;MAC5CmB,MAAM;MACNK,KAAK;MACL9B,GAAG,EAAEM,KAAK,CAACW,KAAK,CAAC2B,YAAY,IAAItC,KAAK,CAACN,GAAG,IAAI,SAAS4B,KAAK;IAC9D,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,SAASiB,yBAAyBA,CAACC,QAAQ,EAAEC,IAAI,EAAE;EACjD;EACA,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE,OAAOtF,SAAS,CAACoF,QAAQ,EAAEC,IAAI,CAAC;EACnE,OAAOpF,eAAe,CAACmF,QAAQ,EAAEC,IAAI,CAAC;AACxC;AAEA,MAAME,kBAAkB,GAAG,aAAapF,aAAa,CAAC,IAAI,CAAC;AAC3D,MAAMqF,cAAc,GAAGA,CAAA,KAAM;EAC3B,OAAOtF,UAAU,CAACqF,kBAAkB,CAAC;AACvC,CAAC;AACD,MAAME,aAAa,GAAG,aAAatF,aAAa,CAAC,IAAI,CAAC;AACtD,MAAMuF,SAAS,GAAGA,CAAA,KAAM;EACtB,OAAOxF,UAAU,CAACuF,aAAa,CAAC;AAClC,CAAC;AAED,MAAME,MAAM,GAAG,aAAavF,UAAU,CAAC,UAAUwF,KAAK,EAAEC,aAAa,EAAE;EACrE,IAAI;IACFC,SAAS;IACTC,GAAG,EAAEC,GAAG,GAAG,KAAK;IAChBC,UAAU,EAAEC,UAAU,GAAG,KAAK;IAC9B1C,QAAQ;IACR2C,QAAQ;IACR,GAAGC;EACL,CAAC,GAAGR,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,KAAK;EACjC,IAAIS,cAAc,GAAG,KAAK;EAC1B,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlG,QAAQ,CAAC,QAAQ,CAAC;EAClE,MAAM,CAAC2D,WAAW,EAAEwC,cAAc,CAAC,GAAGnG,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACoG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrG,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAMsG,cAAc,GAAGrG,MAAM,CAAC,KAAK,CAAC;EACpC,MAAMsG,WAAW,GAAGtG,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMuG,SAAS,GAAGvG,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMwG,kBAAkB,GAAGxG,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMyG,SAAS,GAAGzG,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM0G,SAAS,GAAG1G,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM2G,SAAS,GAAG3G,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM4G,eAAe,GAAG5G,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM6G,cAAc,GAAG7G,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM;IACJsE,MAAM,EAAEwC,YAAY;IACpBC,YAAY;IACZjB,IAAI,EAAEkB,SAAS;IACfC;EACF,CAAC,GAAG7G,SAAS,CAAC0F,IAAI,CAAC;EACnB,MAAM;IACJlD,MAAM;IACNS;EACF,CAAC,GAAGD,WAAW,CAACF,QAAQ,CAAC;EACzB,MAAMgE,kBAAkB,GAAGA,CAAA,KAAM;IAC/Bd,oBAAoB,CAAC,CAACD,iBAAiB,CAAC;EAC1C,CAAC;EACD3E,MAAM,CAACC,MAAM,CAACqF,YAAY,CAACK,EAAE,EAAE;IAC7BC,iBAAiBA,CAAC3D,MAAM,EAAE4D,OAAO,EAAE;MACjCpB,mBAAmB,CAACoB,OAAO,CAAC;IAC9B;EACF,CAAC,CAAC;EACF,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB;IACA9F,MAAM,CAACC,MAAM,CAACqF,YAAY,CAACK,EAAE,EAAEF,MAAM,CAAC;IACtClB,cAAc,GAAG,IAAI;IACrB,MAAMwB,UAAU,GAAG;MACjB,GAAGT;IACL,CAAC;IACD,OAAOS,UAAU,CAACzG,YAAY;IAC9ByF,SAAS,CAACiB,OAAO,GAAG,IAAItH,QAAQ,CAACqH,UAAU,CAAC;IAC5C,IAAIhB,SAAS,CAACiB,OAAO,CAACC,OAAO,IAAIlB,SAAS,CAACiB,OAAO,CAAClD,MAAM,CAACmD,OAAO,CAACC,OAAO,EAAE;MACzEnB,SAAS,CAACiB,OAAO,CAACC,OAAO,CAAC7E,MAAM,GAAGA,MAAM;MACzC,MAAM+E,UAAU,GAAG;QACjBC,KAAK,EAAE,KAAK;QACZhF,MAAM;QACNiF,cAAc,EAAE3B,cAAc;QAC9B4B,oBAAoB,EAAE;MACxB,CAAC;MACDzG,MAAM,CAACkF,SAAS,CAACiB,OAAO,CAAClD,MAAM,CAACmD,OAAO,EAAEE,UAAU,CAAC;MACpDtG,MAAM,CAACkF,SAAS,CAACiB,OAAO,CAACO,cAAc,CAACN,OAAO,EAAEE,UAAU,CAAC;IAC9D;EACF,CAAC;EACD,IAAI,CAACrB,WAAW,CAACkB,OAAO,EAAE;IACxBF,UAAU,CAAC,CAAC;EACd;;EAEA;EACA,IAAIf,SAAS,CAACiB,OAAO,EAAE;IACrBjB,SAAS,CAACiB,OAAO,CAACL,EAAE,CAAC,mBAAmB,EAAED,kBAAkB,CAAC;EAC/D;EACA,MAAMc,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIjC,cAAc,IAAI,CAACkB,MAAM,IAAI,CAACV,SAAS,CAACiB,OAAO,EAAE;IACrDhG,MAAM,CAACyG,IAAI,CAAChB,MAAM,CAAC,CAAClE,OAAO,CAACmF,SAAS,IAAI;MACvC3B,SAAS,CAACiB,OAAO,CAACL,EAAE,CAACe,SAAS,EAAEjB,MAAM,CAACiB,SAAS,CAAC,CAAC;IACpD,CAAC,CAAC;EACJ,CAAC;EACD,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAAClB,MAAM,IAAI,CAACV,SAAS,CAACiB,OAAO,EAAE;IACnChG,MAAM,CAACyG,IAAI,CAAChB,MAAM,CAAC,CAAClE,OAAO,CAACmF,SAAS,IAAI;MACvC3B,SAAS,CAACiB,OAAO,CAACY,GAAG,CAACF,SAAS,EAAEjB,MAAM,CAACiB,SAAS,CAAC,CAAC;IACrD,CAAC,CAAC;EACJ,CAAC;EACDxI,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAI6G,SAAS,CAACiB,OAAO,EAAEjB,SAAS,CAACiB,OAAO,CAACY,GAAG,CAAC,mBAAmB,EAAElB,kBAAkB,CAAC;IACvF,CAAC;EACH,CAAC,CAAC;;EAEF;EACAxH,SAAS,CAAC,MAAM;IACd,IAAI,CAAC2G,cAAc,CAACmB,OAAO,IAAIjB,SAAS,CAACiB,OAAO,EAAE;MAChDjB,SAAS,CAACiB,OAAO,CAACa,iBAAiB,CAAC,CAAC;MACrChC,cAAc,CAACmB,OAAO,GAAG,IAAI;IAC/B;EACF,CAAC,CAAC;;EAEF;EACA3C,yBAAyB,CAAC,MAAM;IAC9B,IAAIU,aAAa,EAAE;MACjBA,aAAa,CAACiC,OAAO,GAAGlB,WAAW,CAACkB,OAAO;IAC7C;IACA,IAAI,CAAClB,WAAW,CAACkB,OAAO,EAAE;IAC1B,IAAIjB,SAAS,CAACiB,OAAO,CAACc,SAAS,EAAE;MAC/BhB,UAAU,CAAC,CAAC;IACd;IACAhH,WAAW,CAAC;MACViI,EAAE,EAAEjC,WAAW,CAACkB,OAAO;MACvBgB,MAAM,EAAE9B,SAAS,CAACc,OAAO;MACzBiB,MAAM,EAAE9B,SAAS,CAACa,OAAO;MACzBkB,YAAY,EAAE9B,eAAe,CAACY,OAAO;MACrCmB,WAAW,EAAE9B,cAAc,CAACW,OAAO;MACnC/D,MAAM,EAAE8C,SAAS,CAACiB;IACpB,CAAC,EAAEV,YAAY,CAAC;IAChB,IAAIjB,QAAQ,IAAI,CAACU,SAAS,CAACiB,OAAO,CAACc,SAAS,EAAEzC,QAAQ,CAACU,SAAS,CAACiB,OAAO,CAAC;IACzE;IACA,OAAO,MAAM;MACX,IAAIjB,SAAS,CAACiB,OAAO,IAAI,CAACjB,SAAS,CAACiB,OAAO,CAACc,SAAS,EAAE;QACrD/B,SAAS,CAACiB,OAAO,CAACoB,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/D,yBAAyB,CAAC,MAAM;IAC9BmD,YAAY,CAAC,CAAC;IACd,MAAMa,aAAa,GAAGrI,gBAAgB,CAACuG,YAAY,EAAEP,kBAAkB,CAACgB,OAAO,EAAE5E,MAAM,EAAE6D,SAAS,CAACe,OAAO,EAAE7E,CAAC,IAAIA,CAAC,CAACX,GAAG,CAAC;IACvHwE,kBAAkB,CAACgB,OAAO,GAAGT,YAAY;IACzCN,SAAS,CAACe,OAAO,GAAG5E,MAAM;IAC1B,IAAIiG,aAAa,CAAC/G,MAAM,IAAIyE,SAAS,CAACiB,OAAO,IAAI,CAACjB,SAAS,CAACiB,OAAO,CAACc,SAAS,EAAE;MAC7EhH,YAAY,CAAC;QACXmC,MAAM,EAAE8C,SAAS,CAACiB,OAAO;QACzB5E,MAAM;QACNmE,YAAY;QACZ8B,aAAa;QACbL,MAAM,EAAE9B,SAAS,CAACc,OAAO;QACzBiB,MAAM,EAAE9B,SAAS,CAACa,OAAO;QACzBmB,WAAW,EAAE9B,cAAc,CAACW,OAAO;QACnCkB,YAAY,EAAE9B,eAAe,CAACY;MAChC,CAAC,CAAC;IACJ;IACA,OAAO,MAAM;MACXW,YAAY,CAAC,CAAC;IAChB,CAAC;EACH,CAAC,CAAC;;EAEF;EACAtD,yBAAyB,CAAC,MAAM;IAC9BnE,mBAAmB,CAAC6F,SAAS,CAACiB,OAAO,CAAC;EACxC,CAAC,EAAE,CAAC9D,WAAW,CAAC,CAAC;;EAEjB;EACA,SAASoF,YAAYA,CAAA,EAAG;IACtB,IAAIhC,YAAY,CAACW,OAAO,EAAE;MACxB,OAAOjE,aAAa,CAAC+C,SAAS,CAACiB,OAAO,EAAE5E,MAAM,EAAEc,WAAW,CAAC;IAC9D;IACA,OAAOd,MAAM,CAAC8B,GAAG,CAAC,CAACpC,KAAK,EAAEsB,KAAK,KAAK;MAClC,OAAO,aAAanE,KAAK,CAACkF,YAAY,CAACrC,KAAK,EAAE;QAC5CmB,MAAM,EAAE8C,SAAS,CAACiB,OAAO;QACzBuB,gBAAgB,EAAEnF;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EACA,OAAO,aAAanE,KAAK,CAACuJ,aAAa,CAACtD,GAAG,EAAEnE,QAAQ,CAAC;IACpD0H,GAAG,EAAE3C,WAAW;IAChBd,SAAS,EAAE5E,aAAa,CAAC,GAAGoF,gBAAgB,GAAGR,SAAS,GAAG,IAAIA,SAAS,EAAE,GAAG,EAAE,EAAE;EACnF,CAAC,EAAEwB,SAAS,CAAC,EAAE,aAAavH,KAAK,CAACuJ,aAAa,CAAC7D,aAAa,CAAC+D,QAAQ,EAAE;IACtEC,KAAK,EAAE5C,SAAS,CAACiB;EACnB,CAAC,EAAEnE,KAAK,CAAC,iBAAiB,CAAC,EAAE,aAAa5D,KAAK,CAACuJ,aAAa,CAACpD,UAAU,EAAE;IACxEJ,SAAS,EAAE1E,YAAY,CAACgG,YAAY,CAAChG,YAAY;EACnD,CAAC,EAAEuC,KAAK,CAAC,eAAe,CAAC,EAAEyF,YAAY,CAAC,CAAC,EAAEzF,KAAK,CAAC,aAAa,CAAC,CAAC,EAAErC,eAAe,CAAC8F,YAAY,CAAC,IAAI,aAAarH,KAAK,CAACuJ,aAAa,CAACvJ,KAAK,CAAC2J,QAAQ,EAAE,IAAI,EAAE,aAAa3J,KAAK,CAACuJ,aAAa,CAAC,KAAK,EAAE;IAChMC,GAAG,EAAEtC,SAAS;IACdnB,SAAS,EAAE;EACb,CAAC,CAAC,EAAE,aAAa/F,KAAK,CAACuJ,aAAa,CAAC,KAAK,EAAE;IAC1CC,GAAG,EAAEvC,SAAS;IACdlB,SAAS,EAAE;EACb,CAAC,CAAC,CAAC,EAAEtE,cAAc,CAAC4F,YAAY,CAAC,IAAI,aAAarH,KAAK,CAACuJ,aAAa,CAAC,KAAK,EAAE;IAC3EC,GAAG,EAAEpC,cAAc;IACnBrB,SAAS,EAAE;EACb,CAAC,CAAC,EAAErE,eAAe,CAAC2F,YAAY,CAAC,IAAI,aAAarH,KAAK,CAACuJ,aAAa,CAAC,KAAK,EAAE;IAC3EC,GAAG,EAAErC,eAAe;IACpBpB,SAAS,EAAE;EACb,CAAC,CAAC,EAAEnC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;AAC9B,CAAC,CAAC;AACFgC,MAAM,CAAC7C,WAAW,GAAG,QAAQ;AAE7B,MAAM6G,WAAW,GAAG,aAAavJ,UAAU,CAAC,UAAUwF,KAAK,EAAEgE,WAAW,EAAE;EACxE,IAAI;IACF7D,GAAG,EAAEC,GAAG,GAAG,KAAK;IAChBxC,QAAQ;IACRsC,SAAS,GAAG,EAAE;IACd/B,MAAM;IACN8F,IAAI;IACJC,IAAI;IACJ5E,YAAY;IACZmE,gBAAgB;IAChB,GAAGjD;EACL,CAAC,GAAGR,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,KAAK;EACjC,MAAMmE,UAAU,GAAGzJ,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM,CAAC0J,YAAY,EAAEC,eAAe,CAAC,GAAG5J,QAAQ,CAAC,cAAc,CAAC;EAChE,MAAM,CAAC6J,UAAU,EAAEC,aAAa,CAAC,GAAG9J,QAAQ,CAAC,KAAK,CAAC;EACnD,SAAS+J,aAAaA,CAACC,EAAE,EAAExB,EAAE,EAAEyB,UAAU,EAAE;IACzC,IAAIzB,EAAE,KAAKkB,UAAU,CAACjC,OAAO,EAAE;MAC7BmC,eAAe,CAACK,UAAU,CAAC;IAC7B;EACF;EACAnF,yBAAyB,CAAC,MAAM;IAC9B,IAAI,OAAOkE,gBAAgB,KAAK,WAAW,EAAE;MAC3CU,UAAU,CAACjC,OAAO,CAACuB,gBAAgB,GAAGA,gBAAgB;IACxD;IACA,IAAIO,WAAW,EAAE;MACfA,WAAW,CAAC9B,OAAO,GAAGiC,UAAU,CAACjC,OAAO;IAC1C;IACA,IAAI,CAACiC,UAAU,CAACjC,OAAO,IAAI,CAAC/D,MAAM,EAAE;MAClC;IACF;IACA,IAAIA,MAAM,CAAC6E,SAAS,EAAE;MACpB,IAAIoB,YAAY,KAAK,cAAc,EAAE;QACnCC,eAAe,CAAC,cAAc,CAAC;MACjC;MACA;IACF;IACAlG,MAAM,CAAC0D,EAAE,CAAC,aAAa,EAAE2C,aAAa,CAAC;IACvC;IACA,OAAO,MAAM;MACX,IAAI,CAACrG,MAAM,EAAE;MACbA,MAAM,CAAC2E,GAAG,CAAC,aAAa,EAAE0B,aAAa,CAAC;IAC1C,CAAC;EACH,CAAC,CAAC;EACFjF,yBAAyB,CAAC,MAAM;IAC9B,IAAIpB,MAAM,IAAIgG,UAAU,CAACjC,OAAO,IAAI,CAAC/D,MAAM,CAAC6E,SAAS,EAAE;MACrDqB,eAAe,CAAClG,MAAM,CAACwG,eAAe,CAACR,UAAU,CAACjC,OAAO,CAAC,CAAC;IAC7D;EACF,CAAC,EAAE,CAAC/D,MAAM,CAAC,CAAC;EACZ,MAAMyG,SAAS,GAAG;IAChBC,QAAQ,EAAET,YAAY,CAACU,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC;IAC1DC,SAAS,EAAEX,YAAY,CAACU,OAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC;IAC5DE,MAAM,EAAEZ,YAAY,CAACU,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC;IACtDG,MAAM,EAAEb,YAAY,CAACU,OAAO,CAAC,mBAAmB,CAAC,IAAI;EACvD,CAAC;EACD,MAAMI,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAO,OAAOtH,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACgH,SAAS,CAAC,GAAGhH,QAAQ;EACxE,CAAC;EACD,MAAMuH,MAAM,GAAGA,CAAA,KAAM;IACnBZ,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EACD,OAAO,aAAapK,KAAK,CAACuJ,aAAa,CAACtD,GAAG,EAAEnE,QAAQ,CAAC;IACpD0H,GAAG,EAAEQ,UAAU;IACfjE,SAAS,EAAE5E,aAAa,CAAC,GAAG8I,YAAY,GAAGlE,SAAS,GAAG,IAAIA,SAAS,EAAE,GAAG,EAAE,EAAE,CAAC;IAC9E,yBAAyB,EAAEZ,YAAY;IACvC6F,MAAM,EAAEA;EACV,CAAC,EAAE3E,IAAI,CAAC,EAAEyD,IAAI,IAAI,aAAa9J,KAAK,CAACuJ,aAAa,CAAC/D,kBAAkB,CAACiE,QAAQ,EAAE;IAC9EC,KAAK,EAAEe;EACT,CAAC,EAAE,aAAazK,KAAK,CAACuJ,aAAa,CAAC,KAAK,EAAE;IACzCxD,SAAS,EAAE,uBAAuB;IAClC,kBAAkB,EAAE,OAAO+D,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGmB;EACxD,CAAC,EAAEF,cAAc,CAAC,CAAC,EAAEhB,IAAI,IAAI,CAACI,UAAU,IAAI,aAAanK,KAAK,CAACuJ,aAAa,CAAC,KAAK,EAAE;IAClFxD,SAAS,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC+D,IAAI,IAAI,aAAa9J,KAAK,CAACuJ,aAAa,CAAC/D,kBAAkB,CAACiE,QAAQ,EAAE;IAC3EC,KAAK,EAAEe;EACT,CAAC,EAAEM,cAAc,CAAC,CAAC,EAAEhB,IAAI,IAAI,CAACI,UAAU,IAAI,aAAanK,KAAK,CAACuJ,aAAa,CAAC,KAAK,EAAE;IAClFxD,SAAS,EAAE;EACb,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AACF6D,WAAW,CAAC7G,WAAW,GAAG,aAAa;AAEvC,SAAS6C,MAAM,EAAEgE,WAAW,EAAEjE,SAAS,EAAEF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}