{"ast": null, "code": "// Cart utility functions for managing cart operations\n\n/**\n * Get cart items from localStorage\n * @returns {Array} Array of cart items\n */\nexport const getCartItems = () => {\n  try {\n    const cartItems = JSON.parse(localStorage.getItem(\"cart\")) || [];\n    return cartItems.map(item => ({\n      ...item,\n      price: parseFloat(item.price) || 0,\n      quantity: parseInt(item.quantity) || 1\n    }));\n  } catch (error) {\n    console.error(\"Error getting cart items:\", error);\n    return [];\n  }\n};\n\n/**\n * Get total number of items in cart\n * @returns {number} Total quantity of items\n */\nexport const getCartItemCount = () => {\n  const cartItems = getCartItems();\n  return cartItems.reduce((total, item) => total + (parseInt(item.quantity) || 1), 0);\n};\n\n/**\n * Add item to cart\n * @param {Object} product - Product to add to cart\n * @param {number} quantity - Quantity to add (default: 1)\n * @returns {boolean} Success status\n */\nexport const addToCart = (product, quantity = 1) => {\n  try {\n    const existingCart = getCartItems();\n    const numericPrice = parseFloat(product.Price || product.price) || 0;\n\n    // Create standardized product object\n    const cartItem = {\n      id: product.BookID || product.id,\n      BookID: product.BookID || product.id,\n      // Store BookID for image\n      name: product.Title || product.title || product.name,\n      price: numericPrice,\n      quantity: parseInt(quantity),\n      author: product.Author || product.author || \"\"\n    };\n\n    // Check if item already exists in cart\n    const existingItemIndex = existingCart.findIndex(item => item.id === cartItem.id);\n    if (existingItemIndex !== -1) {\n      // Update quantity if item exists\n      existingCart[existingItemIndex].quantity += cartItem.quantity;\n    } else {\n      // Add new item to cart\n      existingCart.push(cartItem);\n    }\n\n    // Save to localStorage\n    localStorage.setItem(\"cart\", JSON.stringify(existingCart));\n    return true;\n  } catch (error) {\n    console.error(\"Error adding to cart:\", error);\n    return false;\n  }\n};\n\n/**\n * Remove item from cart\n * @param {string|number} itemId - ID of item to remove\n * @returns {boolean} Success status\n */\nexport const removeFromCart = itemId => {\n  try {\n    const existingCart = getCartItems();\n    const updatedCart = existingCart.filter(item => item.id !== itemId);\n    localStorage.setItem(\"cart\", JSON.stringify(updatedCart));\n    return true;\n  } catch (error) {\n    console.error(\"Error removing from cart:\", error);\n    return false;\n  }\n};\n\n/**\n * Update item quantity in cart\n * @param {string|number} itemId - ID of item to update\n * @param {number} newQuantity - New quantity\n * @returns {boolean} Success status\n */\nexport const updateCartItemQuantity = (itemId, newQuantity) => {\n  try {\n    const existingCart = getCartItems();\n    const itemIndex = existingCart.findIndex(item => item.id === itemId);\n    if (itemIndex !== -1) {\n      if (newQuantity <= 0) {\n        // Remove item if quantity is 0 or less\n        return removeFromCart(itemId);\n      } else {\n        existingCart[itemIndex].quantity = parseInt(newQuantity);\n        localStorage.setItem(\"cart\", JSON.stringify(existingCart));\n        return true;\n      }\n    }\n    return false;\n  } catch (error) {\n    console.error(\"Error updating cart item quantity:\", error);\n    return false;\n  }\n};\n\n/**\n * Clear entire cart\n * @returns {boolean} Success status\n */\nexport const clearCart = () => {\n  try {\n    localStorage.removeItem(\"cart\");\n    return true;\n  } catch (error) {\n    console.error(\"Error clearing cart:\", error);\n    return false;\n  }\n};\n\n/**\n * Calculate cart total\n * @returns {number} Total price of all items in cart\n */\nexport const getCartTotal = () => {\n  const cartItems = getCartItems();\n  return cartItems.reduce((total, item) => {\n    const price = parseFloat(item.price) || 0;\n    const quantity = parseInt(item.quantity) || 1;\n    return total + price * quantity;\n  }, 0);\n};", "map": {"version": 3, "names": ["getCartItems", "cartItems", "JSON", "parse", "localStorage", "getItem", "map", "item", "price", "parseFloat", "quantity", "parseInt", "error", "console", "getCartItemCount", "reduce", "total", "addToCart", "product", "existingCart", "numericPrice", "Price", "cartItem", "id", "BookID", "name", "Title", "title", "author", "Author", "existingItemIndex", "findIndex", "push", "setItem", "stringify", "removeFromCart", "itemId", "updatedCart", "filter", "updateCartItemQuantity", "newQuantity", "itemIndex", "clearCart", "removeItem", "getCartTotal"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/utilis/cartUtils.js"], "sourcesContent": ["// Cart utility functions for managing cart operations\n\n/**\n * Get cart items from localStorage\n * @returns {Array} Array of cart items\n */\nexport const getCartItems = () => {\n  try {\n    const cartItems = JSON.parse(localStorage.getItem(\"cart\")) || [];\n    return cartItems.map((item) => ({\n      ...item,\n      price: parseFloat(item.price) || 0,\n      quantity: parseInt(item.quantity) || 1,\n    }));\n  } catch (error) {\n    console.error(\"Error getting cart items:\", error);\n    return [];\n  }\n};\n\n/**\n * Get total number of items in cart\n * @returns {number} Total quantity of items\n */\nexport const getCartItemCount = () => {\n  const cartItems = getCartItems();\n  return cartItems.reduce((total, item) => total + (parseInt(item.quantity) || 1), 0);\n};\n\n/**\n * Add item to cart\n * @param {Object} product - Product to add to cart\n * @param {number} quantity - Quantity to add (default: 1)\n * @returns {boolean} Success status\n */\nexport const addToCart = (product, quantity = 1) => {\n  try {\n    const existingCart = getCartItems();\n    const numericPrice = parseFloat(product.Price || product.price) || 0;\n    \n    // Create standardized product object\n    const cartItem = {\n      id: product.BookID || product.id,\n      BookID: product.BookID || product.id, // Store BookID for image\n      name: product.Title || product.title || product.name,\n      price: numericPrice,\n      quantity: parseInt(quantity),\n      author: product.Author || product.author || \"\",\n    };\n\n    // Check if item already exists in cart\n    const existingItemIndex = existingCart.findIndex(\n      (item) => item.id === cartItem.id\n    );\n\n    if (existingItemIndex !== -1) {\n      // Update quantity if item exists\n      existingCart[existingItemIndex].quantity += cartItem.quantity;\n    } else {\n      // Add new item to cart\n      existingCart.push(cartItem);\n    }\n\n    // Save to localStorage\n    localStorage.setItem(\"cart\", JSON.stringify(existingCart));\n    return true;\n  } catch (error) {\n    console.error(\"Error adding to cart:\", error);\n    return false;\n  }\n};\n\n/**\n * Remove item from cart\n * @param {string|number} itemId - ID of item to remove\n * @returns {boolean} Success status\n */\nexport const removeFromCart = (itemId) => {\n  try {\n    const existingCart = getCartItems();\n    const updatedCart = existingCart.filter((item) => item.id !== itemId);\n    localStorage.setItem(\"cart\", JSON.stringify(updatedCart));\n    return true;\n  } catch (error) {\n    console.error(\"Error removing from cart:\", error);\n    return false;\n  }\n};\n\n/**\n * Update item quantity in cart\n * @param {string|number} itemId - ID of item to update\n * @param {number} newQuantity - New quantity\n * @returns {boolean} Success status\n */\nexport const updateCartItemQuantity = (itemId, newQuantity) => {\n  try {\n    const existingCart = getCartItems();\n    const itemIndex = existingCart.findIndex((item) => item.id === itemId);\n    \n    if (itemIndex !== -1) {\n      if (newQuantity <= 0) {\n        // Remove item if quantity is 0 or less\n        return removeFromCart(itemId);\n      } else {\n        existingCart[itemIndex].quantity = parseInt(newQuantity);\n        localStorage.setItem(\"cart\", JSON.stringify(existingCart));\n        return true;\n      }\n    }\n    return false;\n  } catch (error) {\n    console.error(\"Error updating cart item quantity:\", error);\n    return false;\n  }\n};\n\n/**\n * Clear entire cart\n * @returns {boolean} Success status\n */\nexport const clearCart = () => {\n  try {\n    localStorage.removeItem(\"cart\");\n    return true;\n  } catch (error) {\n    console.error(\"Error clearing cart:\", error);\n    return false;\n  }\n};\n\n/**\n * Calculate cart total\n * @returns {number} Total price of all items in cart\n */\nexport const getCartTotal = () => {\n  const cartItems = getCartItems();\n  return cartItems.reduce((total, item) => {\n    const price = parseFloat(item.price) || 0;\n    const quantity = parseInt(item.quantity) || 1;\n    return total + (price * quantity);\n  }, 0);\n};\n"], "mappings": "AAAA;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMA,YAAY,GAAGA,CAAA,KAAM;EAChC,IAAI;IACF,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE;IAChE,OAAOJ,SAAS,CAACK,GAAG,CAAEC,IAAI,KAAM;MAC9B,GAAGA,IAAI;MACPC,KAAK,EAAEC,UAAU,CAACF,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC;MAClCE,QAAQ,EAAEC,QAAQ,CAACJ,IAAI,CAACG,QAAQ,CAAC,IAAI;IACvC,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAME,gBAAgB,GAAGA,CAAA,KAAM;EACpC,MAAMb,SAAS,GAAGD,YAAY,CAAC,CAAC;EAChC,OAAOC,SAAS,CAACc,MAAM,CAAC,CAACC,KAAK,EAAET,IAAI,KAAKS,KAAK,IAAIL,QAAQ,CAACJ,IAAI,CAACG,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;AACrF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,SAAS,GAAGA,CAACC,OAAO,EAAER,QAAQ,GAAG,CAAC,KAAK;EAClD,IAAI;IACF,MAAMS,YAAY,GAAGnB,YAAY,CAAC,CAAC;IACnC,MAAMoB,YAAY,GAAGX,UAAU,CAACS,OAAO,CAACG,KAAK,IAAIH,OAAO,CAACV,KAAK,CAAC,IAAI,CAAC;;IAEpE;IACA,MAAMc,QAAQ,GAAG;MACfC,EAAE,EAAEL,OAAO,CAACM,MAAM,IAAIN,OAAO,CAACK,EAAE;MAChCC,MAAM,EAAEN,OAAO,CAACM,MAAM,IAAIN,OAAO,CAACK,EAAE;MAAE;MACtCE,IAAI,EAAEP,OAAO,CAACQ,KAAK,IAAIR,OAAO,CAACS,KAAK,IAAIT,OAAO,CAACO,IAAI;MACpDjB,KAAK,EAAEY,YAAY;MACnBV,QAAQ,EAAEC,QAAQ,CAACD,QAAQ,CAAC;MAC5BkB,MAAM,EAAEV,OAAO,CAACW,MAAM,IAAIX,OAAO,CAACU,MAAM,IAAI;IAC9C,CAAC;;IAED;IACA,MAAME,iBAAiB,GAAGX,YAAY,CAACY,SAAS,CAC7CxB,IAAI,IAAKA,IAAI,CAACgB,EAAE,KAAKD,QAAQ,CAACC,EACjC,CAAC;IAED,IAAIO,iBAAiB,KAAK,CAAC,CAAC,EAAE;MAC5B;MACAX,YAAY,CAACW,iBAAiB,CAAC,CAACpB,QAAQ,IAAIY,QAAQ,CAACZ,QAAQ;IAC/D,CAAC,MAAM;MACL;MACAS,YAAY,CAACa,IAAI,CAACV,QAAQ,CAAC;IAC7B;;IAEA;IACAlB,YAAY,CAAC6B,OAAO,CAAC,MAAM,EAAE/B,IAAI,CAACgC,SAAS,CAACf,YAAY,CAAC,CAAC;IAC1D,OAAO,IAAI;EACb,CAAC,CAAC,OAAOP,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7C,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMuB,cAAc,GAAIC,MAAM,IAAK;EACxC,IAAI;IACF,MAAMjB,YAAY,GAAGnB,YAAY,CAAC,CAAC;IACnC,MAAMqC,WAAW,GAAGlB,YAAY,CAACmB,MAAM,CAAE/B,IAAI,IAAKA,IAAI,CAACgB,EAAE,KAAKa,MAAM,CAAC;IACrEhC,YAAY,CAAC6B,OAAO,CAAC,MAAM,EAAE/B,IAAI,CAACgC,SAAS,CAACG,WAAW,CAAC,CAAC;IACzD,OAAO,IAAI;EACb,CAAC,CAAC,OAAOzB,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM2B,sBAAsB,GAAGA,CAACH,MAAM,EAAEI,WAAW,KAAK;EAC7D,IAAI;IACF,MAAMrB,YAAY,GAAGnB,YAAY,CAAC,CAAC;IACnC,MAAMyC,SAAS,GAAGtB,YAAY,CAACY,SAAS,CAAExB,IAAI,IAAKA,IAAI,CAACgB,EAAE,KAAKa,MAAM,CAAC;IAEtE,IAAIK,SAAS,KAAK,CAAC,CAAC,EAAE;MACpB,IAAID,WAAW,IAAI,CAAC,EAAE;QACpB;QACA,OAAOL,cAAc,CAACC,MAAM,CAAC;MAC/B,CAAC,MAAM;QACLjB,YAAY,CAACsB,SAAS,CAAC,CAAC/B,QAAQ,GAAGC,QAAQ,CAAC6B,WAAW,CAAC;QACxDpC,YAAY,CAAC6B,OAAO,CAAC,MAAM,EAAE/B,IAAI,CAACgC,SAAS,CAACf,YAAY,CAAC,CAAC;QAC1D,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOP,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC1D,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAM8B,SAAS,GAAGA,CAAA,KAAM;EAC7B,IAAI;IACFtC,YAAY,CAACuC,UAAU,CAAC,MAAM,CAAC;IAC/B,OAAO,IAAI;EACb,CAAC,CAAC,OAAO/B,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC5C,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMgC,YAAY,GAAGA,CAAA,KAAM;EAChC,MAAM3C,SAAS,GAAGD,YAAY,CAAC,CAAC;EAChC,OAAOC,SAAS,CAACc,MAAM,CAAC,CAACC,KAAK,EAAET,IAAI,KAAK;IACvC,MAAMC,KAAK,GAAGC,UAAU,CAACF,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC;IACzC,MAAME,QAAQ,GAAGC,QAAQ,CAACJ,IAAI,CAACG,QAAQ,CAAC,IAAI,CAAC;IAC7C,OAAOM,KAAK,GAAIR,KAAK,GAAGE,QAAS;EACnC,CAAC,EAAE,CAAC,CAAC;AACP,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}