{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useCallback, useMemo } from 'react';\nimport SelectableContext from '@restart/ui/SelectableContext';\nimport { useUncontrolled } from 'uncontrollable';\nimport NavbarBrand from './NavbarBrand';\nimport NavbarCollapse from './NavbarCollapse';\nimport NavbarToggle from './NavbarToggle';\nimport NavbarOffcanvas from './NavbarOffcanvas';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport NavbarContext from './NavbarContext';\nimport NavbarText from './NavbarText';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Navbar = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    bsPrefix: initialBsPrefix,\n    expand = true,\n    variant = 'light',\n    bg,\n    fixed,\n    sticky,\n    className,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'nav',\n    expanded,\n    onToggle,\n    onSelect,\n    collapseOnSelect = false,\n    ...controlledProps\n  } = useUncontrolled(props, {\n    expanded: 'onToggle'\n  });\n  const bsPrefix = useBootstrapPrefix(initialBsPrefix, 'navbar');\n  const handleCollapse = useCallback((...args) => {\n    onSelect == null || onSelect(...args);\n    if (collapseOnSelect && expanded) {\n      onToggle == null || onToggle(false);\n    }\n  }, [onSelect, collapseOnSelect, expanded, onToggle]);\n\n  // will result in some false positives but that seems better\n  // than false negatives. strict `undefined` check allows explicit\n  // \"nulling\" of the role if the user really doesn't want one\n  if (controlledProps.role === undefined && Component !== 'nav') {\n    controlledProps.role = 'navigation';\n  }\n  let expandClass = `${bsPrefix}-expand`;\n  if (typeof expand === 'string') expandClass = `${expandClass}-${expand}`;\n  const navbarContext = useMemo(() => ({\n    onToggle: () => onToggle == null ? void 0 : onToggle(!expanded),\n    bsPrefix,\n    expanded: !!expanded,\n    expand\n  }), [bsPrefix, expanded, expand, onToggle]);\n  return /*#__PURE__*/_jsx(NavbarContext.Provider, {\n    value: navbarContext,\n    children: /*#__PURE__*/_jsx(SelectableContext.Provider, {\n      value: handleCollapse,\n      children: /*#__PURE__*/_jsx(Component, {\n        ref: ref,\n        ...controlledProps,\n        className: classNames(className, bsPrefix, expand && expandClass, variant && `${bsPrefix}-${variant}`, bg && `bg-${bg}`, sticky && `sticky-${sticky}`, fixed && `fixed-${fixed}`)\n      })\n    })\n  });\n});\nNavbar.displayName = 'Navbar';\nexport default Object.assign(Navbar, {\n  Brand: NavbarBrand,\n  Collapse: NavbarCollapse,\n  Offcanvas: NavbarOffcanvas,\n  Text: NavbarText,\n  Toggle: NavbarToggle\n});", "map": {"version": 3, "names": ["classNames", "React", "useCallback", "useMemo", "SelectableContext", "useUncontrolled", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NavbarCollapse", "Navbar<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useBootstrapPrefix", "NavbarContext", "NavbarText", "jsx", "_jsx", "<PERSON><PERSON><PERSON>", "forwardRef", "props", "ref", "bsPrefix", "initialBsPrefix", "expand", "variant", "bg", "fixed", "sticky", "className", "as", "Component", "expanded", "onToggle", "onSelect", "collapseOnSelect", "controlledProps", "handleCollapse", "args", "role", "undefined", "expandClass", "navbarContext", "Provider", "value", "children", "displayName", "Object", "assign", "Brand", "Collapse", "<PERSON><PERSON><PERSON>", "Text", "Toggle"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/react-bootstrap/esm/Navbar.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useCallback, useMemo } from 'react';\nimport SelectableContext from '@restart/ui/SelectableContext';\nimport { useUncontrolled } from 'uncontrollable';\nimport NavbarBrand from './NavbarBrand';\nimport NavbarCollapse from './NavbarCollapse';\nimport NavbarToggle from './NavbarToggle';\nimport NavbarOffcanvas from './NavbarOffcanvas';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport NavbarContext from './NavbarContext';\nimport NavbarText from './NavbarText';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Navbar = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    bsPrefix: initialBsPrefix,\n    expand = true,\n    variant = 'light',\n    bg,\n    fixed,\n    sticky,\n    className,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'nav',\n    expanded,\n    onToggle,\n    onSelect,\n    collapseOnSelect = false,\n    ...controlledProps\n  } = useUncontrolled(props, {\n    expanded: 'onToggle'\n  });\n  const bsPrefix = useBootstrapPrefix(initialBsPrefix, 'navbar');\n  const handleCollapse = useCallback((...args) => {\n    onSelect == null || onSelect(...args);\n    if (collapseOnSelect && expanded) {\n      onToggle == null || onToggle(false);\n    }\n  }, [onSelect, collapseOnSelect, expanded, onToggle]);\n\n  // will result in some false positives but that seems better\n  // than false negatives. strict `undefined` check allows explicit\n  // \"nulling\" of the role if the user really doesn't want one\n  if (controlledProps.role === undefined && Component !== 'nav') {\n    controlledProps.role = 'navigation';\n  }\n  let expandClass = `${bsPrefix}-expand`;\n  if (typeof expand === 'string') expandClass = `${expandClass}-${expand}`;\n  const navbarContext = useMemo(() => ({\n    onToggle: () => onToggle == null ? void 0 : onToggle(!expanded),\n    bsPrefix,\n    expanded: !!expanded,\n    expand\n  }), [bsPrefix, expanded, expand, onToggle]);\n  return /*#__PURE__*/_jsx(NavbarContext.Provider, {\n    value: navbarContext,\n    children: /*#__PURE__*/_jsx(SelectableContext.Provider, {\n      value: handleCollapse,\n      children: /*#__PURE__*/_jsx(Component, {\n        ref: ref,\n        ...controlledProps,\n        className: classNames(className, bsPrefix, expand && expandClass, variant && `${bsPrefix}-${variant}`, bg && `bg-${bg}`, sticky && `sticky-${sticky}`, fixed && `fixed-${fixed}`)\n      })\n    })\n  });\n});\nNavbar.displayName = 'Navbar';\nexport default Object.assign(Navbar, {\n  Brand: NavbarBrand,\n  Collapse: NavbarCollapse,\n  Offcanvas: NavbarOffcanvas,\n  Text: NavbarText,\n  Toggle: NavbarToggle\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AAC5C,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,SAASC,eAAe,QAAQ,gBAAgB;AAChD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,MAAM,GAAG,aAAad,KAAK,CAACe,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC3D,MAAM;IACJC,QAAQ,EAAEC,eAAe;IACzBC,MAAM,GAAG,IAAI;IACbC,OAAO,GAAG,OAAO;IACjBC,EAAE;IACFC,KAAK;IACLC,MAAM;IACNC,SAAS;IACT;IACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;IACrBC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,gBAAgB,GAAG,KAAK;IACxB,GAAGC;EACL,CAAC,GAAG5B,eAAe,CAACY,KAAK,EAAE;IACzBY,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAMV,QAAQ,GAAGT,kBAAkB,CAACU,eAAe,EAAE,QAAQ,CAAC;EAC9D,MAAMc,cAAc,GAAGhC,WAAW,CAAC,CAAC,GAAGiC,IAAI,KAAK;IAC9CJ,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAAC,GAAGI,IAAI,CAAC;IACrC,IAAIH,gBAAgB,IAAIH,QAAQ,EAAE;MAChCC,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAAC,KAAK,CAAC;IACrC;EACF,CAAC,EAAE,CAACC,QAAQ,EAAEC,gBAAgB,EAAEH,QAAQ,EAAEC,QAAQ,CAAC,CAAC;;EAEpD;EACA;EACA;EACA,IAAIG,eAAe,CAACG,IAAI,KAAKC,SAAS,IAAIT,SAAS,KAAK,KAAK,EAAE;IAC7DK,eAAe,CAACG,IAAI,GAAG,YAAY;EACrC;EACA,IAAIE,WAAW,GAAG,GAAGnB,QAAQ,SAAS;EACtC,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAAEiB,WAAW,GAAG,GAAGA,WAAW,IAAIjB,MAAM,EAAE;EACxE,MAAMkB,aAAa,GAAGpC,OAAO,CAAC,OAAO;IACnC2B,QAAQ,EAAEA,CAAA,KAAMA,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,CAACD,QAAQ,CAAC;IAC/DV,QAAQ;IACRU,QAAQ,EAAE,CAAC,CAACA,QAAQ;IACpBR;EACF,CAAC,CAAC,EAAE,CAACF,QAAQ,EAAEU,QAAQ,EAAER,MAAM,EAAES,QAAQ,CAAC,CAAC;EAC3C,OAAO,aAAahB,IAAI,CAACH,aAAa,CAAC6B,QAAQ,EAAE;IAC/CC,KAAK,EAAEF,aAAa;IACpBG,QAAQ,EAAE,aAAa5B,IAAI,CAACV,iBAAiB,CAACoC,QAAQ,EAAE;MACtDC,KAAK,EAAEP,cAAc;MACrBQ,QAAQ,EAAE,aAAa5B,IAAI,CAACc,SAAS,EAAE;QACrCV,GAAG,EAAEA,GAAG;QACR,GAAGe,eAAe;QAClBP,SAAS,EAAE1B,UAAU,CAAC0B,SAAS,EAAEP,QAAQ,EAAEE,MAAM,IAAIiB,WAAW,EAAEhB,OAAO,IAAI,GAAGH,QAAQ,IAAIG,OAAO,EAAE,EAAEC,EAAE,IAAI,MAAMA,EAAE,EAAE,EAAEE,MAAM,IAAI,UAAUA,MAAM,EAAE,EAAED,KAAK,IAAI,SAASA,KAAK,EAAE;MAClL,CAAC;IACH,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFT,MAAM,CAAC4B,WAAW,GAAG,QAAQ;AAC7B,eAAeC,MAAM,CAACC,MAAM,CAAC9B,MAAM,EAAE;EACnC+B,KAAK,EAAExC,WAAW;EAClByC,QAAQ,EAAExC,cAAc;EACxByC,SAAS,EAAEvC,eAAe;EAC1BwC,IAAI,EAAErC,UAAU;EAChBsC,MAAM,EAAE1C;AACV,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}