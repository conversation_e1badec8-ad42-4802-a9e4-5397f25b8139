{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\LaravelReact\\\\frontend-ui\\\\src\\\\shop\\\\SingleProduct.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useParams, Link } from \"react-router-dom\";\nimport PageHeader from \"../components/PageHeader\";\nimport Loading from \"../components/Loading\";\nimport { get, getImageUrl } from \"../utilis/apiService\";\n\n// Import Swiper React Components\nimport { Swiper, SwiperSlide } from \"swiper/react\";\n\n// Import Swiper styles\nimport \"swiper/css\";\nimport \"swiper/css/navigation\";\nimport { Navigation, Autoplay } from \"swiper/modules\";\nimport ProductDisplay from \"./ProductDisplay\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SingleProduct = () => {\n  _s();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [relatedProducts, setRelatedProducts] = useState([]);\n  const [error, setError] = useState(null);\n  const {\n    id\n  } = useParams();\n  useEffect(() => {\n    const getProduct = async () => {\n      try {\n        setLoading(true);\n        // Fetch the specific book by ID using API service\n        const result = await get(`books/${id}`);\n        if (result.success && result.data) {\n          var _result$data$book_det;\n          // Format the data to match the expected structure\n          const formattedProduct = {\n            id: result.data.BookID,\n            title: result.data.Title,\n            price: result.data.Price,\n            brand: result.data.Author,\n            stock: result.data.StockQuantity,\n            description: ((_result$data$book_det = result.data.book_detail) === null || _result$data$book_det === void 0 ? void 0 : _result$data$book_det.Description) || \"No description available\",\n            // Store the BookID for image path construction\n            BookID: result.data.BookID,\n            category_id: result.data.CategoryID\n          };\n          setProduct(formattedProduct);\n\n          // Fetch related books using API service\n          try {\n            const relatedResult = await get(`books/${id}/related`);\n            if (relatedResult.success && relatedResult.data) {\n              // Format related books\n              const formattedRelated = relatedResult.data.map(book => ({\n                id: book.BookID,\n                title: book.Title,\n                price: book.Price,\n                brand: book.Author,\n                stock: book.StockQuantity,\n                // Store BookID for image path\n                BookID: book.BookID,\n                category_id: book.CategoryID\n              })).slice(0, 4);\n              setRelatedProducts(formattedRelated);\n            }\n          } catch (relatedError) {\n            console.error(\"Error fetching related products:\", relatedError);\n            // If related books API fails, fetch all books and filter\n            try {\n              const allBooksResult = await get(\"books\");\n              if (allBooksResult && Array.isArray(allBooksResult.data)) {\n                const related = allBooksResult.data.filter(book => book.BookID !== parseInt(id) && (book.Author === formattedProduct.brand || book.CategoryID === formattedProduct.category_id)).map(book => ({\n                  id: book.BookID,\n                  title: book.Title,\n                  price: book.Price,\n                  brand: book.Author,\n                  stock: book.StockQuantity,\n                  // Store BookID for image path\n                  BookID: book.BookID,\n                  category_id: book.CategoryID\n                })).slice(0, 4);\n                setRelatedProducts(related);\n              }\n            } catch (allBooksError) {\n              console.error(\"Error fetching all books:\", allBooksError);\n            }\n          }\n        } else {\n          setError(\"Product data not found\");\n        }\n      } catch (error) {\n        console.error(\"Error fetching product:\", error);\n        setError(\"Error loading product\");\n      } finally {\n        setLoading(false);\n      }\n    };\n    getProduct();\n    // Scroll to top when component mounts\n    window.scrollTo(0, 0);\n  }, [id]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 12\n    }, this);\n  }\n  if (error || !product) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center p-5\",\n      children: /*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-secondary\",\n        children: error || \"Book not found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(PageHeader, {\n      title: \"Book Details\",\n      curPage: \"Single Product\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"shop-single padding-tb aside-bg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row justify-content-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-8 col-12\",\n            children: /*#__PURE__*/_jsxDEV(\"article\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"product-details\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"row align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-md-6 col-12\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"product-thumb\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"swiper-container pro-single-top\",\n                        children: [/*#__PURE__*/_jsxDEV(Swiper, {\n                          spaceBetween: 30,\n                          slidesPerView: 1,\n                          loop: true,\n                          autoplay: {\n                            delay: 2000,\n                            disableOnInteraction: false\n                          },\n                          navigation: {\n                            prevEl: \".pro-single-prev\",\n                            nextEl: \".pro-single-next\"\n                          },\n                          modules: [Navigation, Autoplay],\n                          className: \"mySwiper\",\n                          children: /*#__PURE__*/_jsxDEV(SwiperSlide, {\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"single-thumb\",\n                              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                                src: getImageUrl(`books/${product.BookID}/image`),\n                                alt: product.title,\n                                className: \"img-fluid rounded\",\n                                onError: e => {\n                                  e.target.src = \"/assets/images/product-placeholder.png\";\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 160,\n                                columnNumber: 33\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 159,\n                              columnNumber: 31\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 158,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 143,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"pro-single-prev\",\n                          children: /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"icofont-rounded-left\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 177,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 176,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"pro-single-next\",\n                          children: /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"icofont-rounded-right\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 180,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 179,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 141,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 140,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-md-6 col-12\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"post-content\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: product && /*#__PURE__*/_jsxDEV(ProductDisplay, {\n                          item: product\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 188,\n                          columnNumber: 39\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 187,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 185,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review mt-5\",\n                children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"review-nav lab-ul nav nav-pills\",\n                  id: \"pills-tab\",\n                  role: \"tablist\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"nav-item\",\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      className: \"nav-link active\",\n                      id: \"pills-desc-tab\",\n                      \"data-bs-toggle\": \"pill\",\n                      href: \"#pills-desc\",\n                      role: \"tab\",\n                      \"aria-controls\": \"pills-desc\",\n                      \"aria-selected\": \"true\",\n                      children: \"Description\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"nav-item\",\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      className: \"nav-link\",\n                      id: \"pills-info-tab\",\n                      \"data-bs-toggle\": \"pill\",\n                      href: \"#pills-info\",\n                      role: \"tab\",\n                      \"aria-controls\": \"pills-info\",\n                      \"aria-selected\": \"false\",\n                      children: \"Additional Info\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"review-content tab-content\",\n                  id: \"pills-tabContent\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"review-desc tab-pane fade show active\",\n                    id: \"pills-desc\",\n                    role: \"tabpanel\",\n                    \"aria-labelledby\": \"pills-desc-tab\",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: product.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 241,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"review-desc tab-pane fade\",\n                    id: \"pills-info\",\n                    role: \"tabpanel\",\n                    \"aria-labelledby\": \"pills-info-tab\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"review-desc-content\",\n                      children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                        className: \"lab-ul\",\n                        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            children: \"Author:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 252,\n                            columnNumber: 29\n                          }, this), \" \", product.brand]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 251,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            children: \"ISBN:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 255,\n                            columnNumber: 29\n                          }, this), \" \", product.id + 978000000000]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 254,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            children: \"Language:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 258,\n                            columnNumber: 29\n                          }, this), \" English\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 257,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            children: \"Publisher:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 261,\n                            columnNumber: 29\n                          }, this), \" Kinokuniya Publishing\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 260,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            children: \"Pages:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 264,\n                            columnNumber: 29\n                          }, this), \" \", Math.floor(Math.random() * 400) + 100]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 263,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            children: \"Availability:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 268,\n                            columnNumber: 29\n                          }, this), \" \", product.stock > 0 ? \"In Stock\" : \"Out of Stock\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 267,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 250,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 249,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this), relatedProducts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"related-products mt-5\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"title-border\",\n                  children: \"Related Books\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"row\",\n                  children: relatedProducts.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-lg-3 col-md-6 col-12\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"product-item\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"product-thumb\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"pro-thumb\",\n                          children: /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: getImageUrl(`books/${item.BookID}/image`),\n                            alt: item.title,\n                            onError: e => {\n                              e.target.src = \"/assets/images/product-placeholder.png\";\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 287,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 286,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"product-action-link\",\n                          children: /*#__PURE__*/_jsxDEV(Link, {\n                            to: `/shop/${item.id}`,\n                            children: /*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"icofont-eye\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 300,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 299,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 298,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 285,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"product-content\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                          children: /*#__PURE__*/_jsxDEV(Link, {\n                            to: `/shop/${item.id}`,\n                            children: [item.title.substring(0, 20), \"...\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 306,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 305,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                          children: [\"$\", item.price]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 310,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 304,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 284,\n                      columnNumber: 27\n                    }, this)\n                  }, item.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-4 col-12\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sidebar-item\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sidebar-inner\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"sidebar-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"product-details-tags\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"title\",\n                      children: \"Categories\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 327,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                      className: \"lab-ul\",\n                      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          to: \"/shop\",\n                          children: \"Fiction\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 330,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 329,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          to: \"/shop\",\n                          children: \"Non-Fiction\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 333,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 332,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          to: \"/shop\",\n                          children: \"Children's Books\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 336,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 335,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          to: \"/shop\",\n                          children: \"Textbooks\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 339,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 338,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          to: \"/shop\",\n                          children: \"Manga & Comics\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 342,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 341,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 328,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"product-details-tags mt-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"title\",\n                      children: \"Popular Authors\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 348,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                      className: \"lab-ul\",\n                      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          to: \"/shop\",\n                          children: \"J.K. Rowling\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 351,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 350,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          to: \"/shop\",\n                          children: \"Haruki Murakami\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 354,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 353,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          to: \"/shop\",\n                          children: \"Stephen King\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 357,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 356,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          to: \"/shop\",\n                          children: \"Jane Austen\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 360,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 359,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          to: \"/shop\",\n                          children: \"George R.R. Martin\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 363,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 362,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 349,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 129,\n    columnNumber: 5\n  }, this);\n};\n_s(SingleProduct, \"/VP67Fm/lH7kbWeFaX3UKRO5NMM=\", false, function () {\n  return [useParams];\n});\n_c = SingleProduct;\nexport default SingleProduct;\nvar _c;\n$RefreshReg$(_c, \"SingleProduct\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "Link", "<PERSON><PERSON><PERSON><PERSON>", "Loading", "get", "getImageUrl", "Swiper", "SwiperSlide", "Navigation", "Autoplay", "ProductDisplay", "jsxDEV", "_jsxDEV", "SingleProduct", "_s", "product", "setProduct", "loading", "setLoading", "relatedProducts", "setRelatedProducts", "error", "setError", "id", "getProduct", "result", "success", "data", "_result$data$book_det", "formattedProduct", "BookID", "title", "Title", "price", "Price", "brand", "Author", "stock", "StockQuantity", "description", "book_detail", "Description", "category_id", "CategoryID", "relatedResult", "formattedRelated", "map", "book", "slice", "relatedError", "console", "allBooksResult", "Array", "isArray", "related", "filter", "parseInt", "allBooksError", "window", "scrollTo", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "curPage", "spaceBetween", "<PERSON><PERSON><PERSON><PERSON>iew", "loop", "autoplay", "delay", "disableOnInteraction", "navigation", "prevEl", "nextEl", "modules", "src", "alt", "onError", "e", "target", "item", "role", "href", "Math", "floor", "random", "length", "to", "substring", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/shop/SingleProduct.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { use<PERSON><PERSON><PERSON>, <PERSON> } from \"react-router-dom\";\nimport PageHeader from \"../components/PageHeader\";\nimport Loading from \"../components/Loading\";\nimport { get, getImageUrl } from \"../utilis/apiService\";\n\n// Import Swiper React Components\nimport { Swiper, SwiperSlide } from \"swiper/react\";\n\n// Import Swiper styles\nimport \"swiper/css\";\nimport \"swiper/css/navigation\";\nimport { Navigation, Autoplay } from \"swiper/modules\";\n\nimport ProductDisplay from \"./ProductDisplay\";\n\nconst SingleProduct = () => {\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [relatedProducts, setRelatedProducts] = useState([]);\n  const [error, setError] = useState(null);\n  const { id } = useParams();\n\n  useEffect(() => {\n    const getProduct = async () => {\n      try {\n        setLoading(true);\n        // Fetch the specific book by ID using API service\n        const result = await get(`books/${id}`);\n\n        if (result.success && result.data) {\n          // Format the data to match the expected structure\n          const formattedProduct = {\n            id: result.data.BookID,\n            title: result.data.Title,\n            price: result.data.Price,\n            brand: result.data.Author,\n            stock: result.data.StockQuantity,\n            description:\n              result.data.book_detail?.Description ||\n              \"No description available\",\n            // Store the BookID for image path construction\n            BookID: result.data.BookID,\n            category_id: result.data.CategoryID,\n          };\n\n          setProduct(formattedProduct);\n\n          // Fetch related books using API service\n          try {\n            const relatedResult = await get(`books/${id}/related`);\n            if (relatedResult.success && relatedResult.data) {\n              // Format related books\n              const formattedRelated = relatedResult.data\n                .map((book) => ({\n                  id: book.BookID,\n                  title: book.Title,\n                  price: book.Price,\n                  brand: book.Author,\n                  stock: book.StockQuantity,\n                  // Store BookID for image path\n                  BookID: book.BookID,\n                  category_id: book.CategoryID,\n                }))\n                .slice(0, 4);\n\n              setRelatedProducts(formattedRelated);\n            }\n          } catch (relatedError) {\n            console.error(\"Error fetching related products:\", relatedError);\n            // If related books API fails, fetch all books and filter\n            try {\n              const allBooksResult = await get(\"books\");\n              if (allBooksResult && Array.isArray(allBooksResult.data)) {\n                const related = allBooksResult.data\n                  .filter(\n                    (book) =>\n                      book.BookID !== parseInt(id) &&\n                      (book.Author === formattedProduct.brand ||\n                        book.CategoryID === formattedProduct.category_id)\n                  )\n                  .map((book) => ({\n                    id: book.BookID,\n                    title: book.Title,\n                    price: book.Price,\n                    brand: book.Author,\n                    stock: book.StockQuantity,\n                    // Store BookID for image path\n                    BookID: book.BookID,\n                    category_id: book.CategoryID,\n                  }))\n                  .slice(0, 4);\n\n                setRelatedProducts(related);\n              }\n            } catch (allBooksError) {\n              console.error(\"Error fetching all books:\", allBooksError);\n            }\n          }\n        } else {\n          setError(\"Product data not found\");\n        }\n      } catch (error) {\n        console.error(\"Error fetching product:\", error);\n        setError(\"Error loading product\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    getProduct();\n    // Scroll to top when component mounts\n    window.scrollTo(0, 0);\n  }, [id]);\n\n  if (loading) {\n    return <Loading />;\n  }\n\n  if (error || !product) {\n    return (\n      <div className=\"text-center p-5\">\n        <h4 className=\"text-secondary\">{error || \"Book not found\"}</h4>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <PageHeader title={\"Book Details\"} curPage={\"Single Product\"} />\n      <div className=\"shop-single padding-tb aside-bg\">\n        <div className=\"container\">\n          <div className=\"row justify-content-center\">\n            {/* left side */}\n            <div className=\"col-lg-8 col-12\">\n              <article>\n                <div className=\"product-details\">\n                  <div className=\"row align-items-center\">\n                    <div className=\"col-md-6 col-12\">\n                      <div className=\"product-thumb\">\n                        <div className=\"swiper-container pro-single-top\">\n                          {/* Swiper to display all images */}\n                          <Swiper\n                            spaceBetween={30}\n                            slidesPerView={1}\n                            loop={true}\n                            autoplay={{\n                              delay: 2000,\n                              disableOnInteraction: false,\n                            }}\n                            navigation={{\n                              prevEl: \".pro-single-prev\",\n                              nextEl: \".pro-single-next\",\n                            }}\n                            modules={[Navigation, Autoplay]}\n                            className=\"mySwiper\"\n                          >\n                            <SwiperSlide>\n                              <div className=\"single-thumb\">\n                                <img\n                                  src={getImageUrl(\n                                    `books/${product.BookID}/image`\n                                  )}\n                                  alt={product.title}\n                                  className=\"img-fluid rounded\"\n                                  onError={(e) => {\n                                    e.target.src =\n                                      \"/assets/images/product-placeholder.png\";\n                                  }}\n                                />\n                              </div>\n                            </SwiperSlide>\n                          </Swiper>\n\n                          {/* Navigation buttons */}\n                          <div className=\"pro-single-prev\">\n                            <i className=\"icofont-rounded-left\"></i>\n                          </div>\n                          <div className=\"pro-single-next\">\n                            <i className=\"icofont-rounded-right\"></i>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"col-md-6 col-12\">\n                      <div className=\"post-content\">\n                        <div>\n                          {product && <ProductDisplay item={product} />}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Detailed Info Section */}\n                <div className=\"review mt-5\">\n                  <ul\n                    className=\"review-nav lab-ul nav nav-pills\"\n                    id=\"pills-tab\"\n                    role=\"tablist\"\n                  >\n                    <li className=\"nav-item\">\n                      <a\n                        className=\"nav-link active\"\n                        id=\"pills-desc-tab\"\n                        data-bs-toggle=\"pill\"\n                        href=\"#pills-desc\"\n                        role=\"tab\"\n                        aria-controls=\"pills-desc\"\n                        aria-selected=\"true\"\n                      >\n                        Description\n                      </a>\n                    </li>\n                    <li className=\"nav-item\">\n                      <a\n                        className=\"nav-link\"\n                        id=\"pills-info-tab\"\n                        data-bs-toggle=\"pill\"\n                        href=\"#pills-info\"\n                        role=\"tab\"\n                        aria-controls=\"pills-info\"\n                        aria-selected=\"false\"\n                      >\n                        Additional Info\n                      </a>\n                    </li>\n                  </ul>\n\n                  {/* Tab Content */}\n                  <div\n                    className=\"review-content tab-content\"\n                    id=\"pills-tabContent\"\n                  >\n                    <div\n                      className=\"review-desc tab-pane fade show active\"\n                      id=\"pills-desc\"\n                      role=\"tabpanel\"\n                      aria-labelledby=\"pills-desc-tab\"\n                    >\n                      <p>{product.description}</p>\n                    </div>\n                    <div\n                      className=\"review-desc tab-pane fade\"\n                      id=\"pills-info\"\n                      role=\"tabpanel\"\n                      aria-labelledby=\"pills-info-tab\"\n                    >\n                      <div className=\"review-desc-content\">\n                        <ul className=\"lab-ul\">\n                          <li>\n                            <span>Author:</span> {product.brand}\n                          </li>\n                          <li>\n                            <span>ISBN:</span> {product.id + 978000000000}\n                          </li>\n                          <li>\n                            <span>Language:</span> English\n                          </li>\n                          <li>\n                            <span>Publisher:</span> Kinokuniya Publishing\n                          </li>\n                          <li>\n                            <span>Pages:</span>{\" \"}\n                            {Math.floor(Math.random() * 400) + 100}\n                          </li>\n                          <li>\n                            <span>Availability:</span>{\" \"}\n                            {product.stock > 0 ? \"In Stock\" : \"Out of Stock\"}\n                          </li>\n                        </ul>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Related Products */}\n                {relatedProducts.length > 0 && (\n                  <div className=\"related-products mt-5\">\n                    <h4 className=\"title-border\">Related Books</h4>\n                    <div className=\"row\">\n                      {relatedProducts.map((item) => (\n                        <div key={item.id} className=\"col-lg-3 col-md-6 col-12\">\n                          <div className=\"product-item\">\n                            <div className=\"product-thumb\">\n                              <div className=\"pro-thumb\">\n                                <img\n                                  src={getImageUrl(\n                                    `books/${item.BookID}/image`\n                                  )}\n                                  alt={item.title}\n                                  onError={(e) => {\n                                    e.target.src =\n                                      \"/assets/images/product-placeholder.png\";\n                                  }}\n                                />\n                              </div>\n                              <div className=\"product-action-link\">\n                                <Link to={`/shop/${item.id}`}>\n                                  <i className=\"icofont-eye\"></i>\n                                </Link>\n                              </div>\n                            </div>\n                            <div className=\"product-content\">\n                              <h5>\n                                <Link to={`/shop/${item.id}`}>\n                                  {item.title.substring(0, 20)}...\n                                </Link>\n                              </h5>\n                              <h6>${item.price}</h6>\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </article>\n            </div>\n\n            {/* Right side */}\n            <div className=\"col-lg-4 col-12\">\n              <div className=\"sidebar-item\">\n                <div className=\"sidebar-inner\">\n                  <div className=\"sidebar-content\">\n                    <div className=\"product-details-tags\">\n                      <h5 className=\"title\">Categories</h5>\n                      <ul className=\"lab-ul\">\n                        <li>\n                          <Link to=\"/shop\">Fiction</Link>\n                        </li>\n                        <li>\n                          <Link to=\"/shop\">Non-Fiction</Link>\n                        </li>\n                        <li>\n                          <Link to=\"/shop\">Children's Books</Link>\n                        </li>\n                        <li>\n                          <Link to=\"/shop\">Textbooks</Link>\n                        </li>\n                        <li>\n                          <Link to=\"/shop\">Manga & Comics</Link>\n                        </li>\n                      </ul>\n                    </div>\n\n                    <div className=\"product-details-tags mt-4\">\n                      <h5 className=\"title\">Popular Authors</h5>\n                      <ul className=\"lab-ul\">\n                        <li>\n                          <Link to=\"/shop\">J.K. Rowling</Link>\n                        </li>\n                        <li>\n                          <Link to=\"/shop\">Haruki Murakami</Link>\n                        </li>\n                        <li>\n                          <Link to=\"/shop\">Stephen King</Link>\n                        </li>\n                        <li>\n                          <Link to=\"/shop\">Jane Austen</Link>\n                        </li>\n                        <li>\n                          <Link to=\"/shop\">George R.R. Martin</Link>\n                        </li>\n                      </ul>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SingleProduct;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,QAAQ,kBAAkB;AAClD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,SAASC,GAAG,EAAEC,WAAW,QAAQ,sBAAsB;;AAEvD;AACA,SAASC,MAAM,EAAEC,WAAW,QAAQ,cAAc;;AAElD;AACA,OAAO,YAAY;AACnB,OAAO,uBAAuB;AAC9B,SAASC,UAAU,EAAEC,QAAQ,QAAQ,gBAAgB;AAErD,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,eAAe,EAAEC,kBAAkB,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM;IAAEwB;EAAG,CAAC,GAAGvB,SAAS,CAAC,CAAC;EAE1BF,SAAS,CAAC,MAAM;IACd,MAAM0B,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI;QACFN,UAAU,CAAC,IAAI,CAAC;QAChB;QACA,MAAMO,MAAM,GAAG,MAAMrB,GAAG,CAAC,SAASmB,EAAE,EAAE,CAAC;QAEvC,IAAIE,MAAM,CAACC,OAAO,IAAID,MAAM,CAACE,IAAI,EAAE;UAAA,IAAAC,qBAAA;UACjC;UACA,MAAMC,gBAAgB,GAAG;YACvBN,EAAE,EAAEE,MAAM,CAACE,IAAI,CAACG,MAAM;YACtBC,KAAK,EAAEN,MAAM,CAACE,IAAI,CAACK,KAAK;YACxBC,KAAK,EAAER,MAAM,CAACE,IAAI,CAACO,KAAK;YACxBC,KAAK,EAAEV,MAAM,CAACE,IAAI,CAACS,MAAM;YACzBC,KAAK,EAAEZ,MAAM,CAACE,IAAI,CAACW,aAAa;YAChCC,WAAW,EACT,EAAAX,qBAAA,GAAAH,MAAM,CAACE,IAAI,CAACa,WAAW,cAAAZ,qBAAA,uBAAvBA,qBAAA,CAAyBa,WAAW,KACpC,0BAA0B;YAC5B;YACAX,MAAM,EAAEL,MAAM,CAACE,IAAI,CAACG,MAAM;YAC1BY,WAAW,EAAEjB,MAAM,CAACE,IAAI,CAACgB;UAC3B,CAAC;UAED3B,UAAU,CAACa,gBAAgB,CAAC;;UAE5B;UACA,IAAI;YACF,MAAMe,aAAa,GAAG,MAAMxC,GAAG,CAAC,SAASmB,EAAE,UAAU,CAAC;YACtD,IAAIqB,aAAa,CAAClB,OAAO,IAAIkB,aAAa,CAACjB,IAAI,EAAE;cAC/C;cACA,MAAMkB,gBAAgB,GAAGD,aAAa,CAACjB,IAAI,CACxCmB,GAAG,CAAEC,IAAI,KAAM;gBACdxB,EAAE,EAAEwB,IAAI,CAACjB,MAAM;gBACfC,KAAK,EAAEgB,IAAI,CAACf,KAAK;gBACjBC,KAAK,EAAEc,IAAI,CAACb,KAAK;gBACjBC,KAAK,EAAEY,IAAI,CAACX,MAAM;gBAClBC,KAAK,EAAEU,IAAI,CAACT,aAAa;gBACzB;gBACAR,MAAM,EAAEiB,IAAI,CAACjB,MAAM;gBACnBY,WAAW,EAAEK,IAAI,CAACJ;cACpB,CAAC,CAAC,CAAC,CACFK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;cAEd5B,kBAAkB,CAACyB,gBAAgB,CAAC;YACtC;UACF,CAAC,CAAC,OAAOI,YAAY,EAAE;YACrBC,OAAO,CAAC7B,KAAK,CAAC,kCAAkC,EAAE4B,YAAY,CAAC;YAC/D;YACA,IAAI;cACF,MAAME,cAAc,GAAG,MAAM/C,GAAG,CAAC,OAAO,CAAC;cACzC,IAAI+C,cAAc,IAAIC,KAAK,CAACC,OAAO,CAACF,cAAc,CAACxB,IAAI,CAAC,EAAE;gBACxD,MAAM2B,OAAO,GAAGH,cAAc,CAACxB,IAAI,CAChC4B,MAAM,CACJR,IAAI,IACHA,IAAI,CAACjB,MAAM,KAAK0B,QAAQ,CAACjC,EAAE,CAAC,KAC3BwB,IAAI,CAACX,MAAM,KAAKP,gBAAgB,CAACM,KAAK,IACrCY,IAAI,CAACJ,UAAU,KAAKd,gBAAgB,CAACa,WAAW,CACtD,CAAC,CACAI,GAAG,CAAEC,IAAI,KAAM;kBACdxB,EAAE,EAAEwB,IAAI,CAACjB,MAAM;kBACfC,KAAK,EAAEgB,IAAI,CAACf,KAAK;kBACjBC,KAAK,EAAEc,IAAI,CAACb,KAAK;kBACjBC,KAAK,EAAEY,IAAI,CAACX,MAAM;kBAClBC,KAAK,EAAEU,IAAI,CAACT,aAAa;kBACzB;kBACAR,MAAM,EAAEiB,IAAI,CAACjB,MAAM;kBACnBY,WAAW,EAAEK,IAAI,CAACJ;gBACpB,CAAC,CAAC,CAAC,CACFK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;gBAEd5B,kBAAkB,CAACkC,OAAO,CAAC;cAC7B;YACF,CAAC,CAAC,OAAOG,aAAa,EAAE;cACtBP,OAAO,CAAC7B,KAAK,CAAC,2BAA2B,EAAEoC,aAAa,CAAC;YAC3D;UACF;QACF,CAAC,MAAM;UACLnC,QAAQ,CAAC,wBAAwB,CAAC;QACpC;MACF,CAAC,CAAC,OAAOD,KAAK,EAAE;QACd6B,OAAO,CAAC7B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/CC,QAAQ,CAAC,uBAAuB,CAAC;MACnC,CAAC,SAAS;QACRJ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDM,UAAU,CAAC,CAAC;IACZ;IACAkC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACvB,CAAC,EAAE,CAACpC,EAAE,CAAC,CAAC;EAER,IAAIN,OAAO,EAAE;IACX,oBAAOL,OAAA,CAACT,OAAO;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpB;EAEA,IAAI1C,KAAK,IAAI,CAACN,OAAO,EAAE;IACrB,oBACEH,OAAA;MAAKoD,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BrD,OAAA;QAAIoD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAE5C,KAAK,IAAI;MAAgB;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC;EAEV;EAEA,oBACEnD,OAAA;IAAAqD,QAAA,gBACErD,OAAA,CAACV,UAAU;MAAC6B,KAAK,EAAE,cAAe;MAACmC,OAAO,EAAE;IAAiB;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChEnD,OAAA;MAAKoD,SAAS,EAAC,iCAAiC;MAAAC,QAAA,eAC9CrD,OAAA;QAAKoD,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBrD,OAAA;UAAKoD,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBAEzCrD,OAAA;YAAKoD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BrD,OAAA;cAAAqD,QAAA,gBACErD,OAAA;gBAAKoD,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,eAC9BrD,OAAA;kBAAKoD,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCrD,OAAA;oBAAKoD,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,eAC9BrD,OAAA;sBAAKoD,SAAS,EAAC,eAAe;sBAAAC,QAAA,eAC5BrD,OAAA;wBAAKoD,SAAS,EAAC,iCAAiC;wBAAAC,QAAA,gBAE9CrD,OAAA,CAACN,MAAM;0BACL6D,YAAY,EAAE,EAAG;0BACjBC,aAAa,EAAE,CAAE;0BACjBC,IAAI,EAAE,IAAK;0BACXC,QAAQ,EAAE;4BACRC,KAAK,EAAE,IAAI;4BACXC,oBAAoB,EAAE;0BACxB,CAAE;0BACFC,UAAU,EAAE;4BACVC,MAAM,EAAE,kBAAkB;4BAC1BC,MAAM,EAAE;0BACV,CAAE;0BACFC,OAAO,EAAE,CAACpE,UAAU,EAAEC,QAAQ,CAAE;0BAChCuD,SAAS,EAAC,UAAU;0BAAAC,QAAA,eAEpBrD,OAAA,CAACL,WAAW;4BAAA0D,QAAA,eACVrD,OAAA;8BAAKoD,SAAS,EAAC,cAAc;8BAAAC,QAAA,eAC3BrD,OAAA;gCACEiE,GAAG,EAAExE,WAAW,CACd,SAASU,OAAO,CAACe,MAAM,QACzB,CAAE;gCACFgD,GAAG,EAAE/D,OAAO,CAACgB,KAAM;gCACnBiC,SAAS,EAAC,mBAAmB;gCAC7Be,OAAO,EAAGC,CAAC,IAAK;kCACdA,CAAC,CAACC,MAAM,CAACJ,GAAG,GACV,wCAAwC;gCAC5C;8BAAE;gCAAAjB,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACK;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACR,CAAC,eAGTnD,OAAA;0BAAKoD,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,eAC9BrD,OAAA;4BAAGoD,SAAS,EAAC;0BAAsB;4BAAAJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrC,CAAC,eACNnD,OAAA;0BAAKoD,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,eAC9BrD,OAAA;4BAAGoD,SAAS,EAAC;0BAAuB;4BAAAJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNnD,OAAA;oBAAKoD,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,eAC9BrD,OAAA;sBAAKoD,SAAS,EAAC,cAAc;sBAAAC,QAAA,eAC3BrD,OAAA;wBAAAqD,QAAA,EACGlD,OAAO,iBAAIH,OAAA,CAACF,cAAc;0BAACwE,IAAI,EAAEnE;wBAAQ;0BAAA6C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNnD,OAAA;gBAAKoD,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BrD,OAAA;kBACEoD,SAAS,EAAC,iCAAiC;kBAC3CzC,EAAE,EAAC,WAAW;kBACd4D,IAAI,EAAC,SAAS;kBAAAlB,QAAA,gBAEdrD,OAAA;oBAAIoD,SAAS,EAAC,UAAU;oBAAAC,QAAA,eACtBrD,OAAA;sBACEoD,SAAS,EAAC,iBAAiB;sBAC3BzC,EAAE,EAAC,gBAAgB;sBACnB,kBAAe,MAAM;sBACrB6D,IAAI,EAAC,aAAa;sBAClBD,IAAI,EAAC,KAAK;sBACV,iBAAc,YAAY;sBAC1B,iBAAc,MAAM;sBAAAlB,QAAA,EACrB;oBAED;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLnD,OAAA;oBAAIoD,SAAS,EAAC,UAAU;oBAAAC,QAAA,eACtBrD,OAAA;sBACEoD,SAAS,EAAC,UAAU;sBACpBzC,EAAE,EAAC,gBAAgB;sBACnB,kBAAe,MAAM;sBACrB6D,IAAI,EAAC,aAAa;sBAClBD,IAAI,EAAC,KAAK;sBACV,iBAAc,YAAY;sBAC1B,iBAAc,OAAO;sBAAAlB,QAAA,EACtB;oBAED;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGLnD,OAAA;kBACEoD,SAAS,EAAC,4BAA4B;kBACtCzC,EAAE,EAAC,kBAAkB;kBAAA0C,QAAA,gBAErBrD,OAAA;oBACEoD,SAAS,EAAC,uCAAuC;oBACjDzC,EAAE,EAAC,YAAY;oBACf4D,IAAI,EAAC,UAAU;oBACf,mBAAgB,gBAAgB;oBAAAlB,QAAA,eAEhCrD,OAAA;sBAAAqD,QAAA,EAAIlD,OAAO,CAACwB;oBAAW;sBAAAqB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACNnD,OAAA;oBACEoD,SAAS,EAAC,2BAA2B;oBACrCzC,EAAE,EAAC,YAAY;oBACf4D,IAAI,EAAC,UAAU;oBACf,mBAAgB,gBAAgB;oBAAAlB,QAAA,eAEhCrD,OAAA;sBAAKoD,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,eAClCrD,OAAA;wBAAIoD,SAAS,EAAC,QAAQ;wBAAAC,QAAA,gBACpBrD,OAAA;0BAAAqD,QAAA,gBACErD,OAAA;4BAAAqD,QAAA,EAAM;0BAAO;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,KAAC,EAAChD,OAAO,CAACoB,KAAK;wBAAA;0BAAAyB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjC,CAAC,eACLnD,OAAA;0BAAAqD,QAAA,gBACErD,OAAA;4BAAAqD,QAAA,EAAM;0BAAK;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,KAAC,EAAChD,OAAO,CAACQ,EAAE,GAAG,YAAY;wBAAA;0BAAAqC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3C,CAAC,eACLnD,OAAA;0BAAAqD,QAAA,gBACErD,OAAA;4BAAAqD,QAAA,EAAM;0BAAS;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,YACxB;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACLnD,OAAA;0BAAAqD,QAAA,gBACErD,OAAA;4BAAAqD,QAAA,EAAM;0BAAU;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,0BACzB;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACLnD,OAAA;0BAAAqD,QAAA,gBACErD,OAAA;4BAAAqD,QAAA,EAAM;0BAAM;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,EAAC,GAAG,EACtBsB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG;wBAAA;0BAAA3B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpC,CAAC,eACLnD,OAAA;0BAAAqD,QAAA,gBACErD,OAAA;4BAAAqD,QAAA,EAAM;0BAAa;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,EAAC,GAAG,EAC7BhD,OAAO,CAACsB,KAAK,GAAG,CAAC,GAAG,UAAU,GAAG,cAAc;wBAAA;0BAAAuB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9C,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL5C,eAAe,CAACqE,MAAM,GAAG,CAAC,iBACzB5E,OAAA;gBAAKoD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCrD,OAAA;kBAAIoD,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAa;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/CnD,OAAA;kBAAKoD,SAAS,EAAC,KAAK;kBAAAC,QAAA,EACjB9C,eAAe,CAAC2B,GAAG,CAAEoC,IAAI,iBACxBtE,OAAA;oBAAmBoD,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,eACrDrD,OAAA;sBAAKoD,SAAS,EAAC,cAAc;sBAAAC,QAAA,gBAC3BrD,OAAA;wBAAKoD,SAAS,EAAC,eAAe;wBAAAC,QAAA,gBAC5BrD,OAAA;0BAAKoD,SAAS,EAAC,WAAW;0BAAAC,QAAA,eACxBrD,OAAA;4BACEiE,GAAG,EAAExE,WAAW,CACd,SAAS6E,IAAI,CAACpD,MAAM,QACtB,CAAE;4BACFgD,GAAG,EAAEI,IAAI,CAACnD,KAAM;4BAChBgD,OAAO,EAAGC,CAAC,IAAK;8BACdA,CAAC,CAACC,MAAM,CAACJ,GAAG,GACV,wCAAwC;4BAC5C;0BAAE;4BAAAjB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACNnD,OAAA;0BAAKoD,SAAS,EAAC,qBAAqB;0BAAAC,QAAA,eAClCrD,OAAA,CAACX,IAAI;4BAACwF,EAAE,EAAE,SAASP,IAAI,CAAC3D,EAAE,EAAG;4BAAA0C,QAAA,eAC3BrD,OAAA;8BAAGoD,SAAS,EAAC;4BAAa;8BAAAJ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3B;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNnD,OAAA;wBAAKoD,SAAS,EAAC,iBAAiB;wBAAAC,QAAA,gBAC9BrD,OAAA;0BAAAqD,QAAA,eACErD,OAAA,CAACX,IAAI;4BAACwF,EAAE,EAAE,SAASP,IAAI,CAAC3D,EAAE,EAAG;4BAAA0C,QAAA,GAC1BiB,IAAI,CAACnD,KAAK,CAAC2D,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAC,KAC/B;0BAAA;4BAAA9B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACLnD,OAAA;0BAAAqD,QAAA,GAAI,GAAC,EAACiB,IAAI,CAACjD,KAAK;wBAAA;0BAAA2B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GA7BEmB,IAAI,CAAC3D,EAAE;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA8BZ,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAGNnD,OAAA;YAAKoD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BrD,OAAA;cAAKoD,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BrD,OAAA;gBAAKoD,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5BrD,OAAA;kBAAKoD,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC9BrD,OAAA;oBAAKoD,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnCrD,OAAA;sBAAIoD,SAAS,EAAC,OAAO;sBAAAC,QAAA,EAAC;oBAAU;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrCnD,OAAA;sBAAIoD,SAAS,EAAC,QAAQ;sBAAAC,QAAA,gBACpBrD,OAAA;wBAAAqD,QAAA,eACErD,OAAA,CAACX,IAAI;0BAACwF,EAAE,EAAC,OAAO;0BAAAxB,QAAA,EAAC;wBAAO;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC,eACLnD,OAAA;wBAAAqD,QAAA,eACErD,OAAA,CAACX,IAAI;0BAACwF,EAAE,EAAC,OAAO;0BAAAxB,QAAA,EAAC;wBAAW;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC,eACLnD,OAAA;wBAAAqD,QAAA,eACErD,OAAA,CAACX,IAAI;0BAACwF,EAAE,EAAC,OAAO;0BAAAxB,QAAA,EAAC;wBAAgB;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC,eACLnD,OAAA;wBAAAqD,QAAA,eACErD,OAAA,CAACX,IAAI;0BAACwF,EAAE,EAAC,OAAO;0BAAAxB,QAAA,EAAC;wBAAS;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B,CAAC,eACLnD,OAAA;wBAAAqD,QAAA,eACErD,OAAA,CAACX,IAAI;0BAACwF,EAAE,EAAC,OAAO;0BAAAxB,QAAA,EAAC;wBAAc;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAENnD,OAAA;oBAAKoD,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,gBACxCrD,OAAA;sBAAIoD,SAAS,EAAC,OAAO;sBAAAC,QAAA,EAAC;oBAAe;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1CnD,OAAA;sBAAIoD,SAAS,EAAC,QAAQ;sBAAAC,QAAA,gBACpBrD,OAAA;wBAAAqD,QAAA,eACErD,OAAA,CAACX,IAAI;0BAACwF,EAAE,EAAC,OAAO;0BAAAxB,QAAA,EAAC;wBAAY;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC,eACLnD,OAAA;wBAAAqD,QAAA,eACErD,OAAA,CAACX,IAAI;0BAACwF,EAAE,EAAC,OAAO;0BAAAxB,QAAA,EAAC;wBAAe;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CAAC,eACLnD,OAAA;wBAAAqD,QAAA,eACErD,OAAA,CAACX,IAAI;0BAACwF,EAAE,EAAC,OAAO;0BAAAxB,QAAA,EAAC;wBAAY;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC,eACLnD,OAAA;wBAAAqD,QAAA,eACErD,OAAA,CAACX,IAAI;0BAACwF,EAAE,EAAC,OAAO;0BAAAxB,QAAA,EAAC;wBAAW;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC,eACLnD,OAAA;wBAAAqD,QAAA,eACErD,OAAA,CAACX,IAAI;0BAACwF,EAAE,EAAC,OAAO;0BAAAxB,QAAA,EAAC;wBAAkB;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjD,EAAA,CAvWID,aAAa;EAAA,QAKFb,SAAS;AAAA;AAAA2F,EAAA,GALpB9E,aAAa;AAyWnB,eAAeA,aAAa;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}