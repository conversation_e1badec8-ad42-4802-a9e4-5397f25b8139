{"ast": null, "code": "import { g as getDocument } from '../shared/ssr-window.esm.mjs';\n\n/* eslint no-underscore-dangle: \"off\" */\n/* eslint no-use-before-define: \"off\" */\nfunction Autoplay(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit,\n    params\n  } = _ref;\n  swiper.autoplay = {\n    running: false,\n    paused: false,\n    timeLeft: 0\n  };\n  extendParams({\n    autoplay: {\n      enabled: false,\n      delay: 3000,\n      waitForTransition: true,\n      disableOnInteraction: false,\n      stopOnLastSlide: false,\n      reverseDirection: false,\n      pauseOnMouseEnter: false\n    }\n  });\n  let timeout;\n  let raf;\n  let autoplayDelayTotal = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayDelayCurrent = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayTimeLeft;\n  let autoplayStartTime = new Date().getTime();\n  let wasPaused;\n  let isTouched;\n  let pausedByTouch;\n  let touchStartTimeout;\n  let slideChanged;\n  let pausedByInteraction;\n  let pausedByPointerEnter;\n  function onTransitionEnd(e) {\n    if (!swiper || swiper.destroyed || !swiper.wrapperEl) return;\n    if (e.target !== swiper.wrapperEl) return;\n    swiper.wrapperEl.removeEventListener('transitionend', onTransitionEnd);\n    if (pausedByPointerEnter || e.detail && e.detail.bySwiperTouchMove) {\n      return;\n    }\n    resume();\n  }\n  const calcTimeLeft = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.autoplay.paused) {\n      wasPaused = true;\n    } else if (wasPaused) {\n      autoplayDelayCurrent = autoplayTimeLeft;\n      wasPaused = false;\n    }\n    const timeLeft = swiper.autoplay.paused ? autoplayTimeLeft : autoplayStartTime + autoplayDelayCurrent - new Date().getTime();\n    swiper.autoplay.timeLeft = timeLeft;\n    emit('autoplayTimeLeft', timeLeft, timeLeft / autoplayDelayTotal);\n    raf = requestAnimationFrame(() => {\n      calcTimeLeft();\n    });\n  };\n  const getSlideDelay = () => {\n    let activeSlideEl;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      activeSlideEl = swiper.slides.find(slideEl => slideEl.classList.contains('swiper-slide-active'));\n    } else {\n      activeSlideEl = swiper.slides[swiper.activeIndex];\n    }\n    if (!activeSlideEl) return undefined;\n    const currentSlideDelay = parseInt(activeSlideEl.getAttribute('data-swiper-autoplay'), 10);\n    return currentSlideDelay;\n  };\n  const run = delayForce => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    cancelAnimationFrame(raf);\n    calcTimeLeft();\n    let delay = typeof delayForce === 'undefined' ? swiper.params.autoplay.delay : delayForce;\n    autoplayDelayTotal = swiper.params.autoplay.delay;\n    autoplayDelayCurrent = swiper.params.autoplay.delay;\n    const currentSlideDelay = getSlideDelay();\n    if (!Number.isNaN(currentSlideDelay) && currentSlideDelay > 0 && typeof delayForce === 'undefined') {\n      delay = currentSlideDelay;\n      autoplayDelayTotal = currentSlideDelay;\n      autoplayDelayCurrent = currentSlideDelay;\n    }\n    autoplayTimeLeft = delay;\n    const speed = swiper.params.speed;\n    const proceed = () => {\n      if (!swiper || swiper.destroyed) return;\n      if (swiper.params.autoplay.reverseDirection) {\n        if (!swiper.isBeginning || swiper.params.loop || swiper.params.rewind) {\n          swiper.slidePrev(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(swiper.slides.length - 1, speed, true, true);\n          emit('autoplay');\n        }\n      } else {\n        if (!swiper.isEnd || swiper.params.loop || swiper.params.rewind) {\n          swiper.slideNext(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(0, speed, true, true);\n          emit('autoplay');\n        }\n      }\n      if (swiper.params.cssMode) {\n        autoplayStartTime = new Date().getTime();\n        requestAnimationFrame(() => {\n          run();\n        });\n      }\n    };\n    if (delay > 0) {\n      clearTimeout(timeout);\n      timeout = setTimeout(() => {\n        proceed();\n      }, delay);\n    } else {\n      requestAnimationFrame(() => {\n        proceed();\n      });\n    }\n\n    // eslint-disable-next-line\n    return delay;\n  };\n  const start = () => {\n    autoplayStartTime = new Date().getTime();\n    swiper.autoplay.running = true;\n    run();\n    emit('autoplayStart');\n  };\n  const stop = () => {\n    swiper.autoplay.running = false;\n    clearTimeout(timeout);\n    cancelAnimationFrame(raf);\n    emit('autoplayStop');\n  };\n  const pause = (internal, reset) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    clearTimeout(timeout);\n    if (!internal) {\n      pausedByInteraction = true;\n    }\n    const proceed = () => {\n      emit('autoplayPause');\n      if (swiper.params.autoplay.waitForTransition) {\n        swiper.wrapperEl.addEventListener('transitionend', onTransitionEnd);\n      } else {\n        resume();\n      }\n    };\n    swiper.autoplay.paused = true;\n    if (reset) {\n      if (slideChanged) {\n        autoplayTimeLeft = swiper.params.autoplay.delay;\n      }\n      slideChanged = false;\n      proceed();\n      return;\n    }\n    const delay = autoplayTimeLeft || swiper.params.autoplay.delay;\n    autoplayTimeLeft = delay - (new Date().getTime() - autoplayStartTime);\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop) return;\n    if (autoplayTimeLeft < 0) autoplayTimeLeft = 0;\n    proceed();\n  };\n  const resume = () => {\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop || swiper.destroyed || !swiper.autoplay.running) return;\n    autoplayStartTime = new Date().getTime();\n    if (pausedByInteraction) {\n      pausedByInteraction = false;\n      run(autoplayTimeLeft);\n    } else {\n      run();\n    }\n    swiper.autoplay.paused = false;\n    emit('autoplayResume');\n  };\n  const onVisibilityChange = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    const document = getDocument();\n    if (document.visibilityState === 'hidden') {\n      pausedByInteraction = true;\n      pause(true);\n    }\n    if (document.visibilityState === 'visible') {\n      resume();\n    }\n  };\n  const onPointerEnter = e => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByInteraction = true;\n    pausedByPointerEnter = true;\n    if (swiper.animating || swiper.autoplay.paused) return;\n    pause(true);\n  };\n  const onPointerLeave = e => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByPointerEnter = false;\n    if (swiper.autoplay.paused) {\n      resume();\n    }\n  };\n  const attachMouseEvents = () => {\n    if (swiper.params.autoplay.pauseOnMouseEnter) {\n      swiper.el.addEventListener('pointerenter', onPointerEnter);\n      swiper.el.addEventListener('pointerleave', onPointerLeave);\n    }\n  };\n  const detachMouseEvents = () => {\n    if (swiper.el && typeof swiper.el !== 'string') {\n      swiper.el.removeEventListener('pointerenter', onPointerEnter);\n      swiper.el.removeEventListener('pointerleave', onPointerLeave);\n    }\n  };\n  const attachDocumentEvents = () => {\n    const document = getDocument();\n    document.addEventListener('visibilitychange', onVisibilityChange);\n  };\n  const detachDocumentEvents = () => {\n    const document = getDocument();\n    document.removeEventListener('visibilitychange', onVisibilityChange);\n  };\n  on('init', () => {\n    if (swiper.params.autoplay.enabled) {\n      attachMouseEvents();\n      attachDocumentEvents();\n      start();\n    }\n  });\n  on('destroy', () => {\n    detachMouseEvents();\n    detachDocumentEvents();\n    if (swiper.autoplay.running) {\n      stop();\n    }\n  });\n  on('_freeModeStaticRelease', () => {\n    if (pausedByTouch || pausedByInteraction) {\n      resume();\n    }\n  });\n  on('_freeModeNoMomentumRelease', () => {\n    if (!swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n  on('beforeTransitionStart', (_s, speed, internal) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (internal || !swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n  on('sliderFirstMove', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.params.autoplay.disableOnInteraction) {\n      stop();\n      return;\n    }\n    isTouched = true;\n    pausedByTouch = false;\n    pausedByInteraction = false;\n    touchStartTimeout = setTimeout(() => {\n      pausedByInteraction = true;\n      pausedByTouch = true;\n      pause(true);\n    }, 200);\n  });\n  on('touchEnd', () => {\n    if (swiper.destroyed || !swiper.autoplay.running || !isTouched) return;\n    clearTimeout(touchStartTimeout);\n    clearTimeout(timeout);\n    if (swiper.params.autoplay.disableOnInteraction) {\n      pausedByTouch = false;\n      isTouched = false;\n      return;\n    }\n    if (pausedByTouch && swiper.params.cssMode) resume();\n    pausedByTouch = false;\n    isTouched = false;\n  });\n  on('slideChange', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    slideChanged = true;\n  });\n  Object.assign(swiper.autoplay, {\n    start,\n    stop,\n    pause,\n    resume\n  });\n}\nexport { Autoplay as default };", "map": {"version": 3, "names": ["g", "getDocument", "Autoplay", "_ref", "swiper", "extendParams", "on", "emit", "params", "autoplay", "running", "paused", "timeLeft", "enabled", "delay", "waitForTransition", "disableOnInteraction", "stopOnLastSlide", "reverseDirection", "pauseOnMouseEnter", "timeout", "raf", "autoplayDelayTotal", "autoplayDelayCurrent", "autoplayTimeLeft", "autoplayStartTime", "Date", "getTime", "wasPaused", "isTouched", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touchStartTimeout", "slideChanged", "pausedByInteraction", "pausedByPointerEnter", "onTransitionEnd", "e", "destroyed", "wrapperEl", "target", "removeEventListener", "detail", "bySwiperTouchMove", "resume", "calcTimeLeft", "requestAnimationFrame", "getSlideDelay", "activeSlideEl", "virtual", "slides", "find", "slideEl", "classList", "contains", "activeIndex", "undefined", "currentSlideDelay", "parseInt", "getAttribute", "run", "delayForce", "cancelAnimationFrame", "Number", "isNaN", "speed", "proceed", "isBeginning", "loop", "rewind", "slidePrev", "slideTo", "length", "isEnd", "slideNext", "cssMode", "clearTimeout", "setTimeout", "start", "stop", "pause", "internal", "reset", "addEventListener", "onVisibilityChange", "document", "visibilityState", "onPointerEnter", "pointerType", "animating", "onPointerLeave", "attachMouseEvents", "el", "detachMouseEvents", "attachDocumentEvents", "detachDocumentEvents", "_s", "Object", "assign", "default"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/swiper/modules/autoplay.mjs"], "sourcesContent": ["import { g as getDocument } from '../shared/ssr-window.esm.mjs';\n\n/* eslint no-underscore-dangle: \"off\" */\n/* eslint no-use-before-define: \"off\" */\nfunction Autoplay(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit,\n    params\n  } = _ref;\n  swiper.autoplay = {\n    running: false,\n    paused: false,\n    timeLeft: 0\n  };\n  extendParams({\n    autoplay: {\n      enabled: false,\n      delay: 3000,\n      waitForTransition: true,\n      disableOnInteraction: false,\n      stopOnLastSlide: false,\n      reverseDirection: false,\n      pauseOnMouseEnter: false\n    }\n  });\n  let timeout;\n  let raf;\n  let autoplayDelayTotal = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayDelayCurrent = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayTimeLeft;\n  let autoplayStartTime = new Date().getTime();\n  let wasPaused;\n  let isTouched;\n  let pausedByTouch;\n  let touchStartTimeout;\n  let slideChanged;\n  let pausedByInteraction;\n  let pausedByPointerEnter;\n  function onTransitionEnd(e) {\n    if (!swiper || swiper.destroyed || !swiper.wrapperEl) return;\n    if (e.target !== swiper.wrapperEl) return;\n    swiper.wrapperEl.removeEventListener('transitionend', onTransitionEnd);\n    if (pausedByPointerEnter || e.detail && e.detail.bySwiperTouchMove) {\n      return;\n    }\n    resume();\n  }\n  const calcTimeLeft = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.autoplay.paused) {\n      wasPaused = true;\n    } else if (wasPaused) {\n      autoplayDelayCurrent = autoplayTimeLeft;\n      wasPaused = false;\n    }\n    const timeLeft = swiper.autoplay.paused ? autoplayTimeLeft : autoplayStartTime + autoplayDelayCurrent - new Date().getTime();\n    swiper.autoplay.timeLeft = timeLeft;\n    emit('autoplayTimeLeft', timeLeft, timeLeft / autoplayDelayTotal);\n    raf = requestAnimationFrame(() => {\n      calcTimeLeft();\n    });\n  };\n  const getSlideDelay = () => {\n    let activeSlideEl;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      activeSlideEl = swiper.slides.find(slideEl => slideEl.classList.contains('swiper-slide-active'));\n    } else {\n      activeSlideEl = swiper.slides[swiper.activeIndex];\n    }\n    if (!activeSlideEl) return undefined;\n    const currentSlideDelay = parseInt(activeSlideEl.getAttribute('data-swiper-autoplay'), 10);\n    return currentSlideDelay;\n  };\n  const run = delayForce => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    cancelAnimationFrame(raf);\n    calcTimeLeft();\n    let delay = typeof delayForce === 'undefined' ? swiper.params.autoplay.delay : delayForce;\n    autoplayDelayTotal = swiper.params.autoplay.delay;\n    autoplayDelayCurrent = swiper.params.autoplay.delay;\n    const currentSlideDelay = getSlideDelay();\n    if (!Number.isNaN(currentSlideDelay) && currentSlideDelay > 0 && typeof delayForce === 'undefined') {\n      delay = currentSlideDelay;\n      autoplayDelayTotal = currentSlideDelay;\n      autoplayDelayCurrent = currentSlideDelay;\n    }\n    autoplayTimeLeft = delay;\n    const speed = swiper.params.speed;\n    const proceed = () => {\n      if (!swiper || swiper.destroyed) return;\n      if (swiper.params.autoplay.reverseDirection) {\n        if (!swiper.isBeginning || swiper.params.loop || swiper.params.rewind) {\n          swiper.slidePrev(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(swiper.slides.length - 1, speed, true, true);\n          emit('autoplay');\n        }\n      } else {\n        if (!swiper.isEnd || swiper.params.loop || swiper.params.rewind) {\n          swiper.slideNext(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(0, speed, true, true);\n          emit('autoplay');\n        }\n      }\n      if (swiper.params.cssMode) {\n        autoplayStartTime = new Date().getTime();\n        requestAnimationFrame(() => {\n          run();\n        });\n      }\n    };\n    if (delay > 0) {\n      clearTimeout(timeout);\n      timeout = setTimeout(() => {\n        proceed();\n      }, delay);\n    } else {\n      requestAnimationFrame(() => {\n        proceed();\n      });\n    }\n\n    // eslint-disable-next-line\n    return delay;\n  };\n  const start = () => {\n    autoplayStartTime = new Date().getTime();\n    swiper.autoplay.running = true;\n    run();\n    emit('autoplayStart');\n  };\n  const stop = () => {\n    swiper.autoplay.running = false;\n    clearTimeout(timeout);\n    cancelAnimationFrame(raf);\n    emit('autoplayStop');\n  };\n  const pause = (internal, reset) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    clearTimeout(timeout);\n    if (!internal) {\n      pausedByInteraction = true;\n    }\n    const proceed = () => {\n      emit('autoplayPause');\n      if (swiper.params.autoplay.waitForTransition) {\n        swiper.wrapperEl.addEventListener('transitionend', onTransitionEnd);\n      } else {\n        resume();\n      }\n    };\n    swiper.autoplay.paused = true;\n    if (reset) {\n      if (slideChanged) {\n        autoplayTimeLeft = swiper.params.autoplay.delay;\n      }\n      slideChanged = false;\n      proceed();\n      return;\n    }\n    const delay = autoplayTimeLeft || swiper.params.autoplay.delay;\n    autoplayTimeLeft = delay - (new Date().getTime() - autoplayStartTime);\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop) return;\n    if (autoplayTimeLeft < 0) autoplayTimeLeft = 0;\n    proceed();\n  };\n  const resume = () => {\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop || swiper.destroyed || !swiper.autoplay.running) return;\n    autoplayStartTime = new Date().getTime();\n    if (pausedByInteraction) {\n      pausedByInteraction = false;\n      run(autoplayTimeLeft);\n    } else {\n      run();\n    }\n    swiper.autoplay.paused = false;\n    emit('autoplayResume');\n  };\n  const onVisibilityChange = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    const document = getDocument();\n    if (document.visibilityState === 'hidden') {\n      pausedByInteraction = true;\n      pause(true);\n    }\n    if (document.visibilityState === 'visible') {\n      resume();\n    }\n  };\n  const onPointerEnter = e => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByInteraction = true;\n    pausedByPointerEnter = true;\n    if (swiper.animating || swiper.autoplay.paused) return;\n    pause(true);\n  };\n  const onPointerLeave = e => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByPointerEnter = false;\n    if (swiper.autoplay.paused) {\n      resume();\n    }\n  };\n  const attachMouseEvents = () => {\n    if (swiper.params.autoplay.pauseOnMouseEnter) {\n      swiper.el.addEventListener('pointerenter', onPointerEnter);\n      swiper.el.addEventListener('pointerleave', onPointerLeave);\n    }\n  };\n  const detachMouseEvents = () => {\n    if (swiper.el && typeof swiper.el !== 'string') {\n      swiper.el.removeEventListener('pointerenter', onPointerEnter);\n      swiper.el.removeEventListener('pointerleave', onPointerLeave);\n    }\n  };\n  const attachDocumentEvents = () => {\n    const document = getDocument();\n    document.addEventListener('visibilitychange', onVisibilityChange);\n  };\n  const detachDocumentEvents = () => {\n    const document = getDocument();\n    document.removeEventListener('visibilitychange', onVisibilityChange);\n  };\n  on('init', () => {\n    if (swiper.params.autoplay.enabled) {\n      attachMouseEvents();\n      attachDocumentEvents();\n      start();\n    }\n  });\n  on('destroy', () => {\n    detachMouseEvents();\n    detachDocumentEvents();\n    if (swiper.autoplay.running) {\n      stop();\n    }\n  });\n  on('_freeModeStaticRelease', () => {\n    if (pausedByTouch || pausedByInteraction) {\n      resume();\n    }\n  });\n  on('_freeModeNoMomentumRelease', () => {\n    if (!swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n  on('beforeTransitionStart', (_s, speed, internal) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (internal || !swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n  on('sliderFirstMove', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.params.autoplay.disableOnInteraction) {\n      stop();\n      return;\n    }\n    isTouched = true;\n    pausedByTouch = false;\n    pausedByInteraction = false;\n    touchStartTimeout = setTimeout(() => {\n      pausedByInteraction = true;\n      pausedByTouch = true;\n      pause(true);\n    }, 200);\n  });\n  on('touchEnd', () => {\n    if (swiper.destroyed || !swiper.autoplay.running || !isTouched) return;\n    clearTimeout(touchStartTimeout);\n    clearTimeout(timeout);\n    if (swiper.params.autoplay.disableOnInteraction) {\n      pausedByTouch = false;\n      isTouched = false;\n      return;\n    }\n    if (pausedByTouch && swiper.params.cssMode) resume();\n    pausedByTouch = false;\n    isTouched = false;\n  });\n  on('slideChange', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    slideChanged = true;\n  });\n  Object.assign(swiper.autoplay, {\n    start,\n    stop,\n    pause,\n    resume\n  });\n}\n\nexport { Autoplay as default };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,WAAW,QAAQ,8BAA8B;;AAE/D;AACA;AACA,SAASC,QAAQA,CAACC,IAAI,EAAE;EACtB,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC,EAAE;IACFC,IAAI;IACJC;EACF,CAAC,GAAGL,IAAI;EACRC,MAAM,CAACK,QAAQ,GAAG;IAChBC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE;EACZ,CAAC;EACDP,YAAY,CAAC;IACXI,QAAQ,EAAE;MACRI,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE,IAAI;MACXC,iBAAiB,EAAE,IAAI;MACvBC,oBAAoB,EAAE,KAAK;MAC3BC,eAAe,EAAE,KAAK;MACtBC,gBAAgB,EAAE,KAAK;MACvBC,iBAAiB,EAAE;IACrB;EACF,CAAC,CAAC;EACF,IAAIC,OAAO;EACX,IAAIC,GAAG;EACP,IAAIC,kBAAkB,GAAGd,MAAM,IAAIA,MAAM,CAACC,QAAQ,GAAGD,MAAM,CAACC,QAAQ,CAACK,KAAK,GAAG,IAAI;EACjF,IAAIS,oBAAoB,GAAGf,MAAM,IAAIA,MAAM,CAACC,QAAQ,GAAGD,MAAM,CAACC,QAAQ,CAACK,KAAK,GAAG,IAAI;EACnF,IAAIU,gBAAgB;EACpB,IAAIC,iBAAiB,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EAC5C,IAAIC,SAAS;EACb,IAAIC,SAAS;EACb,IAAIC,aAAa;EACjB,IAAIC,iBAAiB;EACrB,IAAIC,YAAY;EAChB,IAAIC,mBAAmB;EACvB,IAAIC,oBAAoB;EACxB,SAASC,eAAeA,CAACC,CAAC,EAAE;IAC1B,IAAI,CAAChC,MAAM,IAAIA,MAAM,CAACiC,SAAS,IAAI,CAACjC,MAAM,CAACkC,SAAS,EAAE;IACtD,IAAIF,CAAC,CAACG,MAAM,KAAKnC,MAAM,CAACkC,SAAS,EAAE;IACnClC,MAAM,CAACkC,SAAS,CAACE,mBAAmB,CAAC,eAAe,EAAEL,eAAe,CAAC;IACtE,IAAID,oBAAoB,IAAIE,CAAC,CAACK,MAAM,IAAIL,CAAC,CAACK,MAAM,CAACC,iBAAiB,EAAE;MAClE;IACF;IACAC,MAAM,CAAC,CAAC;EACV;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIxC,MAAM,CAACiC,SAAS,IAAI,CAACjC,MAAM,CAACK,QAAQ,CAACC,OAAO,EAAE;IAClD,IAAIN,MAAM,CAACK,QAAQ,CAACE,MAAM,EAAE;MAC1BiB,SAAS,GAAG,IAAI;IAClB,CAAC,MAAM,IAAIA,SAAS,EAAE;MACpBL,oBAAoB,GAAGC,gBAAgB;MACvCI,SAAS,GAAG,KAAK;IACnB;IACA,MAAMhB,QAAQ,GAAGR,MAAM,CAACK,QAAQ,CAACE,MAAM,GAAGa,gBAAgB,GAAGC,iBAAiB,GAAGF,oBAAoB,GAAG,IAAIG,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IAC5HvB,MAAM,CAACK,QAAQ,CAACG,QAAQ,GAAGA,QAAQ;IACnCL,IAAI,CAAC,kBAAkB,EAAEK,QAAQ,EAAEA,QAAQ,GAAGU,kBAAkB,CAAC;IACjED,GAAG,GAAGwB,qBAAqB,CAAC,MAAM;MAChCD,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC;EACJ,CAAC;EACD,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIC,aAAa;IACjB,IAAI3C,MAAM,CAAC4C,OAAO,IAAI5C,MAAM,CAACI,MAAM,CAACwC,OAAO,CAACnC,OAAO,EAAE;MACnDkC,aAAa,GAAG3C,MAAM,CAAC6C,MAAM,CAACC,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;IAClG,CAAC,MAAM;MACLN,aAAa,GAAG3C,MAAM,CAAC6C,MAAM,CAAC7C,MAAM,CAACkD,WAAW,CAAC;IACnD;IACA,IAAI,CAACP,aAAa,EAAE,OAAOQ,SAAS;IACpC,MAAMC,iBAAiB,GAAGC,QAAQ,CAACV,aAAa,CAACW,YAAY,CAAC,sBAAsB,CAAC,EAAE,EAAE,CAAC;IAC1F,OAAOF,iBAAiB;EAC1B,CAAC;EACD,MAAMG,GAAG,GAAGC,UAAU,IAAI;IACxB,IAAIxD,MAAM,CAACiC,SAAS,IAAI,CAACjC,MAAM,CAACK,QAAQ,CAACC,OAAO,EAAE;IAClDmD,oBAAoB,CAACxC,GAAG,CAAC;IACzBuB,YAAY,CAAC,CAAC;IACd,IAAI9B,KAAK,GAAG,OAAO8C,UAAU,KAAK,WAAW,GAAGxD,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACK,KAAK,GAAG8C,UAAU;IACzFtC,kBAAkB,GAAGlB,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACK,KAAK;IACjDS,oBAAoB,GAAGnB,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACK,KAAK;IACnD,MAAM0C,iBAAiB,GAAGV,aAAa,CAAC,CAAC;IACzC,IAAI,CAACgB,MAAM,CAACC,KAAK,CAACP,iBAAiB,CAAC,IAAIA,iBAAiB,GAAG,CAAC,IAAI,OAAOI,UAAU,KAAK,WAAW,EAAE;MAClG9C,KAAK,GAAG0C,iBAAiB;MACzBlC,kBAAkB,GAAGkC,iBAAiB;MACtCjC,oBAAoB,GAAGiC,iBAAiB;IAC1C;IACAhC,gBAAgB,GAAGV,KAAK;IACxB,MAAMkD,KAAK,GAAG5D,MAAM,CAACI,MAAM,CAACwD,KAAK;IACjC,MAAMC,OAAO,GAAGA,CAAA,KAAM;MACpB,IAAI,CAAC7D,MAAM,IAAIA,MAAM,CAACiC,SAAS,EAAE;MACjC,IAAIjC,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACS,gBAAgB,EAAE;QAC3C,IAAI,CAACd,MAAM,CAAC8D,WAAW,IAAI9D,MAAM,CAACI,MAAM,CAAC2D,IAAI,IAAI/D,MAAM,CAACI,MAAM,CAAC4D,MAAM,EAAE;UACrEhE,MAAM,CAACiE,SAAS,CAACL,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;UACnCzD,IAAI,CAAC,UAAU,CAAC;QAClB,CAAC,MAAM,IAAI,CAACH,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACQ,eAAe,EAAE;UAClDb,MAAM,CAACkE,OAAO,CAAClE,MAAM,CAAC6C,MAAM,CAACsB,MAAM,GAAG,CAAC,EAAEP,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;UAC3DzD,IAAI,CAAC,UAAU,CAAC;QAClB;MACF,CAAC,MAAM;QACL,IAAI,CAACH,MAAM,CAACoE,KAAK,IAAIpE,MAAM,CAACI,MAAM,CAAC2D,IAAI,IAAI/D,MAAM,CAACI,MAAM,CAAC4D,MAAM,EAAE;UAC/DhE,MAAM,CAACqE,SAAS,CAACT,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;UACnCzD,IAAI,CAAC,UAAU,CAAC;QAClB,CAAC,MAAM,IAAI,CAACH,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACQ,eAAe,EAAE;UAClDb,MAAM,CAACkE,OAAO,CAAC,CAAC,EAAEN,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;UACpCzD,IAAI,CAAC,UAAU,CAAC;QAClB;MACF;MACA,IAAIH,MAAM,CAACI,MAAM,CAACkE,OAAO,EAAE;QACzBjD,iBAAiB,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;QACxCkB,qBAAqB,CAAC,MAAM;UAC1Bc,GAAG,CAAC,CAAC;QACP,CAAC,CAAC;MACJ;IACF,CAAC;IACD,IAAI7C,KAAK,GAAG,CAAC,EAAE;MACb6D,YAAY,CAACvD,OAAO,CAAC;MACrBA,OAAO,GAAGwD,UAAU,CAAC,MAAM;QACzBX,OAAO,CAAC,CAAC;MACX,CAAC,EAAEnD,KAAK,CAAC;IACX,CAAC,MAAM;MACL+B,qBAAqB,CAAC,MAAM;QAC1BoB,OAAO,CAAC,CAAC;MACX,CAAC,CAAC;IACJ;;IAEA;IACA,OAAOnD,KAAK;EACd,CAAC;EACD,MAAM+D,KAAK,GAAGA,CAAA,KAAM;IAClBpD,iBAAiB,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACxCvB,MAAM,CAACK,QAAQ,CAACC,OAAO,GAAG,IAAI;IAC9BiD,GAAG,CAAC,CAAC;IACLpD,IAAI,CAAC,eAAe,CAAC;EACvB,CAAC;EACD,MAAMuE,IAAI,GAAGA,CAAA,KAAM;IACjB1E,MAAM,CAACK,QAAQ,CAACC,OAAO,GAAG,KAAK;IAC/BiE,YAAY,CAACvD,OAAO,CAAC;IACrByC,oBAAoB,CAACxC,GAAG,CAAC;IACzBd,IAAI,CAAC,cAAc,CAAC;EACtB,CAAC;EACD,MAAMwE,KAAK,GAAGA,CAACC,QAAQ,EAAEC,KAAK,KAAK;IACjC,IAAI7E,MAAM,CAACiC,SAAS,IAAI,CAACjC,MAAM,CAACK,QAAQ,CAACC,OAAO,EAAE;IAClDiE,YAAY,CAACvD,OAAO,CAAC;IACrB,IAAI,CAAC4D,QAAQ,EAAE;MACb/C,mBAAmB,GAAG,IAAI;IAC5B;IACA,MAAMgC,OAAO,GAAGA,CAAA,KAAM;MACpB1D,IAAI,CAAC,eAAe,CAAC;MACrB,IAAIH,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACM,iBAAiB,EAAE;QAC5CX,MAAM,CAACkC,SAAS,CAAC4C,gBAAgB,CAAC,eAAe,EAAE/C,eAAe,CAAC;MACrE,CAAC,MAAM;QACLQ,MAAM,CAAC,CAAC;MACV;IACF,CAAC;IACDvC,MAAM,CAACK,QAAQ,CAACE,MAAM,GAAG,IAAI;IAC7B,IAAIsE,KAAK,EAAE;MACT,IAAIjD,YAAY,EAAE;QAChBR,gBAAgB,GAAGpB,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACK,KAAK;MACjD;MACAkB,YAAY,GAAG,KAAK;MACpBiC,OAAO,CAAC,CAAC;MACT;IACF;IACA,MAAMnD,KAAK,GAAGU,gBAAgB,IAAIpB,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACK,KAAK;IAC9DU,gBAAgB,GAAGV,KAAK,IAAI,IAAIY,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAGF,iBAAiB,CAAC;IACrE,IAAIrB,MAAM,CAACoE,KAAK,IAAIhD,gBAAgB,GAAG,CAAC,IAAI,CAACpB,MAAM,CAACI,MAAM,CAAC2D,IAAI,EAAE;IACjE,IAAI3C,gBAAgB,GAAG,CAAC,EAAEA,gBAAgB,GAAG,CAAC;IAC9CyC,OAAO,CAAC,CAAC;EACX,CAAC;EACD,MAAMtB,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAIvC,MAAM,CAACoE,KAAK,IAAIhD,gBAAgB,GAAG,CAAC,IAAI,CAACpB,MAAM,CAACI,MAAM,CAAC2D,IAAI,IAAI/D,MAAM,CAACiC,SAAS,IAAI,CAACjC,MAAM,CAACK,QAAQ,CAACC,OAAO,EAAE;IACjHe,iBAAiB,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACxC,IAAIM,mBAAmB,EAAE;MACvBA,mBAAmB,GAAG,KAAK;MAC3B0B,GAAG,CAACnC,gBAAgB,CAAC;IACvB,CAAC,MAAM;MACLmC,GAAG,CAAC,CAAC;IACP;IACAvD,MAAM,CAACK,QAAQ,CAACE,MAAM,GAAG,KAAK;IAC9BJ,IAAI,CAAC,gBAAgB,CAAC;EACxB,CAAC;EACD,MAAM4E,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI/E,MAAM,CAACiC,SAAS,IAAI,CAACjC,MAAM,CAACK,QAAQ,CAACC,OAAO,EAAE;IAClD,MAAM0E,QAAQ,GAAGnF,WAAW,CAAC,CAAC;IAC9B,IAAImF,QAAQ,CAACC,eAAe,KAAK,QAAQ,EAAE;MACzCpD,mBAAmB,GAAG,IAAI;MAC1B8C,KAAK,CAAC,IAAI,CAAC;IACb;IACA,IAAIK,QAAQ,CAACC,eAAe,KAAK,SAAS,EAAE;MAC1C1C,MAAM,CAAC,CAAC;IACV;EACF,CAAC;EACD,MAAM2C,cAAc,GAAGlD,CAAC,IAAI;IAC1B,IAAIA,CAAC,CAACmD,WAAW,KAAK,OAAO,EAAE;IAC/BtD,mBAAmB,GAAG,IAAI;IAC1BC,oBAAoB,GAAG,IAAI;IAC3B,IAAI9B,MAAM,CAACoF,SAAS,IAAIpF,MAAM,CAACK,QAAQ,CAACE,MAAM,EAAE;IAChDoE,KAAK,CAAC,IAAI,CAAC;EACb,CAAC;EACD,MAAMU,cAAc,GAAGrD,CAAC,IAAI;IAC1B,IAAIA,CAAC,CAACmD,WAAW,KAAK,OAAO,EAAE;IAC/BrD,oBAAoB,GAAG,KAAK;IAC5B,IAAI9B,MAAM,CAACK,QAAQ,CAACE,MAAM,EAAE;MAC1BgC,MAAM,CAAC,CAAC;IACV;EACF,CAAC;EACD,MAAM+C,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAItF,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACU,iBAAiB,EAAE;MAC5Cf,MAAM,CAACuF,EAAE,CAACT,gBAAgB,CAAC,cAAc,EAAEI,cAAc,CAAC;MAC1DlF,MAAM,CAACuF,EAAE,CAACT,gBAAgB,CAAC,cAAc,EAAEO,cAAc,CAAC;IAC5D;EACF,CAAC;EACD,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIxF,MAAM,CAACuF,EAAE,IAAI,OAAOvF,MAAM,CAACuF,EAAE,KAAK,QAAQ,EAAE;MAC9CvF,MAAM,CAACuF,EAAE,CAACnD,mBAAmB,CAAC,cAAc,EAAE8C,cAAc,CAAC;MAC7DlF,MAAM,CAACuF,EAAE,CAACnD,mBAAmB,CAAC,cAAc,EAAEiD,cAAc,CAAC;IAC/D;EACF,CAAC;EACD,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMT,QAAQ,GAAGnF,WAAW,CAAC,CAAC;IAC9BmF,QAAQ,CAACF,gBAAgB,CAAC,kBAAkB,EAAEC,kBAAkB,CAAC;EACnE,CAAC;EACD,MAAMW,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMV,QAAQ,GAAGnF,WAAW,CAAC,CAAC;IAC9BmF,QAAQ,CAAC5C,mBAAmB,CAAC,kBAAkB,EAAE2C,kBAAkB,CAAC;EACtE,CAAC;EACD7E,EAAE,CAAC,MAAM,EAAE,MAAM;IACf,IAAIF,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACI,OAAO,EAAE;MAClC6E,iBAAiB,CAAC,CAAC;MACnBG,oBAAoB,CAAC,CAAC;MACtBhB,KAAK,CAAC,CAAC;IACT;EACF,CAAC,CAAC;EACFvE,EAAE,CAAC,SAAS,EAAE,MAAM;IAClBsF,iBAAiB,CAAC,CAAC;IACnBE,oBAAoB,CAAC,CAAC;IACtB,IAAI1F,MAAM,CAACK,QAAQ,CAACC,OAAO,EAAE;MAC3BoE,IAAI,CAAC,CAAC;IACR;EACF,CAAC,CAAC;EACFxE,EAAE,CAAC,wBAAwB,EAAE,MAAM;IACjC,IAAIwB,aAAa,IAAIG,mBAAmB,EAAE;MACxCU,MAAM,CAAC,CAAC;IACV;EACF,CAAC,CAAC;EACFrC,EAAE,CAAC,4BAA4B,EAAE,MAAM;IACrC,IAAI,CAACF,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACO,oBAAoB,EAAE;MAChD+D,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;IACnB,CAAC,MAAM;MACLD,IAAI,CAAC,CAAC;IACR;EACF,CAAC,CAAC;EACFxE,EAAE,CAAC,uBAAuB,EAAE,CAACyF,EAAE,EAAE/B,KAAK,EAAEgB,QAAQ,KAAK;IACnD,IAAI5E,MAAM,CAACiC,SAAS,IAAI,CAACjC,MAAM,CAACK,QAAQ,CAACC,OAAO,EAAE;IAClD,IAAIsE,QAAQ,IAAI,CAAC5E,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACO,oBAAoB,EAAE;MAC5D+D,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;IACnB,CAAC,MAAM;MACLD,IAAI,CAAC,CAAC;IACR;EACF,CAAC,CAAC;EACFxE,EAAE,CAAC,iBAAiB,EAAE,MAAM;IAC1B,IAAIF,MAAM,CAACiC,SAAS,IAAI,CAACjC,MAAM,CAACK,QAAQ,CAACC,OAAO,EAAE;IAClD,IAAIN,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACO,oBAAoB,EAAE;MAC/C8D,IAAI,CAAC,CAAC;MACN;IACF;IACAjD,SAAS,GAAG,IAAI;IAChBC,aAAa,GAAG,KAAK;IACrBG,mBAAmB,GAAG,KAAK;IAC3BF,iBAAiB,GAAG6C,UAAU,CAAC,MAAM;MACnC3C,mBAAmB,GAAG,IAAI;MAC1BH,aAAa,GAAG,IAAI;MACpBiD,KAAK,CAAC,IAAI,CAAC;IACb,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,CAAC;EACFzE,EAAE,CAAC,UAAU,EAAE,MAAM;IACnB,IAAIF,MAAM,CAACiC,SAAS,IAAI,CAACjC,MAAM,CAACK,QAAQ,CAACC,OAAO,IAAI,CAACmB,SAAS,EAAE;IAChE8C,YAAY,CAAC5C,iBAAiB,CAAC;IAC/B4C,YAAY,CAACvD,OAAO,CAAC;IACrB,IAAIhB,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACO,oBAAoB,EAAE;MAC/Cc,aAAa,GAAG,KAAK;MACrBD,SAAS,GAAG,KAAK;MACjB;IACF;IACA,IAAIC,aAAa,IAAI1B,MAAM,CAACI,MAAM,CAACkE,OAAO,EAAE/B,MAAM,CAAC,CAAC;IACpDb,aAAa,GAAG,KAAK;IACrBD,SAAS,GAAG,KAAK;EACnB,CAAC,CAAC;EACFvB,EAAE,CAAC,aAAa,EAAE,MAAM;IACtB,IAAIF,MAAM,CAACiC,SAAS,IAAI,CAACjC,MAAM,CAACK,QAAQ,CAACC,OAAO,EAAE;IAClDsB,YAAY,GAAG,IAAI;EACrB,CAAC,CAAC;EACFgE,MAAM,CAACC,MAAM,CAAC7F,MAAM,CAACK,QAAQ,EAAE;IAC7BoE,KAAK;IACLC,IAAI;IACJC,KAAK;IACLpC;EACF,CAAC,CAAC;AACJ;AAEA,SAASzC,QAAQ,IAAIgG,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}