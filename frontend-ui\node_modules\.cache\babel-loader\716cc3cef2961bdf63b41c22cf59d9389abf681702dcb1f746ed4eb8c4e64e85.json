{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\LaravelReact\\\\frontend-ui\\\\src\\\\shop\\\\Search.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Link } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Search = ({\n  products,\n  GridList\n}) => {\n  _s();\n  const [searchTerm, setSearchTerm] = useState(\"\");\n\n  // Filter products and remove duplicates based on title\n  const filteredProducts = products.filter(product => (product.Title || product.title || \"\").toLowerCase().includes(searchTerm.toLowerCase())).reduce((unique, product) => {\n    // Check if we already have a product with this title\n    const exists = unique.find(p => (p.Title || p.title) === (product.Title || product.title));\n    if (!exists) {\n      unique.push(product);\n    }\n    return unique;\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"widget widget-search\",\n    children: [/*#__PURE__*/_jsxDEV(\"form\", {\n      className: \"search-wrapper mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        name: \"search\",\n        id: \"search\",\n        placeholder: \"Search...\",\n        defaultValue: searchTerm,\n        onChange: e => setSearchTerm(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        children: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"icofont-search-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: searchTerm && filteredProducts.map(product => /*#__PURE__*/_jsxDEV(Link, {\n        to: `/shop/${product.BookID || product.id}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex gap-3 p-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pro-thumb h-25\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: product.Image || product.image || \"/assets/images/product-placeholder.png\",\n              alt: product.Title || product.title,\n              style: {\n                width: \"70px\",\n                height: \"70px\",\n                objectFit: \"contain\"\n              },\n              onError: e => {\n                e.target.src = \"/assets/images/product-placeholder.png\";\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: `/shop/${product.BookID || product.id}`,\n                children: product.Title || product.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              children: [\"$\", parseFloat(product.Price || product.price).toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 15\n        }, this)\n      }, product.BookID || product.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_s(Search, \"+YdqPTpSlp4r5CWiFEQiF/UjThM=\");\n_c = Search;\nexport default Search;\nvar _c;\n$RefreshReg$(_c, \"Search\");", "map": {"version": 3, "names": ["React", "useState", "Link", "jsxDEV", "_jsxDEV", "Search", "products", "GridList", "_s", "searchTerm", "setSearchTerm", "filteredProducts", "filter", "product", "Title", "title", "toLowerCase", "includes", "reduce", "unique", "exists", "find", "p", "push", "className", "children", "type", "name", "id", "placeholder", "defaultValue", "onChange", "e", "target", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "to", "BookID", "src", "Image", "image", "alt", "style", "width", "height", "objectFit", "onError", "parseFloat", "Price", "price", "toFixed", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/shop/Search.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { Link } from \"react-router-dom\";\n\nconst Search = ({ products, GridList }) => {\n  const [searchTerm, setSearchTerm] = useState(\"\");\n\n  // Filter products and remove duplicates based on title\n  const filteredProducts = products\n    .filter((product) =>\n      (product.Title || product.title || \"\")\n        .toLowerCase()\n        .includes(searchTerm.toLowerCase())\n    )\n    .reduce((unique, product) => {\n      // Check if we already have a product with this title\n      const exists = unique.find(\n        (p) => (p.Title || p.title) === (product.Title || product.title)\n      );\n      if (!exists) {\n        unique.push(product);\n      }\n      return unique;\n    }, []);\n\n  return (\n    <div className=\"widget widget-search\">\n      <form className=\"search-wrapper mb-3\">\n        <input\n          type=\"text\"\n          name=\"search\"\n          id=\"search\"\n          placeholder=\"Search...\"\n          defaultValue={searchTerm}\n          onChange={(e) => setSearchTerm(e.target.value)}\n        />\n        <button type=\"submit\">\n          <i className=\"icofont-search-2\"></i>\n        </button>\n      </form>\n\n      {/* showing search result */}\n      <div>\n        {searchTerm &&\n          filteredProducts.map((product) => (\n            <Link\n              key={product.BookID || product.id}\n              to={`/shop/${product.BookID || product.id}`}\n            >\n              <div className=\"d-flex gap-3 p-2\">\n                <div className=\"pro-thumb h-25\">\n                  <img\n                    src={\n                      product.Image ||\n                      product.image ||\n                      \"/assets/images/product-placeholder.png\"\n                    }\n                    alt={product.Title || product.title}\n                    style={{\n                      width: \"70px\",\n                      height: \"70px\",\n                      objectFit: \"contain\",\n                    }}\n                    onError={(e) => {\n                      e.target.src = \"/assets/images/product-placeholder.png\";\n                    }}\n                  />\n                </div>\n                <div className=\"product-content\">\n                  <p>\n                    <Link to={`/shop/${product.BookID || product.id}`}>\n                      {product.Title || product.title}\n                    </Link>\n                  </p>\n                  <h6>\n                    ${parseFloat(product.Price || product.price).toFixed(2)}\n                  </h6>\n                </div>\n              </div>\n            </Link>\n          ))}\n      </div>\n    </div>\n  );\n};\n\nexport default Search;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,MAAM,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAMU,gBAAgB,GAAGL,QAAQ,CAC9BM,MAAM,CAAEC,OAAO,IACd,CAACA,OAAO,CAACC,KAAK,IAAID,OAAO,CAACE,KAAK,IAAI,EAAE,EAClCC,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACR,UAAU,CAACO,WAAW,CAAC,CAAC,CACtC,CAAC,CACAE,MAAM,CAAC,CAACC,MAAM,EAAEN,OAAO,KAAK;IAC3B;IACA,MAAMO,MAAM,GAAGD,MAAM,CAACE,IAAI,CACvBC,CAAC,IAAK,CAACA,CAAC,CAACR,KAAK,IAAIQ,CAAC,CAACP,KAAK,OAAOF,OAAO,CAACC,KAAK,IAAID,OAAO,CAACE,KAAK,CACjE,CAAC;IACD,IAAI,CAACK,MAAM,EAAE;MACXD,MAAM,CAACI,IAAI,CAACV,OAAO,CAAC;IACtB;IACA,OAAOM,MAAM;EACf,CAAC,EAAE,EAAE,CAAC;EAER,oBACEf,OAAA;IAAKoB,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBACnCrB,OAAA;MAAMoB,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBACnCrB,OAAA;QACEsB,IAAI,EAAC,MAAM;QACXC,IAAI,EAAC,QAAQ;QACbC,EAAE,EAAC,QAAQ;QACXC,WAAW,EAAC,WAAW;QACvBC,YAAY,EAAErB,UAAW;QACzBsB,QAAQ,EAAGC,CAAC,IAAKtB,aAAa,CAACsB,CAAC,CAACC,MAAM,CAACC,KAAK;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACFlC,OAAA;QAAQsB,IAAI,EAAC,QAAQ;QAAAD,QAAA,eACnBrB,OAAA;UAAGoB,SAAS,EAAC;QAAkB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGPlC,OAAA;MAAAqB,QAAA,EACGhB,UAAU,IACTE,gBAAgB,CAAC4B,GAAG,CAAE1B,OAAO,iBAC3BT,OAAA,CAACF,IAAI;QAEHsC,EAAE,EAAE,SAAS3B,OAAO,CAAC4B,MAAM,IAAI5B,OAAO,CAACe,EAAE,EAAG;QAAAH,QAAA,eAE5CrB,OAAA;UAAKoB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BrB,OAAA;YAAKoB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BrB,OAAA;cACEsC,GAAG,EACD7B,OAAO,CAAC8B,KAAK,IACb9B,OAAO,CAAC+B,KAAK,IACb,wCACD;cACDC,GAAG,EAAEhC,OAAO,CAACC,KAAK,IAAID,OAAO,CAACE,KAAM;cACpC+B,KAAK,EAAE;gBACLC,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdC,SAAS,EAAE;cACb,CAAE;cACFC,OAAO,EAAGlB,CAAC,IAAK;gBACdA,CAAC,CAACC,MAAM,CAACS,GAAG,GAAG,wCAAwC;cACzD;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlC,OAAA;YAAKoB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BrB,OAAA;cAAAqB,QAAA,eACErB,OAAA,CAACF,IAAI;gBAACsC,EAAE,EAAE,SAAS3B,OAAO,CAAC4B,MAAM,IAAI5B,OAAO,CAACe,EAAE,EAAG;gBAAAH,QAAA,EAC/CZ,OAAO,CAACC,KAAK,IAAID,OAAO,CAACE;cAAK;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACJlC,OAAA;cAAAqB,QAAA,GAAI,GACD,EAAC0B,UAAU,CAACtC,OAAO,CAACuC,KAAK,IAAIvC,OAAO,CAACwC,KAAK,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAhCDzB,OAAO,CAAC4B,MAAM,IAAI5B,OAAO,CAACe,EAAE;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiC7B,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAhFIH,MAAM;AAAAkD,EAAA,GAANlD,MAAM;AAkFZ,eAAeA,MAAM;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}