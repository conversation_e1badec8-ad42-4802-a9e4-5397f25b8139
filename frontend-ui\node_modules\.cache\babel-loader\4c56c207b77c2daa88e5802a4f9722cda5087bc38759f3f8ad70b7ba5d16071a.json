{"ast": null, "code": "const _excluded = [\"component\"];\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.indexOf(n) >= 0) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nimport * as React from 'react';\nimport useRTGTransitionProps from './useRTGTransitionProps';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n// Normalizes Transition callbacks when nodeRef is used.\nconst RTGTransition = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      component: Component\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const transitionProps = useRTGTransitionProps(props);\n  return /*#__PURE__*/_jsx(Component, Object.assign({\n    ref: ref\n  }, transitionProps));\n});\nexport default RTGTransition;", "map": {"version": 3, "names": ["_excluded", "_objectWithoutPropertiesLoose", "r", "e", "t", "n", "hasOwnProperty", "call", "indexOf", "React", "useRTGTransitionProps", "jsx", "_jsx", "RTGTransition", "forwardRef", "_ref", "ref", "component", "Component", "props", "transitionProps", "Object", "assign"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/@restart/ui/esm/RTGTransition.js"], "sourcesContent": ["const _excluded = [\"component\"];\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.indexOf(n) >= 0) continue; t[n] = r[n]; } return t; }\nimport * as React from 'react';\nimport useRTGTransitionProps from './useRTGTransitionProps';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n// Normalizes Transition callbacks when nodeRef is used.\nconst RTGTransition = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      component: Component\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const transitionProps = useRTGTransitionProps(props);\n  return /*#__PURE__*/_jsx(Component, Object.assign({\n    ref: ref\n  }, transitionProps));\n});\nexport default RTGTransition;"], "mappings": "AAAA,MAAMA,SAAS,GAAG,CAAC,WAAW,CAAC;AAC/B,SAASC,6BAA6BA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAI,CAAC,CAAC,CAACI,cAAc,CAACC,IAAI,CAACL,CAAC,EAAEG,CAAC,CAAC,EAAE;IAAE,IAAIF,CAAC,CAACK,OAAO,CAACH,CAAC,CAAC,IAAI,CAAC,EAAE;IAAUD,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACpM,OAAO,KAAKK,KAAK,MAAM,OAAO;AAC9B,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C;AACA,MAAMC,aAAa,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK;EACjE,IAAI;MACAC,SAAS,EAAEC;IACb,CAAC,GAAGH,IAAI;IACRI,KAAK,GAAGlB,6BAA6B,CAACc,IAAI,EAAEf,SAAS,CAAC;EACxD,MAAMoB,eAAe,GAAGV,qBAAqB,CAACS,KAAK,CAAC;EACpD,OAAO,aAAaP,IAAI,CAACM,SAAS,EAAEG,MAAM,CAACC,MAAM,CAAC;IAChDN,GAAG,EAAEA;EACP,CAAC,EAAEI,eAAe,CAAC,CAAC;AACtB,CAAC,CAAC;AACF,eAAeP,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}