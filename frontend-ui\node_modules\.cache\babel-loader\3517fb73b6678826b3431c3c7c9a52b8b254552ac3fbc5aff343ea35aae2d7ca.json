{"ast": null, "code": "import { API_BASE_URL, API_KEY, getApiHeaders } from \"../config/api.config\";\n\n// Try to get the API URL from localStorage first (in case it was customized)\nconst localStorageUrl = localStorage.getItem(\"api_url\");\n\n// The API URL - with fallbacks\nconst API_URL = localStorageUrl || API_BASE_URL;\nconsole.log(\"Frontend API Service using URL:\", API_URL);\n\n/**\n * Get an authenticated image URL\n * This will append the API key as a query parameter for direct image requests\n * @param {string} endpoint - Image endpoint (e.g., 'books/1/image')\n * @returns {string} - Authenticated image URL\n */\nexport const getImageUrl = endpoint => {\n  return `${API_URL}/${endpoint.replace(/^\\//, \"\")}?api_key=${API_KEY}`;\n};\n\n/**\n * Fetch data from API with proper authorization headers\n * @param {string} endpoint - API endpoint (without base URL)\n * @param {Object} options - Fetch options\n * @returns {Promise<Object>} - Response data\n */\nexport const fetchApi = async (endpoint, options = {}) => {\n  try {\n    const url = `${API_URL}/${endpoint.replace(/^\\//, \"\")}`;\n\n    // Ensure we always have the basic headers structure\n    const headers = {\n      \"Content-Type\": \"application/json\",\n      Accept: \"application/json\",\n      \"X-Requested-With\": \"XMLHttpRequest\",\n      \"X-API-Key\": API_KEY,\n      ...(options.headers || {})\n    };\n\n    // Debug the request\n    console.log(`API Request to: ${url}`);\n    console.log(\"Request headers:\", headers);\n    console.log(\"Request method:\", options.method || \"GET\");\n    if (options.body) {\n      console.log(\"Request body:\", options.body);\n    }\n    const response = await fetch(url, {\n      ...options,\n      headers\n    });\n\n    // Always get the response text first\n    const responseText = await response.text();\n    if (!response.ok) {\n      console.error(`API Error: ${response.status} ${response.statusText}`);\n      console.error(\"Response headers:\", Object.fromEntries([...response.headers]));\n      console.error(\"Response body:\", responseText);\n\n      // Try to parse the error message from the response\n      let errorMessage = `HTTP error! Status: ${response.status}`;\n      let errorData = null;\n      try {\n        errorData = JSON.parse(responseText);\n        if (errorData.message) {\n          errorMessage = errorData.message;\n        } else if (errorData.error) {\n          errorMessage = errorData.error;\n        }\n        // Include validation errors if available\n        if (errorData.errors) {\n          errorMessage += \": \" + Object.values(errorData.errors).flat().join(\", \");\n        }\n      } catch (e) {\n        // If we can't parse the JSON, just use the raw text\n        if (responseText) {\n          errorMessage = responseText;\n        }\n      }\n      throw new Error(errorMessage);\n    }\n\n    // Parse the successful response\n    try {\n      return JSON.parse(responseText);\n    } catch (e) {\n      console.warn(\"Response is not valid JSON, returning raw text\");\n      return {\n        success: true,\n        data: responseText\n      };\n    }\n  } catch (error) {\n    console.error(`API Error (${endpoint}):`, error);\n    throw error;\n  }\n};\n\n/**\n * GET request\n * @param {string} endpoint - API endpoint\n * @param {Object} options - Additional fetch options\n * @returns {Promise<Object>} - Response data\n */\nexport const get = (endpoint, options = {}) => {\n  return fetchApi(endpoint, {\n    method: \"GET\",\n    ...options\n  });\n};\n\n/**\n * POST request\n * @param {string} endpoint - API endpoint\n * @param {Object} data - Request body data\n * @param {Object} options - Additional fetch options\n * @returns {Promise<Object>} - Response data\n */\nexport const post = (endpoint, data, options = {}) => {\n  return fetchApi(endpoint, {\n    method: \"POST\",\n    body: JSON.stringify(data),\n    ...options\n  });\n};\n\n/**\n * PUT request\n * @param {string} endpoint - API endpoint\n * @param {Object} data - Request body data\n * @param {Object} options - Additional fetch options\n * @returns {Promise<Object>} - Response data\n */\nexport const put = (endpoint, data, options = {}) => {\n  return fetchApi(endpoint, {\n    method: \"PUT\",\n    body: JSON.stringify(data),\n    ...options\n  });\n};\n\n/**\n * DELETE request\n * @param {string} endpoint - API endpoint\n * @param {Object} options - Additional fetch options\n * @returns {Promise<Object>} - Response data\n */\nexport const del = (endpoint, options = {}) => {\n  return fetchApi(endpoint, {\n    method: \"DELETE\",\n    ...options\n  });\n};\nexport default {\n  fetchApi,\n  get,\n  post,\n  put,\n  del,\n  getImageUrl\n};", "map": {"version": 3, "names": ["API_BASE_URL", "API_KEY", "getApiHeaders", "localStorageUrl", "localStorage", "getItem", "API_URL", "console", "log", "getImageUrl", "endpoint", "replace", "fetchApi", "options", "url", "headers", "Accept", "method", "body", "response", "fetch", "responseText", "text", "ok", "error", "status", "statusText", "Object", "fromEntries", "errorMessage", "errorData", "JSON", "parse", "message", "errors", "values", "flat", "join", "e", "Error", "warn", "success", "data", "get", "post", "stringify", "put", "del"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/utilis/apiService.js"], "sourcesContent": ["import { API_BASE_URL, API_KEY, getApiHeaders } from \"../config/api.config\";\n\n// Try to get the API URL from localStorage first (in case it was customized)\nconst localStorageUrl = localStorage.getItem(\"api_url\");\n\n// The API URL - with fallbacks\nconst API_URL = localStorageUrl || API_BASE_URL;\n\nconsole.log(\"Frontend API Service using URL:\", API_URL);\n\n/**\n * Get an authenticated image URL\n * This will append the API key as a query parameter for direct image requests\n * @param {string} endpoint - Image endpoint (e.g., 'books/1/image')\n * @returns {string} - Authenticated image URL\n */\nexport const getImageUrl = (endpoint) => {\n  return `${API_URL}/${endpoint.replace(/^\\//, \"\")}?api_key=${API_KEY}`;\n};\n\n/**\n * Fetch data from API with proper authorization headers\n * @param {string} endpoint - API endpoint (without base URL)\n * @param {Object} options - Fetch options\n * @returns {Promise<Object>} - Response data\n */\nexport const fetchApi = async (endpoint, options = {}) => {\n  try {\n    const url = `${API_URL}/${endpoint.replace(/^\\//, \"\")}`;\n\n    // Ensure we always have the basic headers structure\n    const headers = {\n      \"Content-Type\": \"application/json\",\n      Accept: \"application/json\",\n      \"X-Requested-With\": \"XMLHttpRequest\",\n      \"X-API-Key\": API_KEY,\n      ...(options.headers || {}),\n    };\n\n    // Debug the request\n    console.log(`API Request to: ${url}`);\n    console.log(\"Request headers:\", headers);\n    console.log(\"Request method:\", options.method || \"GET\");\n    if (options.body) {\n      console.log(\"Request body:\", options.body);\n    }\n\n    const response = await fetch(url, {\n      ...options,\n      headers,\n    });\n\n    // Always get the response text first\n    const responseText = await response.text();\n\n    if (!response.ok) {\n      console.error(`API Error: ${response.status} ${response.statusText}`);\n      console.error(\n        \"Response headers:\",\n        Object.fromEntries([...response.headers])\n      );\n      console.error(\"Response body:\", responseText);\n\n      // Try to parse the error message from the response\n      let errorMessage = `HTTP error! Status: ${response.status}`;\n      let errorData = null;\n\n      try {\n        errorData = JSON.parse(responseText);\n        if (errorData.message) {\n          errorMessage = errorData.message;\n        } else if (errorData.error) {\n          errorMessage = errorData.error;\n        }\n        // Include validation errors if available\n        if (errorData.errors) {\n          errorMessage +=\n            \": \" + Object.values(errorData.errors).flat().join(\", \");\n        }\n      } catch (e) {\n        // If we can't parse the JSON, just use the raw text\n        if (responseText) {\n          errorMessage = responseText;\n        }\n      }\n\n      throw new Error(errorMessage);\n    }\n\n    // Parse the successful response\n    try {\n      return JSON.parse(responseText);\n    } catch (e) {\n      console.warn(\"Response is not valid JSON, returning raw text\");\n      return { success: true, data: responseText };\n    }\n  } catch (error) {\n    console.error(`API Error (${endpoint}):`, error);\n    throw error;\n  }\n};\n\n/**\n * GET request\n * @param {string} endpoint - API endpoint\n * @param {Object} options - Additional fetch options\n * @returns {Promise<Object>} - Response data\n */\nexport const get = (endpoint, options = {}) => {\n  return fetchApi(endpoint, {\n    method: \"GET\",\n    ...options,\n  });\n};\n\n/**\n * POST request\n * @param {string} endpoint - API endpoint\n * @param {Object} data - Request body data\n * @param {Object} options - Additional fetch options\n * @returns {Promise<Object>} - Response data\n */\nexport const post = (endpoint, data, options = {}) => {\n  return fetchApi(endpoint, {\n    method: \"POST\",\n    body: JSON.stringify(data),\n    ...options,\n  });\n};\n\n/**\n * PUT request\n * @param {string} endpoint - API endpoint\n * @param {Object} data - Request body data\n * @param {Object} options - Additional fetch options\n * @returns {Promise<Object>} - Response data\n */\nexport const put = (endpoint, data, options = {}) => {\n  return fetchApi(endpoint, {\n    method: \"PUT\",\n    body: JSON.stringify(data),\n    ...options,\n  });\n};\n\n/**\n * DELETE request\n * @param {string} endpoint - API endpoint\n * @param {Object} options - Additional fetch options\n * @returns {Promise<Object>} - Response data\n */\nexport const del = (endpoint, options = {}) => {\n  return fetchApi(endpoint, {\n    method: \"DELETE\",\n    ...options,\n  });\n};\n\nexport default {\n  fetchApi,\n  get,\n  post,\n  put,\n  del,\n  getImageUrl,\n};\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,OAAO,EAAEC,aAAa,QAAQ,sBAAsB;;AAE3E;AACA,MAAMC,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;;AAEvD;AACA,MAAMC,OAAO,GAAGH,eAAe,IAAIH,YAAY;AAE/CO,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEF,OAAO,CAAC;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,WAAW,GAAIC,QAAQ,IAAK;EACvC,OAAO,GAAGJ,OAAO,IAAII,QAAQ,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,YAAYV,OAAO,EAAE;AACvE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMW,QAAQ,GAAG,MAAAA,CAAOF,QAAQ,EAAEG,OAAO,GAAG,CAAC,CAAC,KAAK;EACxD,IAAI;IACF,MAAMC,GAAG,GAAG,GAAGR,OAAO,IAAII,QAAQ,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE;;IAEvD;IACA,MAAMI,OAAO,GAAG;MACd,cAAc,EAAE,kBAAkB;MAClCC,MAAM,EAAE,kBAAkB;MAC1B,kBAAkB,EAAE,gBAAgB;MACpC,WAAW,EAAEf,OAAO;MACpB,IAAIY,OAAO,CAACE,OAAO,IAAI,CAAC,CAAC;IAC3B,CAAC;;IAED;IACAR,OAAO,CAACC,GAAG,CAAC,mBAAmBM,GAAG,EAAE,CAAC;IACrCP,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEO,OAAO,CAAC;IACxCR,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEK,OAAO,CAACI,MAAM,IAAI,KAAK,CAAC;IACvD,IAAIJ,OAAO,CAACK,IAAI,EAAE;MAChBX,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEK,OAAO,CAACK,IAAI,CAAC;IAC5C;IAEA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACN,GAAG,EAAE;MAChC,GAAGD,OAAO;MACVE;IACF,CAAC,CAAC;;IAEF;IACA,MAAMM,YAAY,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAE1C,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;MAChBhB,OAAO,CAACiB,KAAK,CAAC,cAAcL,QAAQ,CAACM,MAAM,IAAIN,QAAQ,CAACO,UAAU,EAAE,CAAC;MACrEnB,OAAO,CAACiB,KAAK,CACX,mBAAmB,EACnBG,MAAM,CAACC,WAAW,CAAC,CAAC,GAAGT,QAAQ,CAACJ,OAAO,CAAC,CAC1C,CAAC;MACDR,OAAO,CAACiB,KAAK,CAAC,gBAAgB,EAAEH,YAAY,CAAC;;MAE7C;MACA,IAAIQ,YAAY,GAAG,uBAAuBV,QAAQ,CAACM,MAAM,EAAE;MAC3D,IAAIK,SAAS,GAAG,IAAI;MAEpB,IAAI;QACFA,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACX,YAAY,CAAC;QACpC,IAAIS,SAAS,CAACG,OAAO,EAAE;UACrBJ,YAAY,GAAGC,SAAS,CAACG,OAAO;QAClC,CAAC,MAAM,IAAIH,SAAS,CAACN,KAAK,EAAE;UAC1BK,YAAY,GAAGC,SAAS,CAACN,KAAK;QAChC;QACA;QACA,IAAIM,SAAS,CAACI,MAAM,EAAE;UACpBL,YAAY,IACV,IAAI,GAAGF,MAAM,CAACQ,MAAM,CAACL,SAAS,CAACI,MAAM,CAAC,CAACE,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QAC5D;MACF,CAAC,CAAC,OAAOC,CAAC,EAAE;QACV;QACA,IAAIjB,YAAY,EAAE;UAChBQ,YAAY,GAAGR,YAAY;QAC7B;MACF;MAEA,MAAM,IAAIkB,KAAK,CAACV,YAAY,CAAC;IAC/B;;IAEA;IACA,IAAI;MACF,OAAOE,IAAI,CAACC,KAAK,CAACX,YAAY,CAAC;IACjC,CAAC,CAAC,OAAOiB,CAAC,EAAE;MACV/B,OAAO,CAACiC,IAAI,CAAC,gDAAgD,CAAC;MAC9D,OAAO;QAAEC,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAErB;MAAa,CAAC;IAC9C;EACF,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdjB,OAAO,CAACiB,KAAK,CAAC,cAAcd,QAAQ,IAAI,EAAEc,KAAK,CAAC;IAChD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMmB,GAAG,GAAGA,CAACjC,QAAQ,EAAEG,OAAO,GAAG,CAAC,CAAC,KAAK;EAC7C,OAAOD,QAAQ,CAACF,QAAQ,EAAE;IACxBO,MAAM,EAAE,KAAK;IACb,GAAGJ;EACL,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM+B,IAAI,GAAGA,CAAClC,QAAQ,EAAEgC,IAAI,EAAE7B,OAAO,GAAG,CAAC,CAAC,KAAK;EACpD,OAAOD,QAAQ,CAACF,QAAQ,EAAE;IACxBO,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEa,IAAI,CAACc,SAAS,CAACH,IAAI,CAAC;IAC1B,GAAG7B;EACL,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMiC,GAAG,GAAGA,CAACpC,QAAQ,EAAEgC,IAAI,EAAE7B,OAAO,GAAG,CAAC,CAAC,KAAK;EACnD,OAAOD,QAAQ,CAACF,QAAQ,EAAE;IACxBO,MAAM,EAAE,KAAK;IACbC,IAAI,EAAEa,IAAI,CAACc,SAAS,CAACH,IAAI,CAAC;IAC1B,GAAG7B;EACL,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMkC,GAAG,GAAGA,CAACrC,QAAQ,EAAEG,OAAO,GAAG,CAAC,CAAC,KAAK;EAC7C,OAAOD,QAAQ,CAACF,QAAQ,EAAE;IACxBO,MAAM,EAAE,QAAQ;IAChB,GAAGJ;EACL,CAAC,CAAC;AACJ,CAAC;AAED,eAAe;EACbD,QAAQ;EACR+B,GAAG;EACHC,IAAI;EACJE,GAAG;EACHC,GAAG;EACHtC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}