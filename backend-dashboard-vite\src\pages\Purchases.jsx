import React, { useState, useEffect } from 'react';
import axios from '../utils/axios';
import { toast } from 'react-toastify';

function Purchases() {
  const [purchases, setPurchases] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [pagination, setPagination] = useState({
    currentPage: 1,
    perPage: 10,
    total: 0,
    lastPage: 1
  });
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    completed: 0,
    cancelled: 0
  });

  // Fetch purchases
  useEffect(() => {
    const fetchPurchases = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/purchases', {
          params: {
            page: pagination.currentPage,
            per_page: pagination.perPage,
            search: searchQuery
          }
        });

        // Log the response structure for debugging
        console.log('Purchases API Response:', response.data);

        // Handle the API response structure
        let purchasesData = [];
        if (response.data?.success && Array.isArray(response.data.data)) {
          // Direct array in data field
          purchasesData = response.data.data;
        } else if (response.data?.data?.data && Array.isArray(response.data.data.data)) {
          // Nested data structure
          purchasesData = response.data.data.data;
        }

        // Transform the purchases data
        const transformedPurchases = purchasesData.map(purchase => ({
          id: purchase.PurchaseID || purchase.id || Math.random().toString(36).substr(2, 9),
          ordered_by: purchase.OrderedBy || purchase.ordered_by || '-',
          items_count: parseInt(purchase.ItemsCount || purchase.items_count || 0),
          status: (purchase.Status || purchase.status || 'pending').toLowerCase(),
          created_at: purchase.CreatedAt || purchase.created_at || new Date().toISOString(),
          total_amount: parseFloat(purchase.TotalAmount || purchase.total_amount || 0).toFixed(2),
          payment_method: purchase.PaymentMethod || purchase.payment_method || '-',
          payment_status: (purchase.PaymentStatus || purchase.payment_status || 'pending').toLowerCase()
        }));

        console.log('Transformed Purchases:', transformedPurchases);
        setPurchases(transformedPurchases);

        // Calculate stats
        const total = transformedPurchases.length;
        const pending = transformedPurchases.filter(p => p.status === 'pending').length;
        const completed = transformedPurchases.filter(p => p.status === 'completed').length;
        const cancelled = transformedPurchases.filter(p => p.status === 'cancelled').length;

        setStats({ total, pending, completed, cancelled });

        // Update pagination
        setPagination(prev => ({
          ...prev,
          total,
          lastPage: Math.ceil(total / prev.perPage)
        }));
      } catch (error) {
        console.error('Error fetching purchases:', error);
        toast.error('Failed to fetch purchases. Please try again.');
        setPurchases([]);
        setStats({ total: 0, pending: 0, completed: 0, cancelled: 0 });
      } finally {
        setLoading(false);
      }
    };

    const debounceTimer = setTimeout(() => {
      fetchPurchases();
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [pagination.currentPage, pagination.perPage, searchQuery]);

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    setPagination(prev => ({
      ...prev,
      currentPage: 1
    }));
  };

  const handlePageChange = (page) => {
    setPagination(prev => ({
      ...prev,
      currentPage: page
    }));
  };

  return (
    <div className="page-content">
      <div className="page-header mb-4">
        <h1>Purchases</h1>
      </div>

      {/* Stats Cards */}
      <div className="row mb-4">
        <div className="col-md-6 col-xl-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="avatar-md bg-primary bg-opacity-10 rounded">
                  <i className="ri-shopping-bag-line fs-24 text-primary"></i>
                </div>
                <div className="ms-3">
                  <h4 className="mb-1">Total Purchases</h4>
                  <p className="fs-18 mb-0">{stats.total}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-md-6 col-xl-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="avatar-md bg-warning bg-opacity-10 rounded">
                  <i className="ri-time-line fs-24 text-warning"></i>
                </div>
                <div className="ms-3">
                  <h4 className="mb-1">Pending</h4>
                  <p className="fs-18 mb-0">{stats.pending}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-md-6 col-xl-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="avatar-md bg-success bg-opacity-10 rounded">
                  <i className="ri-check-line fs-24 text-success"></i>
                </div>
                <div className="ms-3">
                  <h4 className="mb-1">Completed</h4>
                  <p className="fs-18 mb-0">{stats.completed}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-md-6 col-xl-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="avatar-md bg-danger bg-opacity-10 rounded">
                  <i className="ri-close-line fs-24 text-danger"></i>
                </div>
                <div className="ms-3">
                  <h4 className="mb-1">Cancelled</h4>
                  <p className="fs-18 mb-0">{stats.cancelled}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Purchases Table */}
      <div className="card">
        <div className="card-header d-flex justify-content-between align-items-center">
          <h5 className="card-title mb-0">All Purchases</h5>
          <div className="d-flex gap-2">
            <input
              type="text"
              className="form-control"
              placeholder="Search purchases..."
              style={{ width: "200px" }}
              value={searchQuery}
              onChange={handleSearch}
            />
            <button className="btn btn-primary">
              <i className="ri-add-line me-1"></i>
              Add Purchase
            </button>
          </div>
        </div>
        <div className="card-body">
          <div className="table-responsive">
            <table className="table table-hover">
              <thead className="bg-light">
                <tr>
                  <th>ID</th>
                  <th>Order By</th>
                  <th>Items</th>
                  <th>Purchase Status</th>
                  <th>Date</th>
                  <th>Total</th>
                  <th>Payment Method</th>
                  <th>Payment Status</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="9" className="text-center py-4">
                      <div className="spinner-border text-primary" role="status">
                        <span className="visually-hidden">Loading...</span>
                      </div>
                    </td>
                  </tr>
                ) : purchases.length === 0 ? (
                  <tr>
                    <td colSpan="9" className="text-center py-4">
                      No purchases found
                    </td>
                  </tr>
                ) : (
                  purchases.map((purchase) => (
                    <tr key={`purchase-${purchase.id}`}>
                      <td>#{purchase.id}</td>
                      <td>{purchase.ordered_by}</td>
                      <td>{purchase.items_count}</td>
                      <td>
                        <span className={`badge bg-${
                          purchase.status === 'completed' ? 'success' :
                          purchase.status === 'pending' ? 'warning' :
                          'danger'
                        }`}>
                          {purchase.status}
                        </span>
                      </td>
                      <td>{new Date(purchase.created_at).toLocaleDateString()}</td>
                      <td>${purchase.total_amount}</td>
                      <td>{purchase.payment_method}</td>
                      <td>
                        <span className={`badge bg-${
                          purchase.payment_status === 'paid' ? 'success' :
                          purchase.payment_status === 'pending' ? 'warning' :
                          'danger'
                        }`}>
                          {purchase.payment_status}
                        </span>
                      </td>
                      <td>
                        <button className="btn btn-sm btn-light me-2" title="View">
                          <i className="ri-eye-line"></i>
                        </button>
                        <button className="btn btn-sm btn-light" title="More">
                          <i className="ri-more-2-fill"></i>
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {!loading && purchases.length > 0 && (
            <div className="d-flex justify-content-between align-items-center mt-4">
              <div>
                Showing {((pagination.currentPage - 1) * pagination.perPage) + 1} to {Math.min(pagination.currentPage * pagination.perPage, pagination.total)} of {pagination.total} entries
              </div>
              <div className="d-flex gap-2">
                <button
                  className="btn btn-light"
                  onClick={() => handlePageChange(pagination.currentPage - 1)}
                  disabled={pagination.currentPage === 1}
                >
                  Previous
                </button>
                {Array.from({ length: pagination.lastPage }, (_, i) => i + 1)
                  .filter(page => {
                    const current = pagination.currentPage;
                    return page === 1 || 
                           page === pagination.lastPage || 
                           (page >= current - 1 && page <= current + 1);
                  })
                  .map((page, index, array) => (
                    <React.Fragment key={page}>
                      {index > 0 && array[index - 1] !== page - 1 && (
                        <span className="btn btn-light disabled">...</span>
                      )}
                      <button
                        className={`btn ${pagination.currentPage === page ? 'btn-primary' : 'btn-light'}`}
                        onClick={() => handlePageChange(page)}
                      >
                        {page}
                      </button>
                    </React.Fragment>
                  ))}
                <button
                  className="btn btn-light"
                  onClick={() => handlePageChange(pagination.currentPage + 1)}
                  disabled={pagination.currentPage === pagination.lastPage}
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Purchases; 