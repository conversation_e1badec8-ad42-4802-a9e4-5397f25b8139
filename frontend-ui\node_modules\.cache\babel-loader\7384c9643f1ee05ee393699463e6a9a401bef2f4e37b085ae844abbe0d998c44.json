{"ast": null, "code": "import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport Dropdown from './Dropdown';\nimport DropdownToggle from './DropdownToggle';\nimport DropdownMenu from './DropdownMenu';\nimport { alignPropType } from './types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst propTypes = {\n  /**\n   * An html id attribute for the Toggle button, necessary for assistive technologies, such as screen readers.\n   * @type {string}\n   */\n  id: PropTypes.string,\n  /** An `href` passed to the Toggle component */\n  href: PropTypes.string,\n  /** An `onClick` handler passed to the Toggle component */\n  onClick: PropTypes.func,\n  /** The content of the non-toggle Button.  */\n  title: PropTypes.node.isRequired,\n  /** Disables both Buttons  */\n  disabled: PropTypes.bool,\n  /**\n   * Aligns the dropdown menu.\n   *\n   * _see [DropdownMenu](#dropdown-menu-props) for more details_\n   *\n   * @type {\"start\"|\"end\"|{ sm: \"start\"|\"end\" }|{ md: \"start\"|\"end\" }|{ lg: \"start\"|\"end\" }|{ xl: \"start\"|\"end\"}|{ xxl: \"start\"|\"end\"} }\n   */\n  align: alignPropType,\n  /** An ARIA accessible role applied to the Menu component. When set to 'menu', The dropdown */\n  menuRole: PropTypes.string,\n  /** Whether to render the dropdown menu in the DOM before the first time it is shown */\n  renderMenuOnMount: PropTypes.bool,\n  /**\n   *  Which event when fired outside the component will cause it to be closed.\n   *\n   * _see [DropdownMenu](#dropdown-menu-props) for more details_\n   */\n  rootCloseEvent: PropTypes.string,\n  /**\n   * Menu color variant.\n   *\n   * Omitting this will use the default light color.\n   */\n  menuVariant: PropTypes.oneOf(['dark']),\n  /**\n   * Allow Dropdown to flip in case of an overlapping on the reference element. For more information refer to\n   * Popper.js's flip [docs](https://popper.js.org/docs/v2/modifiers/flip/).\n   *\n   */\n  flip: PropTypes.bool,\n  /** @ignore */\n  bsPrefix: PropTypes.string,\n  /** @ignore */\n  variant: PropTypes.string,\n  /** @ignore */\n  size: PropTypes.string\n};\n\n/**\n * A convenience component for simple or general use dropdowns. Renders a `Button` toggle and all `children`\n * are passed directly to the default `Dropdown.Menu`. This component accepts all of\n * [`Dropdown`'s props](#dropdown-props).\n *\n * _All unknown props are passed through to the `Dropdown` component._ Only\n * the Button `variant`, `size` and `bsPrefix` props are passed to the toggle,\n * along with menu-related props are passed to the `Dropdown.Menu`\n */\nconst DropdownButton = /*#__PURE__*/React.forwardRef(({\n  title,\n  children,\n  bsPrefix,\n  rootCloseEvent,\n  variant,\n  size,\n  menuRole,\n  renderMenuOnMount,\n  disabled,\n  href,\n  id,\n  menuVariant,\n  flip,\n  ...props\n}, ref) => /*#__PURE__*/_jsxs(Dropdown, {\n  ref: ref,\n  ...props,\n  children: [/*#__PURE__*/_jsx(DropdownToggle, {\n    id: id,\n    href: href,\n    size: size,\n    variant: variant,\n    disabled: disabled,\n    childBsPrefix: bsPrefix,\n    children: title\n  }), /*#__PURE__*/_jsx(DropdownMenu, {\n    role: menuRole,\n    renderOnMount: renderMenuOnMount,\n    rootCloseEvent: rootCloseEvent,\n    variant: menuVariant,\n    flip: flip,\n    children: children\n  })]\n}));\nDropdownButton.displayName = 'DropdownButton';\nDropdownButton.propTypes = propTypes;\nexport default DropdownButton;", "map": {"version": 3, "names": ["React", "PropTypes", "Dropdown", "DropdownToggle", "DropdownMenu", "alignPropType", "jsx", "_jsx", "jsxs", "_jsxs", "propTypes", "id", "string", "href", "onClick", "func", "title", "node", "isRequired", "disabled", "bool", "align", "menuRole", "renderMenuOnMount", "rootCloseEvent", "menuVariant", "oneOf", "flip", "bsPrefix", "variant", "size", "DropdownButton", "forwardRef", "children", "props", "ref", "childBsPrefix", "role", "renderOnMount", "displayName"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/react-bootstrap/esm/DropdownButton.js"], "sourcesContent": ["import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport Dropdown from './Dropdown';\nimport DropdownToggle from './DropdownToggle';\nimport DropdownMenu from './DropdownMenu';\nimport { alignPropType } from './types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst propTypes = {\n  /**\n   * An html id attribute for the Toggle button, necessary for assistive technologies, such as screen readers.\n   * @type {string}\n   */\n  id: PropTypes.string,\n  /** An `href` passed to the Toggle component */\n  href: PropTypes.string,\n  /** An `onClick` handler passed to the Toggle component */\n  onClick: PropTypes.func,\n  /** The content of the non-toggle Button.  */\n  title: PropTypes.node.isRequired,\n  /** Disables both Buttons  */\n  disabled: PropTypes.bool,\n  /**\n   * Aligns the dropdown menu.\n   *\n   * _see [DropdownMenu](#dropdown-menu-props) for more details_\n   *\n   * @type {\"start\"|\"end\"|{ sm: \"start\"|\"end\" }|{ md: \"start\"|\"end\" }|{ lg: \"start\"|\"end\" }|{ xl: \"start\"|\"end\"}|{ xxl: \"start\"|\"end\"} }\n   */\n  align: alignPropType,\n  /** An ARIA accessible role applied to the Menu component. When set to 'menu', The dropdown */\n  menuRole: PropTypes.string,\n  /** Whether to render the dropdown menu in the DOM before the first time it is shown */\n  renderMenuOnMount: PropTypes.bool,\n  /**\n   *  Which event when fired outside the component will cause it to be closed.\n   *\n   * _see [DropdownMenu](#dropdown-menu-props) for more details_\n   */\n  rootCloseEvent: PropTypes.string,\n  /**\n   * Menu color variant.\n   *\n   * Omitting this will use the default light color.\n   */\n  menuVariant: PropTypes.oneOf(['dark']),\n  /**\n   * Allow Dropdown to flip in case of an overlapping on the reference element. For more information refer to\n   * Popper.js's flip [docs](https://popper.js.org/docs/v2/modifiers/flip/).\n   *\n   */\n  flip: PropTypes.bool,\n  /** @ignore */\n  bsPrefix: PropTypes.string,\n  /** @ignore */\n  variant: PropTypes.string,\n  /** @ignore */\n  size: PropTypes.string\n};\n\n/**\n * A convenience component for simple or general use dropdowns. Renders a `Button` toggle and all `children`\n * are passed directly to the default `Dropdown.Menu`. This component accepts all of\n * [`Dropdown`'s props](#dropdown-props).\n *\n * _All unknown props are passed through to the `Dropdown` component._ Only\n * the Button `variant`, `size` and `bsPrefix` props are passed to the toggle,\n * along with menu-related props are passed to the `Dropdown.Menu`\n */\nconst DropdownButton = /*#__PURE__*/React.forwardRef(({\n  title,\n  children,\n  bsPrefix,\n  rootCloseEvent,\n  variant,\n  size,\n  menuRole,\n  renderMenuOnMount,\n  disabled,\n  href,\n  id,\n  menuVariant,\n  flip,\n  ...props\n}, ref) => /*#__PURE__*/_jsxs(Dropdown, {\n  ref: ref,\n  ...props,\n  children: [/*#__PURE__*/_jsx(DropdownToggle, {\n    id: id,\n    href: href,\n    size: size,\n    variant: variant,\n    disabled: disabled,\n    childBsPrefix: bsPrefix,\n    children: title\n  }), /*#__PURE__*/_jsx(DropdownMenu, {\n    role: menuRole,\n    renderOnMount: renderMenuOnMount,\n    rootCloseEvent: rootCloseEvent,\n    variant: menuVariant,\n    flip: flip,\n    children: children\n  })]\n}));\nDropdownButton.displayName = 'DropdownButton';\nDropdownButton.propTypes = propTypes;\nexport default DropdownButton;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,aAAa,QAAQ,SAAS;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,SAAS,GAAG;EAChB;AACF;AACA;AACA;EACEC,EAAE,EAAEV,SAAS,CAACW,MAAM;EACpB;EACAC,IAAI,EAAEZ,SAAS,CAACW,MAAM;EACtB;EACAE,OAAO,EAAEb,SAAS,CAACc,IAAI;EACvB;EACAC,KAAK,EAAEf,SAAS,CAACgB,IAAI,CAACC,UAAU;EAChC;EACAC,QAAQ,EAAElB,SAAS,CAACmB,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,KAAK,EAAEhB,aAAa;EACpB;EACAiB,QAAQ,EAAErB,SAAS,CAACW,MAAM;EAC1B;EACAW,iBAAiB,EAAEtB,SAAS,CAACmB,IAAI;EACjC;AACF;AACA;AACA;AACA;EACEI,cAAc,EAAEvB,SAAS,CAACW,MAAM;EAChC;AACF;AACA;AACA;AACA;EACEa,WAAW,EAAExB,SAAS,CAACyB,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;EACtC;AACF;AACA;AACA;AACA;EACEC,IAAI,EAAE1B,SAAS,CAACmB,IAAI;EACpB;EACAQ,QAAQ,EAAE3B,SAAS,CAACW,MAAM;EAC1B;EACAiB,OAAO,EAAE5B,SAAS,CAACW,MAAM;EACzB;EACAkB,IAAI,EAAE7B,SAAS,CAACW;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmB,cAAc,GAAG,aAAa/B,KAAK,CAACgC,UAAU,CAAC,CAAC;EACpDhB,KAAK;EACLiB,QAAQ;EACRL,QAAQ;EACRJ,cAAc;EACdK,OAAO;EACPC,IAAI;EACJR,QAAQ;EACRC,iBAAiB;EACjBJ,QAAQ;EACRN,IAAI;EACJF,EAAE;EACFc,WAAW;EACXE,IAAI;EACJ,GAAGO;AACL,CAAC,EAAEC,GAAG,KAAK,aAAa1B,KAAK,CAACP,QAAQ,EAAE;EACtCiC,GAAG,EAAEA,GAAG;EACR,GAAGD,KAAK;EACRD,QAAQ,EAAE,CAAC,aAAa1B,IAAI,CAACJ,cAAc,EAAE;IAC3CQ,EAAE,EAAEA,EAAE;IACNE,IAAI,EAAEA,IAAI;IACViB,IAAI,EAAEA,IAAI;IACVD,OAAO,EAAEA,OAAO;IAChBV,QAAQ,EAAEA,QAAQ;IAClBiB,aAAa,EAAER,QAAQ;IACvBK,QAAQ,EAAEjB;EACZ,CAAC,CAAC,EAAE,aAAaT,IAAI,CAACH,YAAY,EAAE;IAClCiC,IAAI,EAAEf,QAAQ;IACdgB,aAAa,EAAEf,iBAAiB;IAChCC,cAAc,EAAEA,cAAc;IAC9BK,OAAO,EAAEJ,WAAW;IACpBE,IAAI,EAAEA,IAAI;IACVM,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC;AACHF,cAAc,CAACQ,WAAW,GAAG,gBAAgB;AAC7CR,cAAc,CAACrB,SAAS,GAAGA,SAAS;AACpC,eAAeqB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}