{"ast": null, "code": "import * as React from 'react';\nimport classNames from 'classnames';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default className => /*#__PURE__*/\n// eslint-disable-next-line react/display-name\nReact.forwardRef((p, ref) => /*#__PURE__*/_jsx(\"div\", {\n  ...p,\n  ref: ref,\n  className: classNames(p.className, className)\n}));", "map": {"version": 3, "names": ["React", "classNames", "jsx", "_jsx", "className", "forwardRef", "p", "ref"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/react-bootstrap/esm/divWithClassName.js"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default (className =>\n/*#__PURE__*/\n// eslint-disable-next-line react/display-name\nReact.forwardRef((p, ref) => /*#__PURE__*/_jsx(\"div\", {\n  ...p,\n  ref: ref,\n  className: classNames(p.className, className)\n})));"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAgBC,SAAS,IACzB;AACA;AACAJ,KAAK,CAACK,UAAU,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK,aAAaJ,IAAI,CAAC,KAAK,EAAE;EACpD,GAAGG,CAAC;EACJC,GAAG,EAAEA,GAAG;EACRH,SAAS,EAAEH,UAAU,CAACK,CAAC,CAACF,SAAS,EAAEA,SAAS;AAC9C,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}