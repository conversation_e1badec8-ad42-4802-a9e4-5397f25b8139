.sidebar {
  width: 250px;
  min-height: 100vh;
  background-color: #2a3042;
  color: #a6b0cf;
  transition: all 0.3s ease;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.sidebar.collapsed {
  width: 70px;
}

.sidebar-header {
  height: 64px;
  display: flex;
  align-items: center;
  padding: 0 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header .logo {
  font-size: 1.25rem;
  font-weight: 600;
  color: #fff;
  text-decoration: none;
}

.sidebar-header .logo-icon {
  display: none;
}

.sidebar.collapsed .logo {
  display: none;
}

.sidebar.collapsed .logo-icon {
  display: block;
}

.sidebar-nav {
  padding: 1rem 0;
}

.nav-section {
  padding: 0 1rem;
  margin-bottom: 1rem;
}

.nav-section-title {
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #6a7187;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
}

.nav-item {
  margin-bottom: 0.25rem;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: #a6b0cf;
  text-decoration: none;
  border-radius: 5px;
  transition: all 0.2s;
}

.nav-link:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
  color: #fff;
  background-color: #556ee6;
}

.nav-link i {
  width: 20px;
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

.sidebar.collapsed .nav-section-title,
.sidebar.collapsed .nav-link span {
  display: none;
}

.sidebar.collapsed .nav-link {
  justify-content: center;
  padding: 0.75rem;
}

.sidebar.collapsed .nav-link i {
  margin-right: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -250px;
  }

  .sidebar.active {
    left: 0;
  }

  .sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }

  .sidebar.active + .sidebar-overlay {
    display: block;
  }
} 