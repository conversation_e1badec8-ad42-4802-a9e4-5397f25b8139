{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "map": {"version": 3, "names": ["React", "classNames", "useBootstrapPrefix", "divWithClassName", "jsx", "_jsx", "DivStyledAsH5", "CardTitle", "forwardRef", "className", "bsPrefix", "as", "Component", "props", "ref", "displayName"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/react-bootstrap/esm/CardTitle.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,aAAa,GAAGH,gBAAgB,CAAC,IAAI,CAAC;AAC5C,MAAMI,SAAS,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAAC;EAC/CC,SAAS;EACTC,QAAQ;EACRC,EAAE,EAAEC,SAAS,GAAGN,aAAa;EAC7B,GAAGO;AACL,CAAC,EAAEC,GAAG,KAAK;EACTJ,QAAQ,GAAGR,kBAAkB,CAACQ,QAAQ,EAAE,YAAY,CAAC;EACrD,OAAO,aAAaL,IAAI,CAACO,SAAS,EAAE;IAClCE,GAAG,EAAEA,GAAG;IACRL,SAAS,EAAER,UAAU,CAACQ,SAAS,EAAEC,QAAQ,CAAC;IAC1C,GAAGG;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,SAAS,CAACQ,WAAW,GAAG,WAAW;AACnC,eAAeR,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}