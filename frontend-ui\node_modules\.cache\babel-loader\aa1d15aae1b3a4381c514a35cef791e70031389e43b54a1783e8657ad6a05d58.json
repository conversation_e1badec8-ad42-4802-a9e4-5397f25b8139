{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AbstractModalHeader from './AbstractModalHeader';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ModalHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  closeLabel = 'Close',\n  closeButton = false,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'modal-header');\n  return /*#__PURE__*/_jsx(AbstractModalHeader, {\n    ref: ref,\n    ...props,\n    className: classNames(className, bsPrefix),\n    closeLabel: closeLabel,\n    closeButton: closeButton\n  });\n});\nModalHeader.displayName = 'ModalHeader';\nexport default ModalHeader;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "AbstractModalHeader", "jsx", "_jsx", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardRef", "bsPrefix", "className", "<PERSON><PERSON><PERSON><PERSON>", "closeButton", "props", "ref", "displayName"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/react-bootstrap/esm/ModalHeader.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AbstractModalHeader from './AbstractModalHeader';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ModalHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  closeLabel = 'Close',\n  closeButton = false,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'modal-header');\n  return /*#__PURE__*/_jsx(AbstractModalHeader, {\n    ref: ref,\n    ...props,\n    className: classNames(className, bsPrefix),\n    closeLabel: closeLabel,\n    closeButton: closeButton\n  });\n});\nModalHeader.displayName = 'ModalHeader';\nexport default ModalHeader;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,WAAW,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAC;EACjDC,QAAQ;EACRC,SAAS;EACTC,UAAU,GAAG,OAAO;EACpBC,WAAW,GAAG,KAAK;EACnB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTL,QAAQ,GAAGN,kBAAkB,CAACM,QAAQ,EAAE,cAAc,CAAC;EACvD,OAAO,aAAaH,IAAI,CAACF,mBAAmB,EAAE;IAC5CU,GAAG,EAAEA,GAAG;IACR,GAAGD,KAAK;IACRH,SAAS,EAAET,UAAU,CAACS,SAAS,EAAED,QAAQ,CAAC;IAC1CE,UAAU,EAAEA,UAAU;IACtBC,WAAW,EAAEA;EACf,CAAC,CAAC;AACJ,CAAC,CAAC;AACFL,WAAW,CAACQ,WAAW,GAAG,aAAa;AACvC,eAAeR,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}