{"ast": null, "code": "import { l as elementTransitionEnd } from './utils.mjs';\nfunction effectVirtualTransitionEnd(_ref) {\n  let {\n    swiper,\n    duration,\n    transformElements,\n    allSlides\n  } = _ref;\n  const {\n    activeIndex\n  } = swiper;\n  const getSlide = el => {\n    if (!el.parentElement) {\n      // assume shadow root\n      const slide = swiper.slides.find(slideEl => slideEl.shadowRoot && slideEl.shadowRoot === el.parentNode);\n      return slide;\n    }\n    return el.parentElement;\n  };\n  if (swiper.params.virtualTranslate && duration !== 0) {\n    let eventTriggered = false;\n    let transitionEndTarget;\n    if (allSlides) {\n      transitionEndTarget = transformElements;\n    } else {\n      transitionEndTarget = transformElements.filter(transformEl => {\n        const el = transformEl.classList.contains('swiper-slide-transform') ? getSlide(transformEl) : transformEl;\n        return swiper.getSlideIndex(el) === activeIndex;\n      });\n    }\n    transitionEndTarget.forEach(el => {\n      elementTransitionEnd(el, () => {\n        if (eventTriggered) return;\n        if (!swiper || swiper.destroyed) return;\n        eventTriggered = true;\n        swiper.animating = false;\n        const evt = new window.CustomEvent('transitionend', {\n          bubbles: true,\n          cancelable: true\n        });\n        swiper.wrapperEl.dispatchEvent(evt);\n      });\n    });\n  }\n}\nexport { effectVirtualTransitionEnd as e };", "map": {"version": 3, "names": ["l", "elementTransitionEnd", "effectVirtualTransitionEnd", "_ref", "swiper", "duration", "transformElements", "allSlides", "activeIndex", "getSlide", "el", "parentElement", "slide", "slides", "find", "slideEl", "shadowRoot", "parentNode", "params", "virtualTranslate", "eventTriggered", "transitionEndTarget", "filter", "transformEl", "classList", "contains", "getSlideIndex", "for<PERSON>ach", "destroyed", "animating", "evt", "window", "CustomEvent", "bubbles", "cancelable", "wrapperEl", "dispatchEvent", "e"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/swiper/shared/effect-virtual-transition-end.mjs"], "sourcesContent": ["import { l as elementTransitionEnd } from './utils.mjs';\n\nfunction effectVirtualTransitionEnd(_ref) {\n  let {\n    swiper,\n    duration,\n    transformElements,\n    allSlides\n  } = _ref;\n  const {\n    activeIndex\n  } = swiper;\n  const getSlide = el => {\n    if (!el.parentElement) {\n      // assume shadow root\n      const slide = swiper.slides.find(slideEl => slideEl.shadowRoot && slideEl.shadowRoot === el.parentNode);\n      return slide;\n    }\n    return el.parentElement;\n  };\n  if (swiper.params.virtualTranslate && duration !== 0) {\n    let eventTriggered = false;\n    let transitionEndTarget;\n    if (allSlides) {\n      transitionEndTarget = transformElements;\n    } else {\n      transitionEndTarget = transformElements.filter(transformEl => {\n        const el = transformEl.classList.contains('swiper-slide-transform') ? getSlide(transformEl) : transformEl;\n        return swiper.getSlideIndex(el) === activeIndex;\n      });\n    }\n    transitionEndTarget.forEach(el => {\n      elementTransitionEnd(el, () => {\n        if (eventTriggered) return;\n        if (!swiper || swiper.destroyed) return;\n        eventTriggered = true;\n        swiper.animating = false;\n        const evt = new window.CustomEvent('transitionend', {\n          bubbles: true,\n          cancelable: true\n        });\n        swiper.wrapperEl.dispatchEvent(evt);\n      });\n    });\n  }\n}\n\nexport { effectVirtualTransitionEnd as e };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,oBAAoB,QAAQ,aAAa;AAEvD,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EACxC,IAAI;IACFC,MAAM;IACNC,QAAQ;IACRC,iBAAiB;IACjBC;EACF,CAAC,GAAGJ,IAAI;EACR,MAAM;IACJK;EACF,CAAC,GAAGJ,MAAM;EACV,MAAMK,QAAQ,GAAGC,EAAE,IAAI;IACrB,IAAI,CAACA,EAAE,CAACC,aAAa,EAAE;MACrB;MACA,MAAMC,KAAK,GAAGR,MAAM,CAACS,MAAM,CAACC,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACC,UAAU,IAAID,OAAO,CAACC,UAAU,KAAKN,EAAE,CAACO,UAAU,CAAC;MACvG,OAAOL,KAAK;IACd;IACA,OAAOF,EAAE,CAACC,aAAa;EACzB,CAAC;EACD,IAAIP,MAAM,CAACc,MAAM,CAACC,gBAAgB,IAAId,QAAQ,KAAK,CAAC,EAAE;IACpD,IAAIe,cAAc,GAAG,KAAK;IAC1B,IAAIC,mBAAmB;IACvB,IAAId,SAAS,EAAE;MACbc,mBAAmB,GAAGf,iBAAiB;IACzC,CAAC,MAAM;MACLe,mBAAmB,GAAGf,iBAAiB,CAACgB,MAAM,CAACC,WAAW,IAAI;QAC5D,MAAMb,EAAE,GAAGa,WAAW,CAACC,SAAS,CAACC,QAAQ,CAAC,wBAAwB,CAAC,GAAGhB,QAAQ,CAACc,WAAW,CAAC,GAAGA,WAAW;QACzG,OAAOnB,MAAM,CAACsB,aAAa,CAAChB,EAAE,CAAC,KAAKF,WAAW;MACjD,CAAC,CAAC;IACJ;IACAa,mBAAmB,CAACM,OAAO,CAACjB,EAAE,IAAI;MAChCT,oBAAoB,CAACS,EAAE,EAAE,MAAM;QAC7B,IAAIU,cAAc,EAAE;QACpB,IAAI,CAAChB,MAAM,IAAIA,MAAM,CAACwB,SAAS,EAAE;QACjCR,cAAc,GAAG,IAAI;QACrBhB,MAAM,CAACyB,SAAS,GAAG,KAAK;QACxB,MAAMC,GAAG,GAAG,IAAIC,MAAM,CAACC,WAAW,CAAC,eAAe,EAAE;UAClDC,OAAO,EAAE,IAAI;UACbC,UAAU,EAAE;QACd,CAAC,CAAC;QACF9B,MAAM,CAAC+B,SAAS,CAACC,aAAa,CAACN,GAAG,CAAC;MACrC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF;AAEA,SAAS5B,0BAA0B,IAAImC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}