import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from '../utils/axios';
import { toast } from 'react-toastify';

function Orders() {
  const navigate = useNavigate();
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    delivered: 0,
    cancelled: 0
  });
  const [pagination, setPagination] = useState({
    currentPage: 1,
    perPage: 10,
    total: 0,
    lastPage: 1
  });

  // Fetch orders
  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/orders', {
          params: {
            page: pagination.currentPage,
            per_page: pagination.perPage,
            search: searchTerm
          }
        });

        const { data, meta } = response.data.data;
        setOrders(data || []);
        setPagination(prev => ({
          ...prev,
          total: meta.total,
          lastPage: meta.last_page,
          currentPage: meta.current_page
        }));
        
        // Calculate stats
        const total = data.length;
        const pending = data.filter(order => order.status === 'pending').length;
        const delivered = data.filter(order => order.status === 'delivered').length;
        const cancelled = data.filter(order => order.status === 'cancelled').length;
        
        setStats({ total, pending, delivered, cancelled });
      } catch (error) {
        console.error('Error fetching orders:', error);
        toast.error('Failed to fetch orders. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    const debounceTimer = setTimeout(() => {
      fetchOrders();
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [pagination.currentPage, pagination.perPage, searchTerm]);

  const handlePageChange = (page) => {
    setPagination(prev => ({
      ...prev,
      currentPage: page
    }));
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setPagination(prev => ({
      ...prev,
      currentPage: 1
    }));
  };

  const handleAddOrder = () => {
    navigate('/orders/new');
  };

  const handleViewOrder = (orderId) => {
    navigate(`/orders/${orderId}`);
  };

  const handleEditOrder = (orderId) => {
    navigate(`/orders/${orderId}/edit`);
  };

  const handleDeleteOrder = async (orderId) => {
    if (window.confirm('Are you sure you want to delete this order?')) {
      try {
        await axios.delete(`/orders/${orderId}`);
        toast.success('Order deleted successfully');
        // Refresh the orders list
        setPagination(prev => ({ ...prev }));
      } catch (error) {
        console.error('Error deleting order:', error);
        toast.error('Failed to delete order. Please try again.');
      }
    }
  };

  const getStatusBadgeClass = (status) => {
    switch (status.toLowerCase()) {
      case 'delivered':
        return 'bg-success';
      case 'pending':
        return 'bg-warning';
      case 'processing':
        return 'bg-info';
      case 'shipped':
        return 'bg-primary';
      case 'cancelled':
        return 'bg-danger';
      default:
        return 'bg-secondary';
    }
  };

  return (
    <div className="page-content">
      <div className="page-header mb-4">
        <h1>Orders</h1>
      </div>

      {/* Stats Cards */}
      <div className="row mb-4">
        <div className="col-md-6 col-xl-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="avatar-md bg-primary bg-opacity-10 rounded">
                  <i className="ri-shopping-cart-2-line fs-24 text-primary"></i>
                </div>
                <div className="ms-3">
                  <h4 className="mb-1">Total Orders</h4>
                  <p className="fs-18 mb-0">{stats.total}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-md-6 col-xl-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="avatar-md bg-warning bg-opacity-10 rounded">
                  <i className="ri-time-line fs-24 text-warning"></i>
                </div>
                <div className="ms-3">
                  <h4 className="mb-1">Pending Orders</h4>
                  <p className="fs-18 mb-0">{stats.pending}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-md-6 col-xl-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="avatar-md bg-success bg-opacity-10 rounded">
                  <i className="ri-check-line fs-24 text-success"></i>
                </div>
                <div className="ms-3">
                  <h4 className="mb-1">Delivered Orders</h4>
                  <p className="fs-18 mb-0">{stats.delivered}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-md-6 col-xl-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="avatar-md bg-danger bg-opacity-10 rounded">
                  <i className="ri-close-line fs-24 text-danger"></i>
                </div>
                <div className="ms-3">
                  <h4 className="mb-1">Cancelled Orders</h4>
                  <p className="fs-18 mb-0">{stats.cancelled}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Orders Table */}
      <div className="card">
        <div className="card-header d-flex justify-content-between align-items-center">
          <h5 className="card-title mb-0">All Orders</h5>
          <div className="d-flex gap-2">
            <input
              type="text"
              className="form-control"
              placeholder="Search orders..."
              value={searchTerm}
              onChange={handleSearch}
              style={{ width: "200px" }}
            />
            <button className="btn btn-primary" onClick={handleAddOrder}>
              <i className="ri-add-line me-1"></i>
              Add Order
            </button>
          </div>
        </div>
        <div className="card-body">
          <div className="table-responsive">
            <table className="table table-hover">
              <thead className="bg-light">
                <tr>
                  <th>Order ID</th>
                  <th>Date</th>
                  <th>Customer</th>
                  <th>Total Amount</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="6" className="text-center py-4">
                      <div className="spinner-border text-primary" role="status">
                        <span className="visually-hidden">Loading...</span>
                      </div>
                    </td>
                  </tr>
                ) : orders.length === 0 ? (
                  <tr>
                    <td colSpan="6" className="text-center py-4">
                      No orders found
                    </td>
                  </tr>
                ) : (
                  orders.map((order) => (
                    <tr key={order.id}>
                      <td>#{order.id}</td>
                      <td>{new Date(order.created_at).toLocaleDateString()}</td>
                      <td>{order.customer_name}</td>
                      <td>${parseFloat(order.total_amount).toFixed(2)}</td>
                      <td>
                        <span className={`badge ${getStatusBadgeClass(order.status)}`}>
                          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        </span>
                      </td>
                      <td>
                        <button
                          className="btn btn-sm btn-light me-2"
                          onClick={() => handleViewOrder(order.id)}
                          title="View Details"
                        >
                          <i className="ri-eye-line"></i>
                        </button>
                        <button
                          className="btn btn-sm btn-light me-2"
                          onClick={() => handleEditOrder(order.id)}
                          title="Edit Order"
                        >
                          <i className="ri-pencil-line"></i>
                        </button>
                        <button
                          className="btn btn-sm btn-light text-danger"
                          onClick={() => handleDeleteOrder(order.id)}
                          title="Delete Order"
                        >
                          <i className="ri-delete-bin-line"></i>
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
        <div className="card-footer">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              Showing <span className="fw-semibold">{orders.length}</span> of{' '}
              <span className="fw-semibold">{pagination.total}</span> orders
            </div>
            <nav>
              <ul className="pagination mb-0">
                <li className={`page-item ${pagination.currentPage === 1 ? 'disabled' : ''}`}>
                  <button
                    className="page-link"
                    onClick={() => handlePageChange(pagination.currentPage - 1)}
                  >
                    Previous
                  </button>
                </li>
                {[...Array(pagination.lastPage)].map((_, i) => (
                  <li
                    key={i + 1}
                    className={`page-item ${pagination.currentPage === i + 1 ? 'active' : ''}`}
                  >
                    <button
                      className="page-link"
                      onClick={() => handlePageChange(i + 1)}
                    >
                      {i + 1}
                    </button>
                  </li>
                ))}
                <li className={`page-item ${pagination.currentPage === pagination.lastPage ? 'disabled' : ''}`}>
                  <button
                    className="page-link"
                    onClick={() => handlePageChange(pagination.currentPage + 1)}
                  >
                    Next
                  </button>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Orders; 