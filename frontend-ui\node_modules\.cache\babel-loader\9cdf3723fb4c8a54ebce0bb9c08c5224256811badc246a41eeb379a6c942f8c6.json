{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport FigureImage from './FigureImage';\nimport FigureCaption from './FigureCaption';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Figure = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'figure',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'figure');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nFigure.displayName = 'Figure';\nexport default Object.assign(Figure, {\n  Image: FigureImage,\n  Caption: FigureCaption\n});", "map": {"version": 3, "names": ["React", "classNames", "FigureImage", "FigureCaption", "useBootstrapPrefix", "jsx", "_jsx", "Figure", "forwardRef", "className", "bsPrefix", "as", "Component", "props", "ref", "displayName", "Object", "assign", "Image", "Caption"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/react-bootstrap/esm/Figure.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport FigureImage from './FigureImage';\nimport FigureCaption from './FigureCaption';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Figure = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'figure',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'figure');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nFigure.displayName = 'Figure';\nexport default Object.assign(Figure, {\n  Image: FigureImage,\n  Caption: FigureCaption\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,MAAM,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAAC;EAC5CC,SAAS;EACTC,QAAQ;EACRC,EAAE,EAAEC,SAAS,GAAG,QAAQ;EACxB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTJ,QAAQ,GAAGN,kBAAkB,CAACM,QAAQ,EAAE,QAAQ,CAAC;EACjD,OAAO,aAAaJ,IAAI,CAACM,SAAS,EAAE;IAClCE,GAAG,EAAEA,GAAG;IACRL,SAAS,EAAER,UAAU,CAACQ,SAAS,EAAEC,QAAQ,CAAC;IAC1C,GAAGG;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,MAAM,CAACQ,WAAW,GAAG,QAAQ;AAC7B,eAAeC,MAAM,CAACC,MAAM,CAACV,MAAM,EAAE;EACnCW,KAAK,EAAEhB,WAAW;EAClBiB,OAAO,EAAEhB;AACX,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}