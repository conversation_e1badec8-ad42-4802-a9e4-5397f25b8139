{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const propTypes = {\n  /**\n   * @default 'img'\n   */\n  bsPrefix: PropTypes.string,\n  /**\n   * Sets image as fluid image.\n   */\n  fluid: PropTypes.bool,\n  /**\n   * Sets image shape as rounded.\n   */\n  rounded: PropTypes.bool,\n  /**\n   * Sets image shape as circle.\n   */\n  roundedCircle: PropTypes.bool,\n  /**\n   * Sets image shape as thumbnail.\n   */\n  thumbnail: PropTypes.bool\n};\nconst Image = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  fluid = false,\n  rounded = false,\n  roundedCircle = false,\n  thumbnail = false,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'img');\n  return /*#__PURE__*/_jsx(\"img\", {\n    ref: ref,\n    ...props,\n    className: classNames(className, fluid && `${bsPrefix}-fluid`, rounded && `rounded`, roundedCircle && `rounded-circle`, thumbnail && `${bsPrefix}-thumbnail`)\n  });\n});\nImage.displayName = 'Image';\nexport default Image;", "map": {"version": 3, "names": ["classNames", "React", "PropTypes", "useBootstrapPrefix", "jsx", "_jsx", "propTypes", "bsPrefix", "string", "fluid", "bool", "rounded", "roundedCircle", "thumbnail", "Image", "forwardRef", "className", "props", "ref", "displayName"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/react-bootstrap/esm/Image.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const propTypes = {\n  /**\n   * @default 'img'\n   */\n  bsPrefix: PropTypes.string,\n  /**\n   * Sets image as fluid image.\n   */\n  fluid: PropTypes.bool,\n  /**\n   * Sets image shape as rounded.\n   */\n  rounded: PropTypes.bool,\n  /**\n   * Sets image shape as circle.\n   */\n  roundedCircle: PropTypes.bool,\n  /**\n   * Sets image shape as thumbnail.\n   */\n  thumbnail: PropTypes.bool\n};\nconst Image = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  fluid = false,\n  rounded = false,\n  roundedCircle = false,\n  thumbnail = false,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'img');\n  return /*#__PURE__*/_jsx(\"img\", {\n    ref: ref,\n    ...props,\n    className: classNames(className, fluid && `${bsPrefix}-fluid`, rounded && `rounded`, roundedCircle && `rounded-circle`, thumbnail && `${bsPrefix}-thumbnail`)\n  });\n});\nImage.displayName = 'Image';\nexport default Image;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMC,SAAS,GAAG;EACvB;AACF;AACA;EACEC,QAAQ,EAAEL,SAAS,CAACM,MAAM;EAC1B;AACF;AACA;EACEC,KAAK,EAAEP,SAAS,CAACQ,IAAI;EACrB;AACF;AACA;EACEC,OAAO,EAAET,SAAS,CAACQ,IAAI;EACvB;AACF;AACA;EACEE,aAAa,EAAEV,SAAS,CAACQ,IAAI;EAC7B;AACF;AACA;EACEG,SAAS,EAAEX,SAAS,CAACQ;AACvB,CAAC;AACD,MAAMI,KAAK,GAAG,aAAab,KAAK,CAACc,UAAU,CAAC,CAAC;EAC3CR,QAAQ;EACRS,SAAS;EACTP,KAAK,GAAG,KAAK;EACbE,OAAO,GAAG,KAAK;EACfC,aAAa,GAAG,KAAK;EACrBC,SAAS,GAAG,KAAK;EACjB,GAAGI;AACL,CAAC,EAAEC,GAAG,KAAK;EACTX,QAAQ,GAAGJ,kBAAkB,CAACI,QAAQ,EAAE,KAAK,CAAC;EAC9C,OAAO,aAAaF,IAAI,CAAC,KAAK,EAAE;IAC9Ba,GAAG,EAAEA,GAAG;IACR,GAAGD,KAAK;IACRD,SAAS,EAAEhB,UAAU,CAACgB,SAAS,EAAEP,KAAK,IAAI,GAAGF,QAAQ,QAAQ,EAAEI,OAAO,IAAI,SAAS,EAAEC,aAAa,IAAI,gBAAgB,EAAEC,SAAS,IAAI,GAAGN,QAAQ,YAAY;EAC9J,CAAC,CAAC;AACJ,CAAC,CAAC;AACFO,KAAK,CAACK,WAAW,GAAG,OAAO;AAC3B,eAAeL,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}