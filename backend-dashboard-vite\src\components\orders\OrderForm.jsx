import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from '../../utils/axios';
import { toast } from 'react-toastify';

function OrderForm({ orderId }) {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [books, setBooks] = useState([]);
  const [users, setUsers] = useState([]);
  const [formData, setFormData] = useState({
    user_id: '',
    items: [{ book_id: '', quantity: 1, price: 0 }],
    shipping_address: '',
    payment_method: 'cash',
    status: 'pending'
  });

  // Fetch books and users for dropdowns
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [booksRes, usersRes] = await Promise.all([
          axios.get('/books'),
          axios.get('/users')
        ]);

        // Extract books data
        let booksData = [];
        if (booksRes.data?.data?.data) {
          booksData = booksRes.data.data.data;
        } else if (Array.isArray(booksRes.data?.data)) {
          booksData = booksRes.data.data;
        }
        setBooks(booksData);

        // Extract users data
        let usersData = [];
        if (usersRes.data?.data?.data) {
          usersData = usersRes.data.data.data;
        } else if (Array.isArray(usersRes.data?.data)) {
          usersData = usersRes.data.data;
        }
        setUsers(usersData);

      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('Failed to load required data');
      }
    };

    fetchData();
  }, []);

  // Add new item row
  const handleAddItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { book_id: '', quantity: 1, price: 0 }]
    }));
  };

  // Remove item row
  const handleRemoveItem = (index) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }));
  };

  // Update item details
  const handleItemChange = (index, field, value) => {
    setFormData(prev => {
      const newItems = [...prev.items];
      newItems[index] = { ...newItems[index], [field]: value };

      // If book_id changes, update the price
      if (field === 'book_id') {
        const book = books.find(b => b.id === value || b.BookID === value);
        if (book) {
          newItems[index].price = parseFloat(book.Price || book.price || 0);
        }
      }

      // If quantity changes, ensure it's a positive number
      if (field === 'quantity') {
        newItems[index].quantity = Math.max(1, value);
      }

      return { ...prev, items: newItems };
    });
  };

  // Calculate total amount
  const calculateTotal = () => {
    return formData.items.reduce((sum, item) => {
      const quantity = parseInt(item.quantity) || 0;
      const price = parseFloat(item.price) || 0;
      return sum + (quantity * price);
    }, 0);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setLoading(true);

      // Validate form
      if (!formData.user_id) {
        toast.error('Please select a customer');
        return;
      }

      if (!formData.shipping_address.trim()) {
        toast.error('Please enter a shipping address');
        return;
      }

      if (formData.items.length === 0) {
        toast.error('Please add at least one item');
        return;
      }

      if (!formData.items.every(item => item.book_id && item.quantity > 0)) {
        toast.error('Please fill in all item details');
        return;
      }

      // Prepare data for submission
      const orderData = {
        user_id: formData.user_id,
        shipping_address: formData.shipping_address.trim(),
        payment_method: formData.payment_method,
        status: formData.status,
        total_amount: calculateTotal().toFixed(2),
        order_items: formData.items.map(item => ({
          book_id: item.book_id,
          quantity: parseInt(item.quantity),
          unit_price: parseFloat(item.price).toFixed(2)
        }))
      };

      console.log('Submitting order data:', orderData);

      // Submit to API
      const response = await axios.post('/orders', orderData);

      if (response.data.success) {
        toast.success('Order created successfully');
        navigate('/orders');
      } else {
        toast.error(response.data.message || 'Failed to create order');
      }
    } catch (error) {
      console.error('Error creating order:', error);
      const errorMessage = error.response?.data?.message || error.response?.data?.error || 'Failed to create order. Please try again.';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container-fluid">
      <div className="row">
        <div className="col-12">
          <div className="card">
            <div className="card-header">
              <h5 className="card-title mb-0">{orderId ? 'Edit Order' : 'Create New Order'}</h5>
            </div>
            <div className="card-body">
              <form onSubmit={handleSubmit}>
                {/* Customer Selection */}
                <div className="mb-3">
                  <label className="form-label">Customer</label>
                  <select
                    className="form-select"
                    value={formData.user_id}
                    onChange={(e) => setFormData(prev => ({ ...prev, user_id: e.target.value }))}
                    required
                  >
                    <option value="">Select Customer</option>
                    {users.map(user => (
                      <option key={user.id || user.UserID} value={user.id || user.UserID}>
                        {user.FirstName || user.first_name} {user.LastName || user.last_name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Order Items */}
                <div className="mb-3">
                  <label className="form-label">Order Items</label>
                  {formData.items.map((item, index) => (
                    <div key={index} className="row mb-2">
                      <div className="col-md-5">
                        <select
                          className="form-select"
                          value={item.book_id}
                          onChange={(e) => handleItemChange(index, 'book_id', e.target.value)}
                          required
                        >
                          <option value="">Select Book</option>
                          {books.map(book => (
                            <option key={book.id || book.BookID} value={book.id || book.BookID}>
                              {book.Title || book.title} (${book.Price || book.price})
                            </option>
                          ))}
                        </select>
                      </div>
                      <div className="col-md-3">
                        <input
                          type="number"
                          className="form-control"
                          placeholder="Quantity"
                          value={item.quantity}
                          onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value) || 0)}
                          min="1"
                          required
                        />
                      </div>
                      <div className="col-md-3">
                        <input
                          type="text"
                          className="form-control"
                          placeholder="Price"
                          value={`$${item.price.toFixed(2)}`}
                          readOnly
                        />
                      </div>
                      <div className="col-md-1">
                        {index > 0 && (
                          <button
                            type="button"
                            className="btn btn-danger btn-sm"
                            onClick={() => handleRemoveItem(index)}
                          >
                            <i className="ri-delete-bin-line"></i>
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                  <button
                    type="button"
                    className="btn btn-secondary btn-sm"
                    onClick={handleAddItem}
                  >
                    <i className="ri-add-line me-1"></i>
                    Add Item
                  </button>
                </div>

                {/* Shipping Address */}
                <div className="mb-3">
                  <label className="form-label">Shipping Address</label>
                  <textarea
                    className="form-control"
                    value={formData.shipping_address}
                    onChange={(e) => setFormData(prev => ({ ...prev, shipping_address: e.target.value }))}
                    rows="3"
                    required
                  ></textarea>
                </div>

                {/* Payment Method */}
                <div className="mb-3">
                  <label className="form-label">Payment Method</label>
                  <select
                    className="form-select"
                    value={formData.payment_method}
                    onChange={(e) => setFormData(prev => ({ ...prev, payment_method: e.target.value }))}
                    required
                  >
                    <option value="cash">Cash</option>
                    <option value="card">Card</option>
                    <option value="bank_transfer">Bank Transfer</option>
                  </select>
                </div>

                {/* Order Status */}
                <div className="mb-3">
                  <label className="form-label">Status</label>
                  <select
                    className="form-select"
                    value={formData.status}
                    onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
                    required
                  >
                    <option value="pending">Pending</option>
                    <option value="processing">Processing</option>
                    <option value="shipped">Shipped</option>
                    <option value="delivered">Delivered</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>

                {/* Total Amount */}
                <div className="mb-4">
                  <h5>Total Amount: ${calculateTotal().toFixed(2)}</h5>
                </div>

                {/* Submit Button */}
                <div className="text-end">
                  <button
                    type="button"
                    className="btn btn-secondary me-2"
                    onClick={() => navigate('/orders')}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                        Saving...
                      </>
                    ) : (
                      'Save Order'
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default OrderForm; 