{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useCallback } from 'react';\nimport { getCartItems, getCartItemCount, addToCart as addToCartUtil, removeFromCart as removeFromCartUtil, updateCartItemQuantity as updateCartItemQuantityUtil, clearCart as clearCartUtil, getCartTotal } from '../utilis/cartUtils';\n\n/**\n * Custom hook for cart management\n * Provides cart state and operations with real-time updates\n */\nexport const useCart = () => {\n  _s();\n  const [cartItems, setCartItems] = useState([]);\n  const [cartCount, setCartCount] = useState(0);\n  const [cartTotal, setCartTotal] = useState(0);\n  const [isLoading, setIsLoading] = useState(false);\n  const [lastUpdate, setLastUpdate] = useState(0);\n\n  // Load cart data from localStorage with debouncing\n  const loadCartData = useCallback((skipEvent = false) => {\n    const now = Date.now();\n\n    // Debounce rapid calls (prevent calls within 100ms)\n    if (now - lastUpdate < 100) {\n      return;\n    }\n    console.log('Loading cart data...', {\n      skipEvent\n    });\n    const items = getCartItems();\n    const count = getCartItemCount();\n    const total = getCartTotal();\n    setCartItems(items);\n    setCartCount(count);\n    setCartTotal(total);\n    setLastUpdate(now);\n\n    // Only dispatch event if not triggered by an event listener (to prevent infinite loops)\n    if (!skipEvent) {\n      console.log('Dispatching cartUpdated event');\n      window.dispatchEvent(new CustomEvent('cartUpdated', {\n        detail: {\n          items,\n          count,\n          total\n        }\n      }));\n    }\n  }, [lastUpdate]);\n\n  // Initialize cart data on mount and listen for storage changes\n  useEffect(() => {\n    loadCartData();\n\n    // Listen for localStorage changes (from other tabs/windows)\n    const handleStorageChange = e => {\n      if (e.key === 'cart') {\n        loadCartData(true); // Skip event dispatch to prevent loops\n      }\n    };\n\n    // Listen for custom cart events (from same window)\n    const handleCartEvent = () => {\n      loadCartData(true); // Skip event dispatch to prevent loops\n    };\n    window.addEventListener('storage', handleStorageChange);\n    window.addEventListener('cartCleared', handleCartEvent);\n    // Remove cartUpdated listener to prevent infinite loops\n    // window.addEventListener('cartUpdated', handleCartEvent);\n\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      window.removeEventListener('cartCleared', handleCartEvent);\n      // window.removeEventListener('cartUpdated', handleCartEvent);\n    };\n  }, [loadCartData]);\n\n  // Listen for storage changes (cart updates from other tabs/components)\n  useEffect(() => {\n    const handleStorageChange = e => {\n      if (e.key === 'cart') {\n        loadCartData();\n      }\n    };\n    window.addEventListener('storage', handleStorageChange);\n    return () => window.removeEventListener('storage', handleStorageChange);\n  }, [loadCartData]);\n\n  // Add item to cart\n  const addToCart = useCallback(async (product, quantity = 1) => {\n    // Prevent multiple simultaneous operations\n    if (isLoading) {\n      console.log('Cart operation already in progress, skipping...');\n      return {\n        success: false,\n        message: 'Please wait, processing previous request...'\n      };\n    }\n    console.log('Adding to cart:', product.Title || product.title);\n    setIsLoading(true);\n    try {\n      const success = addToCartUtil(product, quantity);\n      if (success) {\n        console.log('Successfully added to cart, refreshing data...');\n\n        // Use setTimeout to prevent blocking the UI\n        setTimeout(() => {\n          loadCartData(false); // Allow event dispatch for this operation\n        }, 50);\n\n        // Dispatch toast event\n        setTimeout(() => {\n          window.dispatchEvent(new CustomEvent('showToast', {\n            detail: {\n              message: `${product.Title || product.title || 'Item'} added to cart!`,\n              type: 'success'\n            }\n          }));\n        }, 150);\n        return {\n          success: true,\n          message: 'Item added to cart successfully!'\n        };\n      } else {\n        return {\n          success: false,\n          message: 'Failed to add item to cart'\n        };\n      }\n    } catch (error) {\n      console.error('Error in addToCart:', error);\n      return {\n        success: false,\n        message: 'An error occurred while adding to cart'\n      };\n    } finally {\n      // Use setTimeout to ensure UI doesn't freeze\n      setTimeout(() => {\n        setIsLoading(false);\n      }, 100);\n    }\n  }, [loadCartData, isLoading]);\n\n  // Remove item from cart\n  const removeFromCart = useCallback(async itemId => {\n    setIsLoading(true);\n    try {\n      // Get item name before removing\n      const items = getCartItems();\n      const itemToRemove = items.find(item => item.id === itemId);\n      const itemName = (itemToRemove === null || itemToRemove === void 0 ? void 0 : itemToRemove.name) || 'Item';\n      const success = removeFromCartUtil(itemId);\n      if (success) {\n        loadCartData(false); // Allow event dispatch for this operation\n\n        // Dispatch toast event with delay\n        setTimeout(() => {\n          window.dispatchEvent(new CustomEvent('showToast', {\n            detail: {\n              message: `${itemName} removed from cart`,\n              type: 'success'\n            }\n          }));\n        }, 100);\n        return {\n          success: true,\n          message: 'Item removed from cart'\n        };\n      } else {\n        return {\n          success: false,\n          message: 'Failed to remove item from cart'\n        };\n      }\n    } catch (error) {\n      console.error('Error in removeFromCart:', error);\n      return {\n        success: false,\n        message: 'An error occurred while removing from cart'\n      };\n    } finally {\n      setIsLoading(false);\n    }\n  }, [loadCartData]);\n\n  // Update item quantity\n  const updateItemQuantity = useCallback(async (itemId, newQuantity) => {\n    setIsLoading(true);\n    try {\n      const success = updateCartItemQuantityUtil(itemId, newQuantity);\n      if (success) {\n        loadCartData(false); // Allow event dispatch for this operation\n        return {\n          success: true,\n          message: 'Quantity updated successfully'\n        };\n      } else {\n        return {\n          success: false,\n          message: 'Failed to update quantity'\n        };\n      }\n    } catch (error) {\n      console.error('Error in updateItemQuantity:', error);\n      return {\n        success: false,\n        message: 'An error occurred while updating quantity'\n      };\n    } finally {\n      setIsLoading(false);\n    }\n  }, [loadCartData]);\n\n  // Clear entire cart\n  const clearCart = useCallback(async () => {\n    setIsLoading(true);\n    try {\n      const success = clearCartUtil();\n      if (success) {\n        loadCartData(); // Refresh cart data\n        return {\n          success: true,\n          message: 'Cart cleared successfully'\n        };\n      } else {\n        return {\n          success: false,\n          message: 'Failed to clear cart'\n        };\n      }\n    } catch (error) {\n      console.error('Error in clearCart:', error);\n      return {\n        success: false,\n        message: 'An error occurred while clearing cart'\n      };\n    } finally {\n      setIsLoading(false);\n    }\n  }, [loadCartData]);\n\n  // Check if item is in cart\n  const isInCart = useCallback(productId => {\n    return cartItems.some(item => item.id === productId);\n  }, [cartItems]);\n\n  // Get item quantity in cart\n  const getItemQuantity = useCallback(productId => {\n    const item = cartItems.find(item => item.id === productId);\n    return item ? item.quantity : 0;\n  }, [cartItems]);\n  return {\n    // State\n    cartItems,\n    cartCount,\n    cartTotal,\n    isLoading,\n    // Actions\n    addToCart,\n    removeFromCart,\n    updateItemQuantity,\n    clearCart,\n    // Utilities\n    isInCart,\n    getItemQuantity,\n    refreshCart: loadCartData\n  };\n};\n_s(useCart, \"xxJYQH0D8FJTHdAPYAmpMVB5ZtU=\");", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "getCartItems", "getCartItemCount", "addToCart", "addToCartUtil", "removeFromCart", "removeFromCartUtil", "updateCartItemQuantity", "updateCartItemQuantityUtil", "clearCart", "clearCartUtil", "getCartTotal", "useCart", "_s", "cartItems", "setCartItems", "cartCount", "setCartCount", "cartTotal", "setCartTotal", "isLoading", "setIsLoading", "lastUpdate", "setLastUpdate", "loadCartData", "skip<PERSON><PERSON>", "now", "Date", "console", "log", "items", "count", "total", "window", "dispatchEvent", "CustomEvent", "detail", "handleStorageChange", "e", "key", "handleCartEvent", "addEventListener", "removeEventListener", "product", "quantity", "success", "message", "Title", "title", "setTimeout", "type", "error", "itemId", "itemToRemove", "find", "item", "id", "itemName", "name", "updateItemQuantity", "newQuantity", "isInCart", "productId", "some", "getItemQuantity", "refreshCart"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/hooks/useCart.js"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { \n  getCartItems, \n  getCartItemCount, \n  addToCart as addToCartUtil, \n  removeFromCart as removeFromCartUtil,\n  updateCartItemQuantity as updateCartItemQuantityUtil,\n  clearCart as clearCartUtil,\n  getCartTotal\n} from '../utilis/cartUtils';\n\n/**\n * Custom hook for cart management\n * Provides cart state and operations with real-time updates\n */\nexport const useCart = () => {\n  const [cartItems, setCartItems] = useState([]);\n  const [cartCount, setCartCount] = useState(0);\n  const [cartTotal, setCartTotal] = useState(0);\n  const [isLoading, setIsLoading] = useState(false);\n  const [lastUpdate, setLastUpdate] = useState(0);\n\n  // Load cart data from localStorage with debouncing\n  const loadCartData = useCallback((skipEvent = false) => {\n    const now = Date.now();\n\n    // Debounce rapid calls (prevent calls within 100ms)\n    if (now - lastUpdate < 100) {\n      return;\n    }\n\n    console.log('Loading cart data...', { skipEvent });\n\n    const items = getCartItems();\n    const count = getCartItemCount();\n    const total = getCartTotal();\n\n    setCartItems(items);\n    setCartCount(count);\n    setCartTotal(total);\n    setLastUpdate(now);\n\n    // Only dispatch event if not triggered by an event listener (to prevent infinite loops)\n    if (!skipEvent) {\n      console.log('Dispatching cartUpdated event');\n      window.dispatchEvent(new CustomEvent('cartUpdated', {\n        detail: { items, count, total }\n      }));\n    }\n  }, [lastUpdate]);\n\n  // Initialize cart data on mount and listen for storage changes\n  useEffect(() => {\n    loadCartData();\n\n    // Listen for localStorage changes (from other tabs/windows)\n    const handleStorageChange = (e) => {\n      if (e.key === 'cart') {\n        loadCartData(true); // Skip event dispatch to prevent loops\n      }\n    };\n\n    // Listen for custom cart events (from same window)\n    const handleCartEvent = () => {\n      loadCartData(true); // Skip event dispatch to prevent loops\n    };\n\n    window.addEventListener('storage', handleStorageChange);\n    window.addEventListener('cartCleared', handleCartEvent);\n    // Remove cartUpdated listener to prevent infinite loops\n    // window.addEventListener('cartUpdated', handleCartEvent);\n\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      window.removeEventListener('cartCleared', handleCartEvent);\n      // window.removeEventListener('cartUpdated', handleCartEvent);\n    };\n  }, [loadCartData]);\n\n  // Listen for storage changes (cart updates from other tabs/components)\n  useEffect(() => {\n    const handleStorageChange = (e) => {\n      if (e.key === 'cart') {\n        loadCartData();\n      }\n    };\n\n    window.addEventListener('storage', handleStorageChange);\n    return () => window.removeEventListener('storage', handleStorageChange);\n  }, [loadCartData]);\n\n  // Add item to cart\n  const addToCart = useCallback(async (product, quantity = 1) => {\n    // Prevent multiple simultaneous operations\n    if (isLoading) {\n      console.log('Cart operation already in progress, skipping...');\n      return { success: false, message: 'Please wait, processing previous request...' };\n    }\n\n    console.log('Adding to cart:', product.Title || product.title);\n    setIsLoading(true);\n\n    try {\n      const success = addToCartUtil(product, quantity);\n      if (success) {\n        console.log('Successfully added to cart, refreshing data...');\n\n        // Use setTimeout to prevent blocking the UI\n        setTimeout(() => {\n          loadCartData(false); // Allow event dispatch for this operation\n        }, 50);\n\n        // Dispatch toast event\n        setTimeout(() => {\n          window.dispatchEvent(new CustomEvent('showToast', {\n            detail: {\n              message: `${product.Title || product.title || 'Item'} added to cart!`,\n              type: 'success'\n            }\n          }));\n        }, 150);\n\n        return { success: true, message: 'Item added to cart successfully!' };\n      } else {\n        return { success: false, message: 'Failed to add item to cart' };\n      }\n    } catch (error) {\n      console.error('Error in addToCart:', error);\n      return { success: false, message: 'An error occurred while adding to cart' };\n    } finally {\n      // Use setTimeout to ensure UI doesn't freeze\n      setTimeout(() => {\n        setIsLoading(false);\n      }, 100);\n    }\n  }, [loadCartData, isLoading]);\n\n  // Remove item from cart\n  const removeFromCart = useCallback(async (itemId) => {\n    setIsLoading(true);\n    try {\n      // Get item name before removing\n      const items = getCartItems();\n      const itemToRemove = items.find(item => item.id === itemId);\n      const itemName = itemToRemove?.name || 'Item';\n\n      const success = removeFromCartUtil(itemId);\n      if (success) {\n        loadCartData(false); // Allow event dispatch for this operation\n\n        // Dispatch toast event with delay\n        setTimeout(() => {\n          window.dispatchEvent(new CustomEvent('showToast', {\n            detail: {\n              message: `${itemName} removed from cart`,\n              type: 'success'\n            }\n          }));\n        }, 100);\n\n        return { success: true, message: 'Item removed from cart' };\n      } else {\n        return { success: false, message: 'Failed to remove item from cart' };\n      }\n    } catch (error) {\n      console.error('Error in removeFromCart:', error);\n      return { success: false, message: 'An error occurred while removing from cart' };\n    } finally {\n      setIsLoading(false);\n    }\n  }, [loadCartData]);\n\n  // Update item quantity\n  const updateItemQuantity = useCallback(async (itemId, newQuantity) => {\n    setIsLoading(true);\n    try {\n      const success = updateCartItemQuantityUtil(itemId, newQuantity);\n      if (success) {\n        loadCartData(false); // Allow event dispatch for this operation\n        return { success: true, message: 'Quantity updated successfully' };\n      } else {\n        return { success: false, message: 'Failed to update quantity' };\n      }\n    } catch (error) {\n      console.error('Error in updateItemQuantity:', error);\n      return { success: false, message: 'An error occurred while updating quantity' };\n    } finally {\n      setIsLoading(false);\n    }\n  }, [loadCartData]);\n\n  // Clear entire cart\n  const clearCart = useCallback(async () => {\n    setIsLoading(true);\n    try {\n      const success = clearCartUtil();\n      if (success) {\n        loadCartData(); // Refresh cart data\n        return { success: true, message: 'Cart cleared successfully' };\n      } else {\n        return { success: false, message: 'Failed to clear cart' };\n      }\n    } catch (error) {\n      console.error('Error in clearCart:', error);\n      return { success: false, message: 'An error occurred while clearing cart' };\n    } finally {\n      setIsLoading(false);\n    }\n  }, [loadCartData]);\n\n  // Check if item is in cart\n  const isInCart = useCallback((productId) => {\n    return cartItems.some(item => item.id === productId);\n  }, [cartItems]);\n\n  // Get item quantity in cart\n  const getItemQuantity = useCallback((productId) => {\n    const item = cartItems.find(item => item.id === productId);\n    return item ? item.quantity : 0;\n  }, [cartItems]);\n\n  return {\n    // State\n    cartItems,\n    cartCount,\n    cartTotal,\n    isLoading,\n    \n    // Actions\n    addToCart,\n    removeFromCart,\n    updateItemQuantity,\n    clearCart,\n    \n    // Utilities\n    isInCart,\n    getItemQuantity,\n    refreshCart: loadCartData,\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SACEC,YAAY,EACZC,gBAAgB,EAChBC,SAAS,IAAIC,aAAa,EAC1BC,cAAc,IAAIC,kBAAkB,EACpCC,sBAAsB,IAAIC,0BAA0B,EACpDC,SAAS,IAAIC,aAAa,EAC1BC,YAAY,QACP,qBAAqB;;AAE5B;AACA;AACA;AACA;AACA,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAM0B,YAAY,GAAGxB,WAAW,CAAC,CAACyB,SAAS,GAAG,KAAK,KAAK;IACtD,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;IAEtB;IACA,IAAIA,GAAG,GAAGJ,UAAU,GAAG,GAAG,EAAE;MAC1B;IACF;IAEAM,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;MAAEJ;IAAU,CAAC,CAAC;IAElD,MAAMK,KAAK,GAAG7B,YAAY,CAAC,CAAC;IAC5B,MAAM8B,KAAK,GAAG7B,gBAAgB,CAAC,CAAC;IAChC,MAAM8B,KAAK,GAAGrB,YAAY,CAAC,CAAC;IAE5BI,YAAY,CAACe,KAAK,CAAC;IACnBb,YAAY,CAACc,KAAK,CAAC;IACnBZ,YAAY,CAACa,KAAK,CAAC;IACnBT,aAAa,CAACG,GAAG,CAAC;;IAElB;IACA,IAAI,CAACD,SAAS,EAAE;MACdG,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5CI,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,aAAa,EAAE;QAClDC,MAAM,EAAE;UAAEN,KAAK;UAAEC,KAAK;UAAEC;QAAM;MAChC,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACV,UAAU,CAAC,CAAC;;EAEhB;EACAvB,SAAS,CAAC,MAAM;IACdyB,YAAY,CAAC,CAAC;;IAEd;IACA,MAAMa,mBAAmB,GAAIC,CAAC,IAAK;MACjC,IAAIA,CAAC,CAACC,GAAG,KAAK,MAAM,EAAE;QACpBf,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;MACtB;IACF,CAAC;;IAED;IACA,MAAMgB,eAAe,GAAGA,CAAA,KAAM;MAC5BhB,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;IACtB,CAAC;IAEDS,MAAM,CAACQ,gBAAgB,CAAC,SAAS,EAAEJ,mBAAmB,CAAC;IACvDJ,MAAM,CAACQ,gBAAgB,CAAC,aAAa,EAAED,eAAe,CAAC;IACvD;IACA;;IAEA,OAAO,MAAM;MACXP,MAAM,CAACS,mBAAmB,CAAC,SAAS,EAAEL,mBAAmB,CAAC;MAC1DJ,MAAM,CAACS,mBAAmB,CAAC,aAAa,EAAEF,eAAe,CAAC;MAC1D;IACF,CAAC;EACH,CAAC,EAAE,CAAChB,YAAY,CAAC,CAAC;;EAElB;EACAzB,SAAS,CAAC,MAAM;IACd,MAAMsC,mBAAmB,GAAIC,CAAC,IAAK;MACjC,IAAIA,CAAC,CAACC,GAAG,KAAK,MAAM,EAAE;QACpBf,YAAY,CAAC,CAAC;MAChB;IACF,CAAC;IAEDS,MAAM,CAACQ,gBAAgB,CAAC,SAAS,EAAEJ,mBAAmB,CAAC;IACvD,OAAO,MAAMJ,MAAM,CAACS,mBAAmB,CAAC,SAAS,EAAEL,mBAAmB,CAAC;EACzE,CAAC,EAAE,CAACb,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMrB,SAAS,GAAGH,WAAW,CAAC,OAAO2C,OAAO,EAAEC,QAAQ,GAAG,CAAC,KAAK;IAC7D;IACA,IAAIxB,SAAS,EAAE;MACbQ,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC9D,OAAO;QAAEgB,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE;MAA8C,CAAC;IACnF;IAEAlB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEc,OAAO,CAACI,KAAK,IAAIJ,OAAO,CAACK,KAAK,CAAC;IAC9D3B,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAMwB,OAAO,GAAGzC,aAAa,CAACuC,OAAO,EAAEC,QAAQ,CAAC;MAChD,IAAIC,OAAO,EAAE;QACXjB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;;QAE7D;QACAoB,UAAU,CAAC,MAAM;UACfzB,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;QACvB,CAAC,EAAE,EAAE,CAAC;;QAEN;QACAyB,UAAU,CAAC,MAAM;UACfhB,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,WAAW,EAAE;YAChDC,MAAM,EAAE;cACNU,OAAO,EAAE,GAAGH,OAAO,CAACI,KAAK,IAAIJ,OAAO,CAACK,KAAK,IAAI,MAAM,iBAAiB;cACrEE,IAAI,EAAE;YACR;UACF,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,GAAG,CAAC;QAEP,OAAO;UAAEL,OAAO,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAmC,CAAC;MACvE,CAAC,MAAM;QACL,OAAO;UAAED,OAAO,EAAE,KAAK;UAAEC,OAAO,EAAE;QAA6B,CAAC;MAClE;IACF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdvB,OAAO,CAACuB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,OAAO;QAAEN,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAyC,CAAC;IAC9E,CAAC,SAAS;MACR;MACAG,UAAU,CAAC,MAAM;QACf5B,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC,EAAE,CAACG,YAAY,EAAEJ,SAAS,CAAC,CAAC;;EAE7B;EACA,MAAMf,cAAc,GAAGL,WAAW,CAAC,MAAOoD,MAAM,IAAK;IACnD/B,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF;MACA,MAAMS,KAAK,GAAG7B,YAAY,CAAC,CAAC;MAC5B,MAAMoD,YAAY,GAAGvB,KAAK,CAACwB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKJ,MAAM,CAAC;MAC3D,MAAMK,QAAQ,GAAG,CAAAJ,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEK,IAAI,KAAI,MAAM;MAE7C,MAAMb,OAAO,GAAGvC,kBAAkB,CAAC8C,MAAM,CAAC;MAC1C,IAAIP,OAAO,EAAE;QACXrB,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;;QAErB;QACAyB,UAAU,CAAC,MAAM;UACfhB,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,WAAW,EAAE;YAChDC,MAAM,EAAE;cACNU,OAAO,EAAE,GAAGW,QAAQ,oBAAoB;cACxCP,IAAI,EAAE;YACR;UACF,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,GAAG,CAAC;QAEP,OAAO;UAAEL,OAAO,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAyB,CAAC;MAC7D,CAAC,MAAM;QACL,OAAO;UAAED,OAAO,EAAE,KAAK;UAAEC,OAAO,EAAE;QAAkC,CAAC;MACvE;IACF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdvB,OAAO,CAACuB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO;QAAEN,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE;MAA6C,CAAC;IAClF,CAAC,SAAS;MACRzB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACG,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMmC,kBAAkB,GAAG3D,WAAW,CAAC,OAAOoD,MAAM,EAAEQ,WAAW,KAAK;IACpEvC,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMwB,OAAO,GAAGrC,0BAA0B,CAAC4C,MAAM,EAAEQ,WAAW,CAAC;MAC/D,IAAIf,OAAO,EAAE;QACXrB,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;QACrB,OAAO;UAAEqB,OAAO,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAgC,CAAC;MACpE,CAAC,MAAM;QACL,OAAO;UAAED,OAAO,EAAE,KAAK;UAAEC,OAAO,EAAE;QAA4B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdvB,OAAO,CAACuB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO;QAAEN,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE;MAA4C,CAAC;IACjF,CAAC,SAAS;MACRzB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACG,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMf,SAAS,GAAGT,WAAW,CAAC,YAAY;IACxCqB,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMwB,OAAO,GAAGnC,aAAa,CAAC,CAAC;MAC/B,IAAImC,OAAO,EAAE;QACXrB,YAAY,CAAC,CAAC,CAAC,CAAC;QAChB,OAAO;UAAEqB,OAAO,EAAE,IAAI;UAAEC,OAAO,EAAE;QAA4B,CAAC;MAChE,CAAC,MAAM;QACL,OAAO;UAAED,OAAO,EAAE,KAAK;UAAEC,OAAO,EAAE;QAAuB,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdvB,OAAO,CAACuB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,OAAO;QAAEN,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAwC,CAAC;IAC7E,CAAC,SAAS;MACRzB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACG,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMqC,QAAQ,GAAG7D,WAAW,CAAE8D,SAAS,IAAK;IAC1C,OAAOhD,SAAS,CAACiD,IAAI,CAACR,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKM,SAAS,CAAC;EACtD,CAAC,EAAE,CAAChD,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMkD,eAAe,GAAGhE,WAAW,CAAE8D,SAAS,IAAK;IACjD,MAAMP,IAAI,GAAGzC,SAAS,CAACwC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKM,SAAS,CAAC;IAC1D,OAAOP,IAAI,GAAGA,IAAI,CAACX,QAAQ,GAAG,CAAC;EACjC,CAAC,EAAE,CAAC9B,SAAS,CAAC,CAAC;EAEf,OAAO;IACL;IACAA,SAAS;IACTE,SAAS;IACTE,SAAS;IACTE,SAAS;IAET;IACAjB,SAAS;IACTE,cAAc;IACdsD,kBAAkB;IAClBlD,SAAS;IAET;IACAoD,QAAQ;IACRG,eAAe;IACfC,WAAW,EAAEzC;EACf,CAAC;AACH,CAAC;AAACX,EAAA,CAhOWD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}