onpage-dialog.preload.js:121  Uncaught ReferenceError: browser is not defined
    at start (onpage-dialog.preload.js:121:5)
    at onpage-dialog.preload.js:135:1
    at onpage-dialog.preload.js:393:12
react-dom-client.development.js:24867 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
:3001/api/public/pages/blog-page:1 
            
            
            Failed to load resource: the server responded with a status of 404 (Not Found)
DynamicPage.jsx:40  Error fetching blog-page page: AxiosErrorcode: "ERR_BAD_REQUEST"config: {transitional: {…}, adapter: Array(3), transformRequest: Array(1), transformResponse: Array(1), timeout: 0, …}message: "Request failed with status code 404"name: "AxiosError"request: XMLHttpRequest {onreadystatechange: null, readyState: 4, timeout: 0, withCredentials: false, upload: XMLHttpRequestUpload, …}response: {data: '<!DOCTYPE html>\n<html lang="en">\n<head>\n<meta char…api/public/pages/blog-page</pre>\n</body>\n</html>\n', status: 404, statusText: 'Not Found', headers: AxiosHeaders, config: {…}, …}status: 404stack: "AxiosError: Request failed with status code 404\n    at settle (http://localhost:3001/static/js/bundle.js:22474:12)\n    at XMLHttpRequest.onloadend (http://localhost:3001/static/js/bundle.js:21111:66)\n    at Axios.request (http://localhost:3001/static/js/bundle.js:21610:41)\n    at async fetchPageContent (http://localhost:3001/static/js/bundle.js:84045:26)"[[Prototype]]: Error
fetchPageContent @ DynamicPage.jsx:40
[NEW] Explain Console errors by using Copilot in Edge: click
         
         to explain an error. 
        Learn more
        Don't show again
