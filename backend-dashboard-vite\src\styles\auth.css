.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  padding: 1rem;
}

.auth-form-box {
  width: 100%;
  max-width: 400px;
  padding: 2rem;
}

.form-label {
  color: #6c757d;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-control {
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 0.75rem 1rem;
  width: 100%;
  margin-bottom: 1rem;
}

.form-control:focus {
  border-color: #4154f1;
  box-shadow: none;
  outline: none;
}

.form-check {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.form-check-input {
  margin: 0;
}

.form-check-label {
  color: #6c757d;
  font-size: 0.9rem;
}

.btn-primary {
  background-color: #ff6b6b;
  border: none;
  color: white;
  padding: 0.75rem;
  border-radius: 4px;
  width: 100%;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #ff5252;
}

.signup-link {
  text-align: center;
  margin-top: 1.5rem;
  color: #6c757d;
}

.signup-link a {
  color: #ff6b6b;
  text-decoration: none;
  font-weight: 500;
}

.signup-link a:hover {
  text-decoration: underline;
}

@media (max-width: 576px) {
  .auth-form-box {
    padding: 1.5rem;
  }
}