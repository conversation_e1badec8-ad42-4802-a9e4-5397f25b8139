{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "useBootstrapBreakpoints", "useBootstrapMinBreakpoint", "jsx", "_jsx", "Row", "forwardRef", "bsPrefix", "className", "as", "Component", "props", "ref", "decoratedBsPrefix", "breakpoints", "minBreakpoint", "sizePrefix", "classes", "for<PERSON>ach", "brkPoint", "propValue", "cols", "infix", "push", "displayName"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/react-bootstrap/esm/Row.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,EAAEC,uBAAuB,EAAEC,yBAAyB,QAAQ,iBAAiB;AACxG,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,GAAG,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAC;EACzCC,QAAQ;EACRC,SAAS;EACT;EACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;EACrB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,iBAAiB,GAAGb,kBAAkB,CAACO,QAAQ,EAAE,KAAK,CAAC;EAC7D,MAAMO,WAAW,GAAGb,uBAAuB,CAAC,CAAC;EAC7C,MAAMc,aAAa,GAAGb,yBAAyB,CAAC,CAAC;EACjD,MAAMc,UAAU,GAAG,GAAGH,iBAAiB,OAAO;EAC9C,MAAMI,OAAO,GAAG,EAAE;EAClBH,WAAW,CAACI,OAAO,CAACC,QAAQ,IAAI;IAC9B,MAAMC,SAAS,GAAGT,KAAK,CAACQ,QAAQ,CAAC;IACjC,OAAOR,KAAK,CAACQ,QAAQ,CAAC;IACtB,IAAIE,IAAI;IACR,IAAID,SAAS,IAAI,IAAI,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;MACtD,CAAC;QACCC;MACF,CAAC,GAAGD,SAAS;IACf,CAAC,MAAM;MACLC,IAAI,GAAGD,SAAS;IAClB;IACA,MAAME,KAAK,GAAGH,QAAQ,KAAKJ,aAAa,GAAG,IAAII,QAAQ,EAAE,GAAG,EAAE;IAC9D,IAAIE,IAAI,IAAI,IAAI,EAAEJ,OAAO,CAACM,IAAI,CAAC,GAAGP,UAAU,GAAGM,KAAK,IAAID,IAAI,EAAE,CAAC;EACjE,CAAC,CAAC;EACF,OAAO,aAAajB,IAAI,CAACM,SAAS,EAAE;IAClCE,GAAG,EAAEA,GAAG;IACR,GAAGD,KAAK;IACRH,SAAS,EAAEV,UAAU,CAACU,SAAS,EAAEK,iBAAiB,EAAE,GAAGI,OAAO;EAChE,CAAC,CAAC;AACJ,CAAC,CAAC;AACFZ,GAAG,CAACmB,WAAW,GAAG,KAAK;AACvB,eAAenB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}