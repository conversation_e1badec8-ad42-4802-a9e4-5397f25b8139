import api from '../utils/axios';

// Mock data generation
const generateMockBooks = (count = 10) => {
  return Array.from({ length: count }, (_, i) => ({
    BookID: i + 1,
    Title: `Book Title ${i + 1}`,
    Author: `Author ${i + 1}`,
    Price: Math.round(Math.random() * 100) + 10,
    StockQuantity: Math.round(Math.random() * 100),
    Image: `/assets/images/product/book-${(i % 4) + 1}.jpg`,
    CategoryID: Math.floor(Math.random() * 5) + 1,
    category: {
      CategoryID: Math.floor(Math.random() * 5) + 1,
      Name: ["Fiction", "Non-Fiction", "Science", "History", "Biography"][
        Math.floor(Math.random() * 5)
      ],
    },
    CreatedAt: new Date().toISOString(),
    bookDetail: {
      ISBN10: `123456789${i}`,
      ISBN13: `978-123456789${i}`,
      Publisher: `Publisher ${i + 1}`,
      PublishYear: 2020 + (i % 4),
      Format: ["Hardcover", "Paperback", "Ebook", "Audiobook"][i % 4],
      PageCount: 200 + i * 20,
      Language: "English",
      Description: `This is a description for Book ${i + 1}. It contains interesting details about the plot and author.`,
    },
  }));
};

// Auth services
export const loginUser = (credentials) => {
  return api.post('/login', credentials);
};

export const registerUser = (userData) => {
  return api.post('/register', userData);
};

export const logoutUser = () => {
  return api.post('/logout');
};

// Dashboard statistics
export const fetchDashboardStats = () => {
  return api.get('/dashboard/stats');
};

// Books services
export const fetchBooks = async (page = 1, search = '') => {
  try {
    const response = await api.get(`/books?page=${page}&search=${search}`);
    return {
      data: {
        data: response.data,
        meta: {
          total: response.data.length,
          per_page: 10,
          current_page: page,
          last_page: Math.ceil(response.data.length / 10)
        }
      }
    };
  } catch (error) {
    console.warn("API call failed, using mock data");
    const mockBooks = generateMockBooks(20);
    return {
      data: {
        data: mockBooks,
        meta: {
          total: mockBooks.length,
          per_page: 10,
          current_page: page,
          last_page: Math.ceil(mockBooks.length / 10)
        }
      }
    };
  }
};

export const createBook = (bookData) => {
  return api.post('/books', bookData);
};

export const updateBook = (id, bookData) => {
  return api.put(`/books/${id}`, bookData);
};

export const deleteBook = (id) => {
  return api.delete(`/books/${id}`);
};

// Categories services
export const fetchCategories = (page = 1) => {
  return api.get(`/categories?page=${page}`);
};

export const createCategory = (categoryData) => {
  return api.post('/categories', categoryData);
};

export const updateCategory = (id, categoryData) => {
  return api.put(`/categories/${id}`, categoryData);
};

export const deleteCategory = (id) => {
  return api.delete(`/categories/${id}`);
};

// Orders services
export const fetchOrders = (page = 1, status = '') => {
  return api.get(`/orders?page=${page}${status ? `&status=${status}` : ''}`);
};

export const updateOrderStatus = (id, status) => {
  return api.put(`/orders/${id}`, { status });
};

export const fetchOrderStats = () => {
  return api.get('/orders/stats');
};

// Purchases services
export const fetchPurchases = (page = 1) => {
  return api.get(`/purchases?page=${page}`);
};

export const createPurchase = (purchaseData) => {
  return api.post('/purchases', purchaseData);
};

// Users services
export const fetchUsers = (page = 1, role = '') => {
  return api.get(`/users?page=${page}${role ? `&role=${role}` : ''}`);
};

export const updateUser = (id, userData) => {
  return api.put(`/users/${id}`, userData);
};

export const deleteUser = (id) => {
  return api.delete(`/users/${id}`);
};

// Pages services
export const fetchPages = (page = 1) => {
  return api.get(`/page-contents?page=${page}`);
};

export const createPage = (pageData) => {
  return api.post('/page-contents', pageData);
};

export const updatePage = (id, pageData) => {
  return api.put(`/page-contents/${id}`, pageData);
};

export const deletePage = (id) => {
  return api.delete(`/page-contents/${id}`);
}; 