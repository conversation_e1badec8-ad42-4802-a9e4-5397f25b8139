{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nconst ToastContext = /*#__PURE__*/React.createContext({\n  onClose() {}\n});\nexport default ToastContext;", "map": {"version": 3, "names": ["React", "ToastContext", "createContext", "onClose"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/react-bootstrap/esm/ToastContext.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nconst ToastContext = /*#__PURE__*/React.createContext({\n  onClose() {}\n});\nexport default ToastContext;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,YAAY,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC;EACpDC,OAAOA,CAAA,EAAG,CAAC;AACb,CAAC,CAAC;AACF,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}