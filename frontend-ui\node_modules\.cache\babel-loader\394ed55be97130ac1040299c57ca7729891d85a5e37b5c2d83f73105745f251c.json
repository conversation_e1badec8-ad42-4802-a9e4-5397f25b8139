{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\LaravelReact\\\\frontend-ui\\\\src\\\\components\\\\DynamicPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport axios from \"axios\";\nimport PageHeader from \"./PageHeader\";\nimport Puck<PERSON>enderer from \"./PuckRenderer\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DynamicPage = ({\n  slug,\n  title\n}) => {\n  _s();\n  const [pageContent, setPageContent] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const fetchPageContent = async () => {\n      try {\n        setLoading(true);\n        // Fetch the published page content by slug\n        const response = await axios.get(`/api/public/pages/${slug}`);\n\n        // Process the content\n        let content = response.data.content;\n\n        // Parse content if it's a string\n        if (typeof content === \"string\") {\n          try {\n            content = JSON.parse(content);\n          } catch (err) {\n            console.error(\"Error parsing page content:\", err);\n          }\n        }\n        console.log(\"Page content structure:\", content);\n\n        // Set the processed data\n        setPageContent({\n          ...response.data,\n          content: content\n        });\n        setLoading(false);\n      } catch (err) {\n        console.error(`Error fetching ${slug} page:`, err);\n        setError(`Failed to load ${title} content. Please try again later.`);\n        setLoading(false);\n      }\n    };\n    if (slug) {\n      fetchPageContent();\n    }\n  }, [slug, title]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dynamic-page\",\n    children: [/*#__PURE__*/_jsxDEV(PageHeader, {\n      title: title || (pageContent === null || pageContent === void 0 ? void 0 : pageContent.title) || \"\",\n      curPage: title || (pageContent === null || pageContent === void 0 ? void 0 : pageContent.title) || \"\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 9\n    }, this) : pageContent ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"page-title\",\n        children: pageContent.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(PuckRenderer, {\n        data: pageContent.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No content available yet. Please check back later.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n};\n_s(DynamicPage, \"KhgKMXu0MllkQIPTrUYP8t5kvuM=\");\n_c = DynamicPage;\nexport default DynamicPage;\nvar _c;\n$RefreshReg$(_c, \"DynamicPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "DynamicPage", "slug", "title", "_s", "pageContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "error", "setError", "fetchPageContent", "response", "get", "content", "data", "JSON", "parse", "err", "console", "log", "className", "children", "curPage", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/components/DynamicPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport axios from \"axios\";\nimport PageHeader from \"./PageHeader\";\nimport PuckRenderer from \"./PuckRenderer\";\n\nconst DynamicPage = ({ slug, title }) => {\n  const [pageContent, setPageContent] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    const fetchPageContent = async () => {\n      try {\n        setLoading(true);\n        // Fetch the published page content by slug\n        const response = await axios.get(`/api/public/pages/${slug}`);\n\n        // Process the content\n        let content = response.data.content;\n\n        // Parse content if it's a string\n        if (typeof content === \"string\") {\n          try {\n            content = JSON.parse(content);\n          } catch (err) {\n            console.error(\"Error parsing page content:\", err);\n          }\n        }\n\n        console.log(\"Page content structure:\", content);\n\n        // Set the processed data\n        setPageContent({\n          ...response.data,\n          content: content,\n        });\n\n        setLoading(false);\n      } catch (err) {\n        console.error(`Error fetching ${slug} page:`, err);\n        setError(`Failed to load ${title} content. Please try again later.`);\n        setLoading(false);\n      }\n    };\n\n    if (slug) {\n      fetchPageContent();\n    }\n  }, [slug, title]);\n\n  return (\n    <div className=\"dynamic-page\">\n      <PageHeader\n        title={title || pageContent?.title || \"\"}\n        curPage={title || pageContent?.title || \"\"}\n      />\n\n      {loading ? (\n        <div className=\"loading\">Loading...</div>\n      ) : error ? (\n        <div className=\"error\">{error}</div>\n      ) : pageContent ? (\n        <div className=\"container\">\n          <h1 className=\"page-title\">{pageContent.title}</h1>\n          <PuckRenderer data={pageContent.content} />\n        </div>\n      ) : (\n        <div className=\"container\">\n          <p>No content available yet. Please check back later.</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default DynamicPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,WAAW,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACd,MAAMgB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACFH,UAAU,CAAC,IAAI,CAAC;QAChB;QACA,MAAMI,QAAQ,GAAG,MAAMhB,KAAK,CAACiB,GAAG,CAAC,qBAAqBX,IAAI,EAAE,CAAC;;QAE7D;QACA,IAAIY,OAAO,GAAGF,QAAQ,CAACG,IAAI,CAACD,OAAO;;QAEnC;QACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;UAC/B,IAAI;YACFA,OAAO,GAAGE,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;UAC/B,CAAC,CAAC,OAAOI,GAAG,EAAE;YACZC,OAAO,CAACV,KAAK,CAAC,6BAA6B,EAAES,GAAG,CAAC;UACnD;QACF;QAEAC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEN,OAAO,CAAC;;QAE/C;QACAR,cAAc,CAAC;UACb,GAAGM,QAAQ,CAACG,IAAI;UAChBD,OAAO,EAAEA;QACX,CAAC,CAAC;QAEFN,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOU,GAAG,EAAE;QACZC,OAAO,CAACV,KAAK,CAAC,kBAAkBP,IAAI,QAAQ,EAAEgB,GAAG,CAAC;QAClDR,QAAQ,CAAC,kBAAkBP,KAAK,mCAAmC,CAAC;QACpEK,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIN,IAAI,EAAE;MACRS,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACT,IAAI,EAAEC,KAAK,CAAC,CAAC;EAEjB,oBACEH,OAAA;IAAKqB,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BtB,OAAA,CAACH,UAAU;MACTM,KAAK,EAAEA,KAAK,KAAIE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEF,KAAK,KAAI,EAAG;MACzCoB,OAAO,EAAEpB,KAAK,KAAIE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEF,KAAK,KAAI;IAAG;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,EAEDpB,OAAO,gBACNP,OAAA;MAAKqB,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAC;IAAU;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,GACvClB,KAAK,gBACPT,OAAA;MAAKqB,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAEb;IAAK;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,GAClCtB,WAAW,gBACbL,OAAA;MAAKqB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBtB,OAAA;QAAIqB,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAEjB,WAAW,CAACF;MAAK;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnD3B,OAAA,CAACF,YAAY;QAACiB,IAAI,EAAEV,WAAW,CAACS;MAAQ;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC,gBAEN3B,OAAA;MAAKqB,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBtB,OAAA;QAAAsB,QAAA,EAAG;MAAkD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACvB,EAAA,CApEIH,WAAW;AAAA2B,EAAA,GAAX3B,WAAW;AAsEjB,eAAeA,WAAW;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}