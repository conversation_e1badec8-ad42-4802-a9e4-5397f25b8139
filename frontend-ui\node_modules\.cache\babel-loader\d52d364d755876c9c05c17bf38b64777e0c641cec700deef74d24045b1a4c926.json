{"ast": null, "code": "// fetchProducts.js\nimport { get } from \"./apiService\";\nexport const fetchProducts = async () => {\n  try {\n    const result = await get(\"books\");\n    console.log(\"API Books Response:\", result);\n\n    // Check for the proper structure in the response\n    if (result && result.original && result.original.success && Array.isArray(result.original.data)) {\n      // Response is wrapped in a Laravel response object\n      const formattedData = result.original.data.map(book => {\n        var _book$category;\n        return {\n          id: book.BookID,\n          BookID: book.BookID,\n          title: book.Title,\n          Title: book.Title,\n          author: book.Author,\n          Author: book.Author,\n          price: book.Price,\n          Price: book.Price,\n          stock: book.StockQuantity,\n          StockQuantity: book.StockQuantity,\n          CategoryName: (_book$category = book.category) === null || _book$category === void 0 ? void 0 : _book$category.Name,\n          CategoryID: book.CategoryID,\n          category: book.category\n        };\n      });\n      return {\n        success: true,\n        data: formattedData\n      };\n    }\n    // Handle regular success response format\n    else if (result && result.success && Array.isArray(result.data)) {\n      // Standard API response format\n      const formattedData = result.data.map(book => {\n        var _book$category2;\n        return {\n          id: book.BookID,\n          BookID: book.BookID,\n          title: book.Title,\n          Title: book.Title,\n          author: book.Author,\n          Author: book.Author,\n          price: book.Price,\n          Price: book.Price,\n          stock: book.StockQuantity,\n          StockQuantity: book.StockQuantity,\n          CategoryName: (_book$category2 = book.category) === null || _book$category2 === void 0 ? void 0 : _book$category2.Name,\n          CategoryID: book.CategoryID,\n          category: book.category\n        };\n      });\n      return {\n        success: true,\n        data: formattedData\n      };\n    }\n    // Direct array response (fallback)\n    else if (Array.isArray(result)) {\n      const formattedData = result.map(book => {\n        var _book$category3;\n        return {\n          id: book.BookID || book.id,\n          BookID: book.BookID || book.id,\n          title: book.Title || book.title,\n          Title: book.Title || book.title,\n          author: book.Author || book.author,\n          Author: book.Author || book.author,\n          price: book.Price || book.price,\n          Price: book.Price || book.price,\n          stock: book.StockQuantity || book.stock,\n          StockQuantity: book.StockQuantity || book.stock,\n          CategoryName: ((_book$category3 = book.category) === null || _book$category3 === void 0 ? void 0 : _book$category3.Name) || book.CategoryName,\n          CategoryID: book.CategoryID || book.category_id,\n          category: book.category\n        };\n      });\n      return {\n        success: true,\n        data: formattedData\n      };\n    } else {\n      console.error(\"Unexpected API response format:\", result);\n      return {\n        success: false,\n        data: [],\n        message: \"Invalid response format from API\"\n      };\n    }\n  } catch (error) {\n    console.error(\"Error fetching books:\", error);\n    return {\n      success: false,\n      data: [],\n      message: error.message\n    };\n  }\n};", "map": {"version": 3, "names": ["get", "fetchProducts", "result", "console", "log", "original", "success", "Array", "isArray", "data", "formattedData", "map", "book", "_book$category", "id", "BookID", "title", "Title", "author", "Author", "price", "Price", "stock", "StockQuantity", "CategoryName", "category", "Name", "CategoryID", "_book$category2", "_book$category3", "category_id", "error", "message"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/utilis/fetchProducts.js"], "sourcesContent": ["// fetchProducts.js\nimport { get } from \"./apiService\";\n\nexport const fetchProducts = async () => {\n  try {\n    const result = await get(\"books\");\n    console.log(\"API Books Response:\", result);\n\n    // Check for the proper structure in the response\n    if (\n      result &&\n      result.original &&\n      result.original.success &&\n      Array.isArray(result.original.data)\n    ) {\n      // Response is wrapped in a Laravel response object\n      const formattedData = result.original.data.map((book) => ({\n        id: book.BookID,\n        BookID: book.BookID,\n        title: book.Title,\n        Title: book.Title,\n        author: book.Author,\n        Author: book.Author,\n        price: book.Price,\n        Price: book.Price,\n        stock: book.StockQuantity,\n        StockQuantity: book.StockQuantity,\n        CategoryName: book.category?.Name,\n        CategoryID: book.CategoryID,\n        category: book.category,\n      }));\n\n      return {\n        success: true,\n        data: formattedData,\n      };\n    }\n    // Handle regular success response format\n    else if (result && result.success && Array.isArray(result.data)) {\n      // Standard API response format\n      const formattedData = result.data.map((book) => ({\n        id: book.BookID,\n        BookID: book.BookID,\n        title: book.Title,\n        Title: book.Title,\n        author: book.Author,\n        Author: book.Author,\n        price: book.Price,\n        Price: book.Price,\n        stock: book.StockQuantity,\n        StockQuantity: book.StockQuantity,\n        CategoryName: book.category?.Name,\n        CategoryID: book.CategoryID,\n        category: book.category,\n      }));\n\n      return {\n        success: true,\n        data: formattedData,\n      };\n    }\n    // Direct array response (fallback)\n    else if (Array.isArray(result)) {\n      const formattedData = result.map((book) => ({\n        id: book.BookID || book.id,\n        BookID: book.BookID || book.id,\n        title: book.Title || book.title,\n        Title: book.Title || book.title,\n        author: book.Author || book.author,\n        Author: book.Author || book.author,\n        price: book.Price || book.price,\n        Price: book.Price || book.price,\n        stock: book.StockQuantity || book.stock,\n        StockQuantity: book.StockQuantity || book.stock,\n        CategoryName: book.category?.Name || book.CategoryName,\n        CategoryID: book.CategoryID || book.category_id,\n        category: book.category,\n      }));\n\n      return {\n        success: true,\n        data: formattedData,\n      };\n    } else {\n      console.error(\"Unexpected API response format:\", result);\n      return {\n        success: false,\n        data: [],\n        message: \"Invalid response format from API\",\n      };\n    }\n  } catch (error) {\n    console.error(\"Error fetching books:\", error);\n    return {\n      success: false,\n      data: [],\n      message: error.message,\n    };\n  }\n};\n"], "mappings": "AAAA;AACA,SAASA,GAAG,QAAQ,cAAc;AAElC,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;EACvC,IAAI;IACF,MAAMC,MAAM,GAAG,MAAMF,GAAG,CAAC,OAAO,CAAC;IACjCG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,MAAM,CAAC;;IAE1C;IACA,IACEA,MAAM,IACNA,MAAM,CAACG,QAAQ,IACfH,MAAM,CAACG,QAAQ,CAACC,OAAO,IACvBC,KAAK,CAACC,OAAO,CAACN,MAAM,CAACG,QAAQ,CAACI,IAAI,CAAC,EACnC;MACA;MACA,MAAMC,aAAa,GAAGR,MAAM,CAACG,QAAQ,CAACI,IAAI,CAACE,GAAG,CAAEC,IAAI;QAAA,IAAAC,cAAA;QAAA,OAAM;UACxDC,EAAE,EAAEF,IAAI,CAACG,MAAM;UACfA,MAAM,EAAEH,IAAI,CAACG,MAAM;UACnBC,KAAK,EAAEJ,IAAI,CAACK,KAAK;UACjBA,KAAK,EAAEL,IAAI,CAACK,KAAK;UACjBC,MAAM,EAAEN,IAAI,CAACO,MAAM;UACnBA,MAAM,EAAEP,IAAI,CAACO,MAAM;UACnBC,KAAK,EAAER,IAAI,CAACS,KAAK;UACjBA,KAAK,EAAET,IAAI,CAACS,KAAK;UACjBC,KAAK,EAAEV,IAAI,CAACW,aAAa;UACzBA,aAAa,EAAEX,IAAI,CAACW,aAAa;UACjCC,YAAY,GAAAX,cAAA,GAAED,IAAI,CAACa,QAAQ,cAAAZ,cAAA,uBAAbA,cAAA,CAAea,IAAI;UACjCC,UAAU,EAAEf,IAAI,CAACe,UAAU;UAC3BF,QAAQ,EAAEb,IAAI,CAACa;QACjB,CAAC;MAAA,CAAC,CAAC;MAEH,OAAO;QACLnB,OAAO,EAAE,IAAI;QACbG,IAAI,EAAEC;MACR,CAAC;IACH;IACA;IAAA,KACK,IAAIR,MAAM,IAAIA,MAAM,CAACI,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACN,MAAM,CAACO,IAAI,CAAC,EAAE;MAC/D;MACA,MAAMC,aAAa,GAAGR,MAAM,CAACO,IAAI,CAACE,GAAG,CAAEC,IAAI;QAAA,IAAAgB,eAAA;QAAA,OAAM;UAC/Cd,EAAE,EAAEF,IAAI,CAACG,MAAM;UACfA,MAAM,EAAEH,IAAI,CAACG,MAAM;UACnBC,KAAK,EAAEJ,IAAI,CAACK,KAAK;UACjBA,KAAK,EAAEL,IAAI,CAACK,KAAK;UACjBC,MAAM,EAAEN,IAAI,CAACO,MAAM;UACnBA,MAAM,EAAEP,IAAI,CAACO,MAAM;UACnBC,KAAK,EAAER,IAAI,CAACS,KAAK;UACjBA,KAAK,EAAET,IAAI,CAACS,KAAK;UACjBC,KAAK,EAAEV,IAAI,CAACW,aAAa;UACzBA,aAAa,EAAEX,IAAI,CAACW,aAAa;UACjCC,YAAY,GAAAI,eAAA,GAAEhB,IAAI,CAACa,QAAQ,cAAAG,eAAA,uBAAbA,eAAA,CAAeF,IAAI;UACjCC,UAAU,EAAEf,IAAI,CAACe,UAAU;UAC3BF,QAAQ,EAAEb,IAAI,CAACa;QACjB,CAAC;MAAA,CAAC,CAAC;MAEH,OAAO;QACLnB,OAAO,EAAE,IAAI;QACbG,IAAI,EAAEC;MACR,CAAC;IACH;IACA;IAAA,KACK,IAAIH,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,EAAE;MAC9B,MAAMQ,aAAa,GAAGR,MAAM,CAACS,GAAG,CAAEC,IAAI;QAAA,IAAAiB,eAAA;QAAA,OAAM;UAC1Cf,EAAE,EAAEF,IAAI,CAACG,MAAM,IAAIH,IAAI,CAACE,EAAE;UAC1BC,MAAM,EAAEH,IAAI,CAACG,MAAM,IAAIH,IAAI,CAACE,EAAE;UAC9BE,KAAK,EAAEJ,IAAI,CAACK,KAAK,IAAIL,IAAI,CAACI,KAAK;UAC/BC,KAAK,EAAEL,IAAI,CAACK,KAAK,IAAIL,IAAI,CAACI,KAAK;UAC/BE,MAAM,EAAEN,IAAI,CAACO,MAAM,IAAIP,IAAI,CAACM,MAAM;UAClCC,MAAM,EAAEP,IAAI,CAACO,MAAM,IAAIP,IAAI,CAACM,MAAM;UAClCE,KAAK,EAAER,IAAI,CAACS,KAAK,IAAIT,IAAI,CAACQ,KAAK;UAC/BC,KAAK,EAAET,IAAI,CAACS,KAAK,IAAIT,IAAI,CAACQ,KAAK;UAC/BE,KAAK,EAAEV,IAAI,CAACW,aAAa,IAAIX,IAAI,CAACU,KAAK;UACvCC,aAAa,EAAEX,IAAI,CAACW,aAAa,IAAIX,IAAI,CAACU,KAAK;UAC/CE,YAAY,EAAE,EAAAK,eAAA,GAAAjB,IAAI,CAACa,QAAQ,cAAAI,eAAA,uBAAbA,eAAA,CAAeH,IAAI,KAAId,IAAI,CAACY,YAAY;UACtDG,UAAU,EAAEf,IAAI,CAACe,UAAU,IAAIf,IAAI,CAACkB,WAAW;UAC/CL,QAAQ,EAAEb,IAAI,CAACa;QACjB,CAAC;MAAA,CAAC,CAAC;MAEH,OAAO;QACLnB,OAAO,EAAE,IAAI;QACbG,IAAI,EAAEC;MACR,CAAC;IACH,CAAC,MAAM;MACLP,OAAO,CAAC4B,KAAK,CAAC,iCAAiC,EAAE7B,MAAM,CAAC;MACxD,OAAO;QACLI,OAAO,EAAE,KAAK;QACdG,IAAI,EAAE,EAAE;QACRuB,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC,CAAC,OAAOD,KAAK,EAAE;IACd5B,OAAO,CAAC4B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7C,OAAO;MACLzB,OAAO,EAAE,KAAK;MACdG,IAAI,EAAE,EAAE;MACRuB,OAAO,EAAED,KAAK,CAACC;IACjB,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}