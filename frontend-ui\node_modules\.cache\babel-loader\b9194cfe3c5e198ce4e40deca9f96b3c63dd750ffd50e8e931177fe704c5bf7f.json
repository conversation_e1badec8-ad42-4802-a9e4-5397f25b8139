{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\LaravelReact\\\\frontend-ui\\\\src\\\\home\\\\Register.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst subTitle = \"Save The Day\";\nconst title = /*#__PURE__*/_jsxDEV(\"h2\", {\n  className: \"title\",\n  children: [\"Join on Day Long Free Workshop for \", /*#__PURE__*/_jsxDEV(\"b\", {\n    children: [\"Advance \", /*#__PURE__*/_jsxDEV(\"span\", {\n      children: \"Mastering \"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 5,\n      columnNumber: 73\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 62\n  }, this), \"on Sales\"]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 5,\n  columnNumber: 5\n}, this);\nconst desc = \"Limited Time Offer! Hurry Up\";\nconst Register = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"register-section padding-tb pb-0\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row g4 row-cols-lg-2 row-cols-1 align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"subtitle\",\n              children: subTitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 17,\n              columnNumber: 29\n            }, this), title, /*#__PURE__*/_jsxDEV(\"p\", {\n              children: desc\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Register Now\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              className: \"register-form\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"name\",\n                placeholder: \"Username\",\n                className: \"reg-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                name: \"email\",\n                placeholder: \"Email\",\n                className: \"reg-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"number\",\n                placeholder: \"Phone\",\n                className: \"reg-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"lab-btn\",\n                children: \"Register Now\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 9\n  }, this);\n};\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "subTitle", "title", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "desc", "Register", "type", "name", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/home/<USER>"], "sourcesContent": ["import React from 'react';\n\nconst subTitle = \"Save The Day\";\nconst title = (\n    <h2 className='title'>Join on Day Long Free Workshop for <b>Advance <span>Mastering </span></b>on Sales</h2>\n)\n\nconst desc = \"Limited Time Offer! Hurry Up\";\n\nconst Register = () => {\n    return (\n        <section className='register-section padding-tb pb-0'>\n            <div className='container'>\n                <div className='row g4 row-cols-lg-2 row-cols-1 align-items-center'>\n                    <div className='col'>\n                        <div className='section-header'>\n                            <span className='subtitle'>{subTitle}</span>\n                            {title}\n                            <p>{desc}</p>\n                        </div>\n                    </div>\n                    <div className='col'>\n                        <div className='section-wrapper'>\n                            <h4>Register Now</h4>\n                            <form className='register-form'>\n                                <input type=\"text\" name='name' placeholder='Username' className='reg-input' />\n                                <input type=\"email\" name='email' placeholder='Email' className='reg-input' />\n                                <input type=\"number\" name='number' placeholder='Phone' className='reg-input' />\n                                <button type='submit' className='lab-btn'>\n                                    Register Now\n                                </button>\n                            </form>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </section>\n    )\n}\n\nexport default Register"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,QAAQ,GAAG,cAAc;AAC/B,MAAMC,KAAK,gBACPF,OAAA;EAAIG,SAAS,EAAC,OAAO;EAAAC,QAAA,GAAC,qCAAmC,eAAAJ,OAAA;IAAAI,QAAA,GAAG,UAAQ,eAAAJ,OAAA;MAAAI,QAAA,EAAM;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC,YAAQ;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAI,CAC9G;AAED,MAAMC,IAAI,GAAG,8BAA8B;AAE3C,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACnB,oBACIV,OAAA;IAASG,SAAS,EAAC,kCAAkC;IAAAC,QAAA,eACjDJ,OAAA;MAAKG,SAAS,EAAC,WAAW;MAAAC,QAAA,eACtBJ,OAAA;QAAKG,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBAC/DJ,OAAA;UAAKG,SAAS,EAAC,KAAK;UAAAC,QAAA,eAChBJ,OAAA;YAAKG,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3BJ,OAAA;cAAMG,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAEH;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC3CN,KAAK,eACNF,OAAA;cAAAI,QAAA,EAAIK;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNR,OAAA;UAAKG,SAAS,EAAC,KAAK;UAAAC,QAAA,eAChBJ,OAAA;YAAKG,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5BJ,OAAA;cAAAI,QAAA,EAAI;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBR,OAAA;cAAMG,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC3BJ,OAAA;gBAAOW,IAAI,EAAC,MAAM;gBAACC,IAAI,EAAC,MAAM;gBAACC,WAAW,EAAC,UAAU;gBAACV,SAAS,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9ER,OAAA;gBAAOW,IAAI,EAAC,OAAO;gBAACC,IAAI,EAAC,OAAO;gBAACC,WAAW,EAAC,OAAO;gBAACV,SAAS,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7ER,OAAA;gBAAOW,IAAI,EAAC,QAAQ;gBAACC,IAAI,EAAC,QAAQ;gBAACC,WAAW,EAAC,OAAO;gBAACV,SAAS,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/ER,OAAA;gBAAQW,IAAI,EAAC,QAAQ;gBAACR,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAElB,CAAC;AAAAM,EAAA,GA7BKJ,QAAQ;AA+Bd,eAAeA,QAAQ;AAAA,IAAAI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}