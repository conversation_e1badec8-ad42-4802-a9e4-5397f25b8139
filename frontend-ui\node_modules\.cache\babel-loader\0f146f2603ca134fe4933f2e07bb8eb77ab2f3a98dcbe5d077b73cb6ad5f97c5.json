{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\LaravelReact\\\\frontend-ui\\\\src\\\\about\\\\About.jsx\";\nimport React from \"react\";\nimport PageHeader from \"../components/PageHeader\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst subTitle = \"About Our Brand\";\nconst title = \"Good Qualification Services And Better Expriences\";\nconst desc = \"Distinctively provide acces mutfuncto users whereas transparent proceses somes ncentivize eficient functionalities rather than extensible archtectur communicate leveraged services and cross-platform.\";\nconst year = \"30+\";\nconst expareance = \"Years Of Experiences\";\nconst aboutList = [{\n  imgUrl: \"/src/assets/images/about/icon/01.jpg\",\n  imgAlt: \"about icon rajibraj91 rajibraj\",\n  title: \"Skilled Instructors\",\n  desc: \"Distinctively provide acces mutfuncto users whereas communicate leveraged services\"\n}, {\n  imgUrl: \"/src/assets/images/about/icon/02.jpg\",\n  imgAlt: \"about icon rajibraj91 rajibraj\",\n  title: \"Get Certificate\",\n  desc: \"Distinctively provide acces mutfuncto users whereas communicate leveraged services\"\n}, {\n  imgUrl: \"/src/assets/images/about/icon/03.jpg\",\n  imgAlt: \"about icon rajibraj91 rajibraj\",\n  title: \"Online Classes\",\n  desc: \"Distinctively provide acces mutfuncto users whereas communicate leveraged services\"\n}];\nconst About = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(PageHeader, {\n      title: \"About Our Brand\",\n      curPage: \"About\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"about-section style-3 padding-tb section-bg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row justify-content-center row-cols-xl-2 row-cols-1 align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"about-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"about-thumb\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/src/assets/images/about/01.jpg\",\n                  alt: \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 43,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"abs-thumb\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/src/assets/images/about/02.jpg\",\n                  alt: \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 46,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"about-left-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: year\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 49,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: expareance\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 50,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"about-right\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"section-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"subtitle\",\n                  children: subTitle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"title\",\n                  children: title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 60,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: desc\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 61,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"section-wrapper\",\n                children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"lab-ul\",\n                  children: aboutList.map((val, i) => /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"sr-left\",\n                      children: /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: val.imgUrl,\n                        alt: \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 69,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 68,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"sr-right\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        children: val.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 72,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: val.desc\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 73,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 71,\n                      columnNumber: 25\n                    }, this)]\n                  }, i, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 67,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 65,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_c = About;\nexport default About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "subTitle", "title", "desc", "year", "expareance", "aboutList", "imgUrl", "imgAlt", "About", "children", "curPage", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "src", "alt", "map", "val", "i", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/about/About.jsx"], "sourcesContent": ["import React from \"react\";\nimport PageHeader from \"../components/PageHeader\";\n\nconst subTitle = \"About Our Brand\";\nconst title = \"Good Qualification Services And Better Expriences\";\nconst desc =\n  \"Distinctively provide acces mutfuncto users whereas transparent proceses somes ncentivize eficient functionalities rather than extensible archtectur communicate leveraged services and cross-platform.\";\n\nconst year = \"30+\";\nconst expareance = \"Years Of Experiences\";\n\nconst aboutList = [\n  {\n    imgUrl: \"/src/assets/images/about/icon/01.jpg\",\n    imgAlt: \"about icon rajibraj91 rajibraj\",\n    title: \"Skilled Instructors\",\n    desc: \"Distinctively provide acces mutfuncto users whereas communicate leveraged services\",\n  },\n  {\n    imgUrl: \"/src/assets/images/about/icon/02.jpg\",\n    imgAlt: \"about icon rajibraj91 rajibraj\",\n    title: \"Get Certificate\",\n    desc: \"Distinctively provide acces mutfuncto users whereas communicate leveraged services\",\n  },\n  {\n    imgUrl: \"/src/assets/images/about/icon/03.jpg\",\n    imgAlt: \"about icon rajibraj91 rajibraj\",\n    title: \"Online Classes\",\n    desc: \"Distinctively provide acces mutfuncto users whereas communicate leveraged services\",\n  },\n];\n\nconst About = () => {\n  return (\n    <div>\n      <PageHeader title={\"About Our Brand\"} curPage={\"About\"} />\n      <div className=\"about-section style-3 padding-tb section-bg\">\n        <div className=\"container\">\n          <div className=\"row justify-content-center row-cols-xl-2 row-cols-1 align-items-center\">\n            <div className=\"col\">\n              <div className=\"about-left\">\n                <div className=\"about-thumb\">\n                  <img src=\"/src/assets/images/about/01.jpg\" alt=\"\" />\n                </div>\n                <div className=\"abs-thumb\">\n                  <img src=\"/src/assets/images/about/02.jpg\" alt=\"\" />\n                </div>\n                <div className=\"about-left-content\">\n                  <h3>{year}</h3>\n                  <p>{expareance}</p>\n                </div>\n              </div>\n            </div>\n\n            {/* 2nd col */}\n            <div className=\"col\">\n              <div className=\"about-right\">\n                <div className=\"section-header\">\n                  <span className=\"subtitle\">{subTitle}</span>\n                  <h2 className=\"title\">{title}</h2>\n                  <p>{desc}</p>\n                </div>\n\n                <div className=\"section-wrapper\">\n                  <ul className=\"lab-ul\">\n                    {aboutList.map((val, i) => (\n                      <li key={i}>\n                        <div className=\"sr-left\">\n                          <img src={val.imgUrl} alt=\"\" />\n                        </div>\n                        <div className=\"sr-right\">\n                          <h5>{val.title}</h5>\n                          <p>{val.desc}</p>\n                        </div>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default About;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,QAAQ,GAAG,iBAAiB;AAClC,MAAMC,KAAK,GAAG,mDAAmD;AACjE,MAAMC,IAAI,GACR,yMAAyM;AAE3M,MAAMC,IAAI,GAAG,KAAK;AAClB,MAAMC,UAAU,GAAG,sBAAsB;AAEzC,MAAMC,SAAS,GAAG,CAChB;EACEC,MAAM,EAAE,sCAAsC;EAC9CC,MAAM,EAAE,gCAAgC;EACxCN,KAAK,EAAE,qBAAqB;EAC5BC,IAAI,EAAE;AACR,CAAC,EACD;EACEI,MAAM,EAAE,sCAAsC;EAC9CC,MAAM,EAAE,gCAAgC;EACxCN,KAAK,EAAE,iBAAiB;EACxBC,IAAI,EAAE;AACR,CAAC,EACD;EACEI,MAAM,EAAE,sCAAsC;EAC9CC,MAAM,EAAE,gCAAgC;EACxCN,KAAK,EAAE,gBAAgB;EACvBC,IAAI,EAAE;AACR,CAAC,CACF;AAED,MAAMM,KAAK,GAAGA,CAAA,KAAM;EAClB,oBACET,OAAA;IAAAU,QAAA,gBACEV,OAAA,CAACF,UAAU;MAACI,KAAK,EAAE,iBAAkB;MAACS,OAAO,EAAE;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1Df,OAAA;MAAKgB,SAAS,EAAC,6CAA6C;MAAAN,QAAA,eAC1DV,OAAA;QAAKgB,SAAS,EAAC,WAAW;QAAAN,QAAA,eACxBV,OAAA;UAAKgB,SAAS,EAAC,wEAAwE;UAAAN,QAAA,gBACrFV,OAAA;YAAKgB,SAAS,EAAC,KAAK;YAAAN,QAAA,eAClBV,OAAA;cAAKgB,SAAS,EAAC,YAAY;cAAAN,QAAA,gBACzBV,OAAA;gBAAKgB,SAAS,EAAC,aAAa;gBAAAN,QAAA,eAC1BV,OAAA;kBAAKiB,GAAG,EAAC,iCAAiC;kBAACC,GAAG,EAAC;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACNf,OAAA;gBAAKgB,SAAS,EAAC,WAAW;gBAAAN,QAAA,eACxBV,OAAA;kBAAKiB,GAAG,EAAC,iCAAiC;kBAACC,GAAG,EAAC;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACNf,OAAA;gBAAKgB,SAAS,EAAC,oBAAoB;gBAAAN,QAAA,gBACjCV,OAAA;kBAAAU,QAAA,EAAKN;gBAAI;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACff,OAAA;kBAAAU,QAAA,EAAIL;gBAAU;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNf,OAAA;YAAKgB,SAAS,EAAC,KAAK;YAAAN,QAAA,eAClBV,OAAA;cAAKgB,SAAS,EAAC,aAAa;cAAAN,QAAA,gBAC1BV,OAAA;gBAAKgB,SAAS,EAAC,gBAAgB;gBAAAN,QAAA,gBAC7BV,OAAA;kBAAMgB,SAAS,EAAC,UAAU;kBAAAN,QAAA,EAAET;gBAAQ;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5Cf,OAAA;kBAAIgB,SAAS,EAAC,OAAO;kBAAAN,QAAA,EAAER;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClCf,OAAA;kBAAAU,QAAA,EAAIP;gBAAI;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAENf,OAAA;gBAAKgB,SAAS,EAAC,iBAAiB;gBAAAN,QAAA,eAC9BV,OAAA;kBAAIgB,SAAS,EAAC,QAAQ;kBAAAN,QAAA,EACnBJ,SAAS,CAACa,GAAG,CAAC,CAACC,GAAG,EAAEC,CAAC,kBACpBrB,OAAA;oBAAAU,QAAA,gBACEV,OAAA;sBAAKgB,SAAS,EAAC,SAAS;sBAAAN,QAAA,eACtBV,OAAA;wBAAKiB,GAAG,EAAEG,GAAG,CAACb,MAAO;wBAACW,GAAG,EAAC;sBAAE;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC,eACNf,OAAA;sBAAKgB,SAAS,EAAC,UAAU;sBAAAN,QAAA,gBACvBV,OAAA;wBAAAU,QAAA,EAAKU,GAAG,CAAClB;sBAAK;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACpBf,OAAA;wBAAAU,QAAA,EAAIU,GAAG,CAACjB;sBAAI;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CAAC;kBAAA,GAPCM,CAAC;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAQN,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACO,EAAA,GArDIb,KAAK;AAuDX,eAAeA,KAAK;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}