import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

function UserMenu() {
  const [isOpen, setIsOpen] = useState(false);
  const [user, setUser] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    // Get the user from localStorage
    const userStr = localStorage.getItem("auth_user");
    if (userStr) {
      setUser(JSON.parse(userStr));
    }
  }, []);

  const handleLogout = async () => {
    try {
      // Clear local storage
      localStorage.removeItem("auth_user");
      localStorage.removeItem("auth_token");
      navigate("/login");
    } catch (error) {
      console.error("Logout error:", error);
      // Even if there's an error, redirect to login
      navigate("/login");
    }
  };

  // Get user's initials for display when no avatar is available
  const getUserInitials = () => {
    if (!user) return "U";

    const firstName = user?.firstName || "";
    const lastName = user?.lastName || "";

    return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
  };

  return (
    <>
      {/* Light/Dark Mode */}
      <div className="ms-1 header-item d-none d-sm-flex">
        <button 
          type="button" 
          className="btn btn-icon btn-topbar btn-ghost-secondary"
          id="light-dark-mode"
        >
          <i className="ri-moon-line fs-22"></i>
        </button>
      </div>

      {/* User Profile */}
      <div className={`ms-1 header-item ${isOpen ? 'show' : ''}`}>
        <button
          type="button"
          className="btn btn-icon btn-topbar btn-ghost-secondary"
          id="page-header-user-dropdown"
          onClick={() => setIsOpen(!isOpen)}
        >
          {user && user.avatar ? (
            <img
              className="rounded-circle header-profile-user"
              src={user.avatar}
              alt={`${user.firstName} ${user.lastName}`}
            />
          ) : (
            <span className="d-flex align-items-center justify-content-center rounded-circle header-profile-user bg-primary text-white">
              {getUserInitials()}
            </span>
          )}
          <span className="d-none d-xl-inline-block ms-1 fw-medium user-name-text">
            {user ? `${user.firstName} ${user.lastName}` : "User"}
          </span>
          <i className="ri-arrow-down-s-line d-none d-xl-inline-block ms-1 fs-12"></i>
        </button>
        <div className={`dropdown-menu dropdown-menu-end ${isOpen ? 'show' : ''}`}>
          <h6 className="dropdown-header">Welcome {user ? `${user.firstName}` : "User"}!</h6>
          <a className="dropdown-item" href="/profile">
            <i className="ri-user-line align-middle me-1"></i>
            <span className="align-middle">Profile</span>
          </a>
          <a className="dropdown-item" href="/settings">
            <i className="ri-settings-4-line align-middle me-1"></i>
            <span className="align-middle">Settings</span>
          </a>
          <div className="dropdown-divider"></div>
          <button 
            className="dropdown-item text-danger" 
            onClick={handleLogout}
          >
            <i className="ri-logout-box-line align-middle me-1 text-danger"></i>
            <span className="align-middle">Logout</span>
          </button>
        </div>
      </div>
    </>
  );
}

export default UserMenu; 