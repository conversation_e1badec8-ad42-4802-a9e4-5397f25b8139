{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\LaravelReact\\\\frontend-ui\\\\src\\\\shop\\\\Pagination.jsx\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Pagination = ({\n  productsPerPage,\n  totalProducts,\n  paginate,\n  activePage\n}) => {\n  const totalPages = Math.ceil(totalProducts / productsPerPage);\n  const visiblePageCount = 5; // Display 5 page numbers at a time\n\n  // Calculate the range of pages to display based on the current active page\n  let startPage = Math.max(1, activePage - Math.floor(visiblePageCount / 2));\n  let endPage = Math.min(totalPages, startPage + visiblePageCount - 1);\n\n  // Adjust startPage if there's space after the endPage (to ensure the full range of pages is visible)\n  if (endPage - startPage + 1 < visiblePageCount) {\n    startPage = Math.max(1, endPage - visiblePageCount + 1);\n  }\n\n  // Generate the page numbers to display\n  const pageNumbers = [];\n  for (let i = startPage; i <= endPage; i++) {\n    pageNumbers.push(i);\n  }\n  return /*#__PURE__*/_jsxDEV(\"ul\", {\n    className: \"default-pagination lab-ul\",\n    children: [totalPages > 1 && /*#__PURE__*/_jsxDEV(\"li\", {\n      children: /*#__PURE__*/_jsxDEV(\"a\", {\n        href: \"#\",\n        onClick: e => {\n          e.preventDefault();\n          if (activePage > 1) {\n            paginate(activePage - 1);\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"icofont-rounded-left\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 9\n    }, this), pageNumbers.map(number => /*#__PURE__*/_jsxDEV(\"li\", {\n      className: `page-item ${number === activePage ? \"bg-warning\" : \"\"}`,\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => paginate(number),\n        className: \"bg-transparent\",\n        children: number\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 11\n      }, this)\n    }, number, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 9\n    }, this)), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"li\", {\n      children: /*#__PURE__*/_jsxDEV(\"a\", {\n        href: \"#\",\n        onClick: e => {\n          e.preventDefault();\n          if (activePage < totalPages) {\n            paginate(activePage + 1);\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"icofont-rounded-right\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n_c = Pagination;\nexport default Pagination;\nvar _c;\n$RefreshReg$(_c, \"Pagination\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Pagination", "productsPerPage", "totalProducts", "paginate", "activePage", "totalPages", "Math", "ceil", "visiblePageCount", "startPage", "max", "floor", "endPage", "min", "pageNumbers", "i", "push", "className", "children", "href", "onClick", "e", "preventDefault", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "number", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/shop/Pagination.jsx"], "sourcesContent": ["import React from \"react\";\n\nconst Pagination = ({\n  productsPerPage,\n  totalProducts,\n  paginate,\n  activePage,\n}) => {\n  const totalPages = Math.ceil(totalProducts / productsPerPage);\n  const visiblePageCount = 5; // Display 5 page numbers at a time\n\n  // Calculate the range of pages to display based on the current active page\n  let startPage = Math.max(1, activePage - Math.floor(visiblePageCount / 2));\n  let endPage = Math.min(totalPages, startPage + visiblePageCount - 1);\n\n  // Adjust startPage if there's space after the endPage (to ensure the full range of pages is visible)\n  if (endPage - startPage + 1 < visiblePageCount) {\n    startPage = Math.max(1, endPage - visiblePageCount + 1);\n  }\n\n  // Generate the page numbers to display\n  const pageNumbers = [];\n  for (let i = startPage; i <= endPage; i++) {\n    pageNumbers.push(i);\n  }\n\n  return (\n    <ul className=\"default-pagination lab-ul\">\n      {/* Left Arrow - Only show if there are multiple pages and we're not on page 1 */}\n      {totalPages > 1 && (\n        <li>\n          <a\n            href=\"#\"\n            onClick={(e) => {\n              e.preventDefault();\n              if (activePage > 1) {\n                paginate(activePage - 1);\n              }\n            }}\n          >\n            <i className=\"icofont-rounded-left\"></i>\n          </a>\n        </li>\n      )}\n\n      {/* Page Numbers */}\n      {pageNumbers.map((number) => (\n        <li\n          key={number}\n          className={`page-item ${number === activePage ? \"bg-warning\" : \"\"}`}\n        >\n          <button onClick={() => paginate(number)} className=\"bg-transparent\">\n            {number}\n          </button>\n        </li>\n      ))}\n\n      {/* Right Arrow - Only show if there are multiple pages and we're not on last page */}\n      {totalPages > 1 && (\n        <li>\n          <a\n            href=\"#\"\n            onClick={(e) => {\n              e.preventDefault();\n              if (activePage < totalPages) {\n                paginate(activePage + 1);\n              }\n            }}\n          >\n            <i className=\"icofont-rounded-right\"></i>\n          </a>\n        </li>\n      )}\n    </ul>\n  );\n};\n\nexport default Pagination;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAC;EAClBC,eAAe;EACfC,aAAa;EACbC,QAAQ;EACRC;AACF,CAAC,KAAK;EACJ,MAAMC,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACL,aAAa,GAAGD,eAAe,CAAC;EAC7D,MAAMO,gBAAgB,GAAG,CAAC,CAAC,CAAC;;EAE5B;EACA,IAAIC,SAAS,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEN,UAAU,GAAGE,IAAI,CAACK,KAAK,CAACH,gBAAgB,GAAG,CAAC,CAAC,CAAC;EAC1E,IAAII,OAAO,GAAGN,IAAI,CAACO,GAAG,CAACR,UAAU,EAAEI,SAAS,GAAGD,gBAAgB,GAAG,CAAC,CAAC;;EAEpE;EACA,IAAII,OAAO,GAAGH,SAAS,GAAG,CAAC,GAAGD,gBAAgB,EAAE;IAC9CC,SAAS,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEE,OAAO,GAAGJ,gBAAgB,GAAG,CAAC,CAAC;EACzD;;EAEA;EACA,MAAMM,WAAW,GAAG,EAAE;EACtB,KAAK,IAAIC,CAAC,GAAGN,SAAS,EAAEM,CAAC,IAAIH,OAAO,EAAEG,CAAC,EAAE,EAAE;IACzCD,WAAW,CAACE,IAAI,CAACD,CAAC,CAAC;EACrB;EAEA,oBACEhB,OAAA;IAAIkB,SAAS,EAAC,2BAA2B;IAAAC,QAAA,GAEtCb,UAAU,GAAG,CAAC,iBACbN,OAAA;MAAAmB,QAAA,eACEnB,OAAA;QACEoB,IAAI,EAAC,GAAG;QACRC,OAAO,EAAGC,CAAC,IAAK;UACdA,CAAC,CAACC,cAAc,CAAC,CAAC;UAClB,IAAIlB,UAAU,GAAG,CAAC,EAAE;YAClBD,QAAQ,CAACC,UAAU,GAAG,CAAC,CAAC;UAC1B;QACF,CAAE;QAAAc,QAAA,eAEFnB,OAAA;UAAGkB,SAAS,EAAC;QAAsB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACL,EAGAZ,WAAW,CAACa,GAAG,CAAEC,MAAM,iBACtB7B,OAAA;MAEEkB,SAAS,EAAE,aAAaW,MAAM,KAAKxB,UAAU,GAAG,YAAY,GAAG,EAAE,EAAG;MAAAc,QAAA,eAEpEnB,OAAA;QAAQqB,OAAO,EAAEA,CAAA,KAAMjB,QAAQ,CAACyB,MAAM,CAAE;QAACX,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAChEU;MAAM;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC,GALJE,MAAM;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAMT,CACL,CAAC,EAGDrB,UAAU,GAAG,CAAC,iBACbN,OAAA;MAAAmB,QAAA,eACEnB,OAAA;QACEoB,IAAI,EAAC,GAAG;QACRC,OAAO,EAAGC,CAAC,IAAK;UACdA,CAAC,CAACC,cAAc,CAAC,CAAC;UAClB,IAAIlB,UAAU,GAAGC,UAAU,EAAE;YAC3BF,QAAQ,CAACC,UAAU,GAAG,CAAC,CAAC;UAC1B;QACF,CAAE;QAAAc,QAAA,eAEFnB,OAAA;UAAGkB,SAAS,EAAC;QAAuB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACL;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAET,CAAC;AAACG,EAAA,GAzEI7B,UAAU;AA2EhB,eAAeA,UAAU;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}