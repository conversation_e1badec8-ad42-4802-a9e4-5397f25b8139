{"name": "backend-dashboard-vite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "axios": "^1.8.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.1", "react-toastify": "^11.0.5", "web-vitals": "^3.5.2"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.1"}}