import React, { useState, useEffect } from 'react';
import axios from '../utils/axios';
import { toast } from 'react-toastify';
import Spinner from '../components/Spinner';

function Pages() {
  const [pages, setPages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [pagination, setPagination] = useState({
    currentPage: 1,
    perPage: 10,
    total: 0,
    lastPage: 1
  });
  const [stats, setStats] = useState({
    total: 0,
    published: 0,
    draft: 0,
    archived: 0
  });

  // Fetch pages with pagination
  useEffect(() => {
    let isMounted = true;

    const fetchPages = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/page-contents', {
          params: {
            page: pagination.currentPage,
            per_page: pagination.perPage,
            search: searchQuery
          }
        });

        if (!isMounted) return;

        // Log response for debugging
        console.log('Pages API Response:', response.data);

        // Extract data from response - handle array response directly
        const pagesData = Array.isArray(response.data) ? response.data : [];

        // Transform pages data with error handling
        const transformedPages = pagesData.map(page => {
          try {
            return {
              id: page?.id?.toString() || '',
              title: page?.title || 'Untitled',
              slug: page?.slug || '',
              description: page?.description || '',
              status: page?.status || 'draft',
              type: page?.type || 'page',
              created_at: page?.created_at || new Date().toISOString(),
              updated_at: page?.updated_at || new Date().toISOString()
            };
          } catch (error) {
            console.error('Error transforming page data:', error, page);
            return null;
          }
        }).filter(Boolean);

        if (isMounted) {
          setPages(transformedPages);
          
          // Update pagination
          setPagination(prev => ({
            ...prev,
            total: transformedPages.length,
            lastPage: Math.ceil(transformedPages.length / prev.perPage)
          }));

          // Calculate stats
          const statsUpdate = {
            total: transformedPages.length,
            published: transformedPages.filter(page => page.status === 'published').length,
            draft: transformedPages.filter(page => page.status === 'draft').length,
            archived: transformedPages.filter(page => page.status === 'archived').length
          };

          setStats(statsUpdate);
        }
      } catch (error) {
        console.error('Error fetching pages:', error);
        if (isMounted) {
          toast.error(error.response?.data?.message || 'Failed to fetch pages. Please try again.');
          setPages([]);
          setPagination(prev => ({
            ...prev,
            total: 0,
            lastPage: 1
          }));
          setStats({
            total: 0,
            published: 0,
            draft: 0,
            archived: 0
          });
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    const debounceTimer = setTimeout(() => {
      fetchPages();
    }, 300);

    return () => {
      isMounted = false;
      clearTimeout(debounceTimer);
    };
  }, [pagination.currentPage, pagination.perPage, searchQuery]);

  const handlePageChange = (page) => {
    setPagination(prev => ({
      ...prev,
      currentPage: page
    }));
  };

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    setPagination(prev => ({
      ...prev,
      currentPage: 1
    }));
  };

  return (
    <div className="page-content">
      <div className="page-header mb-4">
        <h1>Pages</h1>
      </div>

      {/* Stats Cards */}
      <div className="row mb-4">
        {['total', 'published', 'draft', 'archived'].map((statKey) => (
          <div key={statKey} className="col-md-6 col-xl-3">
            <div className="card">
              <div className="card-body">
                <div className="d-flex align-items-center">
                  <div className={`avatar-md bg-${
                    statKey === 'total' ? 'primary' :
                    statKey === 'published' ? 'success' :
                    statKey === 'draft' ? 'warning' :
                    'secondary'
                  } bg-opacity-10 rounded`}>
                    <i className={`ri-${
                      statKey === 'total' ? 'file-list' :
                      statKey === 'published' ? 'check' :
                      statKey === 'draft' ? 'draft' :
                      'archive'
                    }-line fs-24 text-${
                      statKey === 'total' ? 'primary' :
                      statKey === 'published' ? 'success' :
                      statKey === 'draft' ? 'warning' :
                      'secondary'
                    }`}></i>
                  </div>
                  <div className="ms-3">
                    <h4 className="mb-1">{
                      statKey === 'total' ? 'Total Pages' :
                      statKey.charAt(0).toUpperCase() + statKey.slice(1)
                    }</h4>
                    <p className="fs-18 mb-0">{stats[statKey]}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pages Table */}
      <div className="card">
        <div className="card-header d-flex justify-content-between align-items-center">
          <h5 className="card-title mb-0">All Pages</h5>
          <div className="d-flex gap-2">
            <input
              type="text"
              className="form-control"
              placeholder="Search pages..."
              style={{ width: "200px" }}
              value={searchQuery}
              onChange={handleSearch}
            />
            <button className="btn btn-primary">
              <i className="ri-add-line me-1"></i>
              Add Page
            </button>
          </div>
        </div>
        <div className="card-body">
          <div className="table-responsive">
            <table className="table table-hover">
              <thead className="bg-light">
                <tr>
                  <th>ID</th>
                  <th>Title</th>
                  <th>Slug</th>
                  <th>Author</th>
                  <th>Status</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="7" className="text-center py-4">
                      <Spinner />
                    </td>
                  </tr>
                ) : pages.length === 0 ? (
                  <tr>
                    <td colSpan="7" className="text-center py-4">
                      No pages found
                    </td>
                  </tr>
                ) : (
                  pages.map((page) => (
                    <tr key={page.id}>
                      <td>#{page.id}</td>
                      <td>{page.title}</td>
                      <td>{page.slug}</td>
                      <td>{page.author}</td>
                      <td>
                        <span className={`badge bg-${
                          page.status === 'published' ? 'success' :
                          page.status === 'draft' ? 'warning' :
                          'secondary'
                        }`}>
                          {page.status}
                        </span>
                      </td>
                      <td>{new Date(page.created_at).toLocaleDateString()}</td>
                      <td>
                        <button className="btn btn-sm btn-light me-2" title="View">
                          <i className="ri-eye-line"></i>
                        </button>
                        <button className="btn btn-sm btn-light me-2" title="Edit">
                          <i className="ri-edit-line"></i>
                        </button>
                        <button className="btn btn-sm btn-light" title="Delete">
                          <i className="ri-delete-bin-line"></i>
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {!loading && pages.length > 0 && (
            <div className="d-flex justify-content-between align-items-center mt-4">
              <div>
                Showing {((pagination.currentPage - 1) * pagination.perPage) + 1} to {Math.min(pagination.currentPage * pagination.perPage, pagination.total)} of {pagination.total} entries
              </div>
              <div className="d-flex gap-2">
                <button
                  className="btn btn-light"
                  onClick={() => handlePageChange(pagination.currentPage - 1)}
                  disabled={pagination.currentPage === 1}
                >
                  Previous
                </button>
                {Array.from({ length: pagination.lastPage }, (_, i) => i + 1)
                  .filter(page => {
                    const current = pagination.currentPage;
                    return page === 1 || 
                           page === pagination.lastPage || 
                           (page >= current - 1 && page <= current + 1);
                  })
                  .map((page, index, array) => (
                    <React.Fragment key={page}>
                      {index > 0 && array[index - 1] !== page - 1 && (
                        <span className="btn btn-light disabled">...</span>
                      )}
                      <button
                        className={`btn ${pagination.currentPage === page ? 'btn-primary' : 'btn-light'}`}
                        onClick={() => handlePageChange(page)}
                      >
                        {page}
                      </button>
                    </React.Fragment>
                  ))}
                <button
                  className="btn btn-light"
                  onClick={() => handlePageChange(pagination.currentPage + 1)}
                  disabled={pagination.currentPage === pagination.lastPage}
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Pages; 