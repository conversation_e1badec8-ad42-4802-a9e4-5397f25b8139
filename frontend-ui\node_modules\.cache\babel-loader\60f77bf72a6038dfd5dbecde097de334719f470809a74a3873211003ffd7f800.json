{"ast": null, "code": "'use strict';\n\n// we can't use just `it == null` since of `document.all` special case\n// https://tc39.es/ecma262/#sec-IsHTMLDDA-internal-slot-aec\nmodule.exports = function (it) {\n  return it === null || it === undefined;\n};", "map": {"version": 3, "names": ["module", "exports", "it", "undefined"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/core-js-pure/internals/is-null-or-undefined.js"], "sourcesContent": ["'use strict';\n// we can't use just `it == null` since of `document.all` special case\n// https://tc39.es/ecma262/#sec-IsHTMLDDA-internal-slot-aec\nmodule.exports = function (it) {\n  return it === null || it === undefined;\n};\n"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,UAAUC,EAAE,EAAE;EAC7B,OAAOA,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAKC,SAAS;AACxC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}