import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import axios from '../../utils/axios';
import { toast } from 'react-toastify';

function BookForm() {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState([]);
  const [formData, setFormData] = useState({
    Title: '',
    Author: '',
    Description: '',
    CategoryID: '',
    Price: '',
    StockQuantity: '',
    Status: 'active',
    Image: null
  });
  const [errors, setErrors] = useState({});

  // Fetch book data if editing
  useEffect(() => {
    const fetchBook = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const response = await axios.get(`/books/${id}`);
        
        if (response.data?.success) {
          const book = response.data.data;
          setFormData({
            Title: book.Title || book.title || '',
            Author: book.Author || book.author || '',
            Description: book.Description || book.description || '',
            CategoryID: book.CategoryID || book.category_id || (book.category?.id || book.category?.CategoryID || ''),
            Price: book.Price || book.price || '',
            StockQuantity: book.StockQuantity || book.stock_quantity || book.stock || '',
            Status: (book.Status || book.status || 'active').toLowerCase(),
            Image: null // Reset image since we can't load the existing one
          });
        } else {
          toast.error('Failed to fetch book details');
          navigate('/books');
        }
      } catch (error) {
        console.error('Error fetching book:', error);
        toast.error('Failed to fetch book details');
        navigate('/books');
      } finally {
        setLoading(false);
      }
    };

    fetchBook();
  }, [id, navigate]);

  // Fetch categories for dropdown
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await axios.get('/categories');
        const categoriesData = response.data?.data?.data || response.data?.data || [];
        setCategories(categoriesData);
      } catch (error) {
        console.error('Error fetching categories:', error);
        toast.error('Failed to load categories');
      }
    };

    fetchCategories();
  }, []);

  // Form validation
  const validateForm = () => {
    const newErrors = {};

    if (!formData.Title.trim()) newErrors.Title = "Title is required";
    if (!formData.Author.trim()) newErrors.Author = "Author is required";
    if (!formData.Price) newErrors.Price = "Price is required";
    else if (isNaN(formData.Price) || parseFloat(formData.Price) < 0) {
      newErrors.Price = "Price must be a positive number";
    }
    if (!formData.CategoryID) newErrors.CategoryID = "Category is required";
    if (formData.StockQuantity && (isNaN(formData.StockQuantity) || parseInt(formData.StockQuantity) < 0)) {
      newErrors.StockQuantity = "Stock quantity must be a non-negative integer";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      // Prepare data
      const processedData = {
        Title: formData.Title.trim(),
        Author: formData.Author.trim(),
        Description: formData.Description.trim() || null,
        CategoryID: parseInt(formData.CategoryID),
        Price: parseFloat(formData.Price),
        StockQuantity: parseInt(formData.StockQuantity) || 0,
        Status: formData.Status.toLowerCase()
      };

      if (formData.Image instanceof File) {
        processedData.Image = formData.Image;
      }

      console.log('Submitting book data:', processedData);

      // Submit to API
      const response = id
        ? await axios.put(`/books/${id}`, processedData)
        : await axios.post('/books', processedData);

      console.log('API Response:', response.data);

      if (response.data.success) {
        toast.success(id ? 'Book updated successfully' : 'Book created successfully');
        navigate('/books');
      } else {
        // Handle validation errors
        if (response.data.errors) {
          const errorMessages = Object.values(response.data.errors).flat();
          errorMessages.forEach(message => toast.error(message));
        } else {
          toast.error(response.data.message || `Failed to ${id ? 'update' : 'create'} book`);
        }
      }
    } catch (error) {
      console.error('Error saving book:', error);
      
      // Handle Laravel validation errors
      if (error.response?.status === 422 && error.response?.data?.errors) {
        const validationErrors = error.response.data.errors;
        Object.values(validationErrors).flat().forEach(message => {
          toast.error(message);
        });
      } else {
        const errorMessage = error.response?.data?.message 
          || error.response?.data?.error 
          || `Failed to ${id ? 'update' : 'create'} book. Please try again.`;
        toast.error(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  if (loading && id) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      <div className="row">
        <div className="col-12">
          <div className="card">
            <div className="card-header">
              <h5 className="card-title mb-0">{id ? 'Edit Book' : 'Add New Book'}</h5>
            </div>
            <div className="card-body">
              <form onSubmit={handleSubmit}>
                <div className="row">
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Title</label>
                    <input
                      type="text"
                      className={`form-control ${errors.Title ? 'is-invalid' : ''}`}
                      name="Title"
                      value={formData.Title}
                      onChange={(e) => setFormData(prev => ({ ...prev, Title: e.target.value }))}
                      required
                    />
                    {errors.Title && <div className="invalid-feedback">{errors.Title}</div>}
                  </div>

                  <div className="col-md-6 mb-3">
                    <label className="form-label">Author</label>
                    <input
                      type="text"
                      className={`form-control ${errors.Author ? 'is-invalid' : ''}`}
                      name="Author"
                      value={formData.Author}
                      onChange={(e) => setFormData(prev => ({ ...prev, Author: e.target.value }))}
                      required
                    />
                    {errors.Author && <div className="invalid-feedback">{errors.Author}</div>}
                  </div>
                </div>

                <div className="mb-3">
                  <label className="form-label">Description</label>
                  <textarea
                    className="form-control"
                    name="Description"
                    rows="3"
                    value={formData.Description}
                    onChange={(e) => setFormData(prev => ({ ...prev, Description: e.target.value }))}
                  ></textarea>
                </div>

                <div className="row">
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Category</label>
                    <select
                      className={`form-select ${errors.CategoryID ? 'is-invalid' : ''}`}
                      name="CategoryID"
                      value={formData.CategoryID}
                      onChange={(e) => setFormData(prev => ({ ...prev, CategoryID: e.target.value }))}
                      required
                    >
                      <option value="">Select Category</option>
                      {categories.map(category => (
                        <option key={category.id || category.CategoryID} value={category.id || category.CategoryID}>
                          {category.name || category.Name}
                        </option>
                      ))}
                    </select>
                    {errors.CategoryID && <div className="invalid-feedback">{errors.CategoryID}</div>}
                  </div>

                  <div className="col-md-3 mb-3">
                    <label className="form-label">Price</label>
                    <div className="input-group">
                      <span className="input-group-text">$</span>
                      <input
                        type="number"
                        className={`form-control ${errors.Price ? 'is-invalid' : ''}`}
                        name="Price"
                        value={formData.Price}
                        onChange={(e) => setFormData(prev => ({ ...prev, Price: e.target.value }))}
                        step="0.01"
                        min="0"
                        required
                      />
                      {errors.Price && <div className="invalid-feedback">{errors.Price}</div>}
                    </div>
                  </div>

                  <div className="col-md-3 mb-3">
                    <label className="form-label">Stock</label>
                    <input
                      type="number"
                      className={`form-control ${errors.StockQuantity ? 'is-invalid' : ''}`}
                      name="StockQuantity"
                      value={formData.StockQuantity}
                      onChange={(e) => setFormData(prev => ({ ...prev, StockQuantity: e.target.value }))}
                      min="0"
                      required
                    />
                    {errors.StockQuantity && <div className="invalid-feedback">{errors.StockQuantity}</div>}
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Status</label>
                    <select
                      className="form-select"
                      name="Status"
                      value={formData.Status}
                      onChange={(e) => setFormData(prev => ({ ...prev, Status: e.target.value.toLowerCase() }))}
                    >
                      <option value="active">Active</option>
                      <option value="draft">Draft</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  </div>

                  <div className="col-md-6 mb-3">
                    <label className="form-label">Book Cover Image</label>
                    <input
                      type="file"
                      className="form-control"
                      name="Image"
                      accept="image/*"
                      onChange={(e) => setFormData(prev => ({ ...prev, Image: e.target.files[0] }))}
                    />
                    <small className="text-muted">Max file size: 2MB</small>
                  </div>
                </div>

                <div className="text-end">
                  <button
                    type="button"
                    className="btn btn-secondary me-2"
                    onClick={() => navigate('/books')}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                        {id ? 'Updating...' : 'Saving...'}
                      </>
                    ) : (
                      id ? 'Update Book' : 'Save Book'
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default BookForm; 