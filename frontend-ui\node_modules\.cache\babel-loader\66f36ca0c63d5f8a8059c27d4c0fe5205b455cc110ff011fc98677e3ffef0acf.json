{"ast": null, "code": "import { g as getDocument } from '../shared/ssr-window.esm.mjs';\nimport { a as setCSSProperty, e as elementChildren, s as setInnerHTML, c as createElement } from '../shared/utils.mjs';\nfunction Virtual(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  extendParams({\n    virtual: {\n      enabled: false,\n      slides: [],\n      cache: true,\n      renderSlide: null,\n      renderExternal: null,\n      renderExternalUpdate: true,\n      addSlidesBefore: 0,\n      addSlidesAfter: 0\n    }\n  });\n  let cssModeTimeout;\n  const document = getDocument();\n  swiper.virtual = {\n    cache: {},\n    from: undefined,\n    to: undefined,\n    slides: [],\n    offset: 0,\n    slidesGrid: []\n  };\n  const tempDOM = document.createElement('div');\n  function renderSlide(slide, index) {\n    const params = swiper.params.virtual;\n    if (params.cache && swiper.virtual.cache[index]) {\n      return swiper.virtual.cache[index];\n    }\n    // eslint-disable-next-line\n    let slideEl;\n    if (params.renderSlide) {\n      slideEl = params.renderSlide.call(swiper, slide, index);\n      if (typeof slideEl === 'string') {\n        setInnerHTML(tempDOM, slideEl);\n        slideEl = tempDOM.children[0];\n      }\n    } else if (swiper.isElement) {\n      slideEl = createElement('swiper-slide');\n    } else {\n      slideEl = createElement('div', swiper.params.slideClass);\n    }\n    slideEl.setAttribute('data-swiper-slide-index', index);\n    if (!params.renderSlide) {\n      setInnerHTML(slideEl, slide);\n    }\n    if (params.cache) {\n      swiper.virtual.cache[index] = slideEl;\n    }\n    return slideEl;\n  }\n  function update(force, beforeInit, forceActiveIndex) {\n    const {\n      slidesPerView,\n      slidesPerGroup,\n      centeredSlides,\n      loop: isLoop,\n      initialSlide\n    } = swiper.params;\n    if (beforeInit && !isLoop && initialSlide > 0) {\n      return;\n    }\n    const {\n      addSlidesBefore,\n      addSlidesAfter\n    } = swiper.params.virtual;\n    const {\n      from: previousFrom,\n      to: previousTo,\n      slides,\n      slidesGrid: previousSlidesGrid,\n      offset: previousOffset\n    } = swiper.virtual;\n    if (!swiper.params.cssMode) {\n      swiper.updateActiveIndex();\n    }\n    const activeIndex = typeof forceActiveIndex === 'undefined' ? swiper.activeIndex || 0 : forceActiveIndex;\n    let offsetProp;\n    if (swiper.rtlTranslate) offsetProp = 'right';else offsetProp = swiper.isHorizontal() ? 'left' : 'top';\n    let slidesAfter;\n    let slidesBefore;\n    if (centeredSlides) {\n      slidesAfter = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesAfter;\n      slidesBefore = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesBefore;\n    } else {\n      slidesAfter = slidesPerView + (slidesPerGroup - 1) + addSlidesAfter;\n      slidesBefore = (isLoop ? slidesPerView : slidesPerGroup) + addSlidesBefore;\n    }\n    let from = activeIndex - slidesBefore;\n    let to = activeIndex + slidesAfter;\n    if (!isLoop) {\n      from = Math.max(from, 0);\n      to = Math.min(to, slides.length - 1);\n    }\n    let offset = (swiper.slidesGrid[from] || 0) - (swiper.slidesGrid[0] || 0);\n    if (isLoop && activeIndex >= slidesBefore) {\n      from -= slidesBefore;\n      if (!centeredSlides) offset += swiper.slidesGrid[0];\n    } else if (isLoop && activeIndex < slidesBefore) {\n      from = -slidesBefore;\n      if (centeredSlides) offset += swiper.slidesGrid[0];\n    }\n    Object.assign(swiper.virtual, {\n      from,\n      to,\n      offset,\n      slidesGrid: swiper.slidesGrid,\n      slidesBefore,\n      slidesAfter\n    });\n    function onRendered() {\n      swiper.updateSlides();\n      swiper.updateProgress();\n      swiper.updateSlidesClasses();\n      emit('virtualUpdate');\n    }\n    if (previousFrom === from && previousTo === to && !force) {\n      if (swiper.slidesGrid !== previousSlidesGrid && offset !== previousOffset) {\n        swiper.slides.forEach(slideEl => {\n          slideEl.style[offsetProp] = `${offset - Math.abs(swiper.cssOverflowAdjustment())}px`;\n        });\n      }\n      swiper.updateProgress();\n      emit('virtualUpdate');\n      return;\n    }\n    if (swiper.params.virtual.renderExternal) {\n      swiper.params.virtual.renderExternal.call(swiper, {\n        offset,\n        from,\n        to,\n        slides: function getSlides() {\n          const slidesToRender = [];\n          for (let i = from; i <= to; i += 1) {\n            slidesToRender.push(slides[i]);\n          }\n          return slidesToRender;\n        }()\n      });\n      if (swiper.params.virtual.renderExternalUpdate) {\n        onRendered();\n      } else {\n        emit('virtualUpdate');\n      }\n      return;\n    }\n    const prependIndexes = [];\n    const appendIndexes = [];\n    const getSlideIndex = index => {\n      let slideIndex = index;\n      if (index < 0) {\n        slideIndex = slides.length + index;\n      } else if (slideIndex >= slides.length) {\n        // eslint-disable-next-line\n        slideIndex = slideIndex - slides.length;\n      }\n      return slideIndex;\n    };\n    if (force) {\n      swiper.slides.filter(el => el.matches(`.${swiper.params.slideClass}, swiper-slide`)).forEach(slideEl => {\n        slideEl.remove();\n      });\n    } else {\n      for (let i = previousFrom; i <= previousTo; i += 1) {\n        if (i < from || i > to) {\n          const slideIndex = getSlideIndex(i);\n          swiper.slides.filter(el => el.matches(`.${swiper.params.slideClass}[data-swiper-slide-index=\"${slideIndex}\"], swiper-slide[data-swiper-slide-index=\"${slideIndex}\"]`)).forEach(slideEl => {\n            slideEl.remove();\n          });\n        }\n      }\n    }\n    const loopFrom = isLoop ? -slides.length : 0;\n    const loopTo = isLoop ? slides.length * 2 : slides.length;\n    for (let i = loopFrom; i < loopTo; i += 1) {\n      if (i >= from && i <= to) {\n        const slideIndex = getSlideIndex(i);\n        if (typeof previousTo === 'undefined' || force) {\n          appendIndexes.push(slideIndex);\n        } else {\n          if (i > previousTo) appendIndexes.push(slideIndex);\n          if (i < previousFrom) prependIndexes.push(slideIndex);\n        }\n      }\n    }\n    appendIndexes.forEach(index => {\n      swiper.slidesEl.append(renderSlide(slides[index], index));\n    });\n    if (isLoop) {\n      for (let i = prependIndexes.length - 1; i >= 0; i -= 1) {\n        const index = prependIndexes[i];\n        swiper.slidesEl.prepend(renderSlide(slides[index], index));\n      }\n    } else {\n      prependIndexes.sort((a, b) => b - a);\n      prependIndexes.forEach(index => {\n        swiper.slidesEl.prepend(renderSlide(slides[index], index));\n      });\n    }\n    elementChildren(swiper.slidesEl, '.swiper-slide, swiper-slide').forEach(slideEl => {\n      slideEl.style[offsetProp] = `${offset - Math.abs(swiper.cssOverflowAdjustment())}px`;\n    });\n    onRendered();\n  }\n  function appendSlide(slides) {\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.push(slides[i]);\n      }\n    } else {\n      swiper.virtual.slides.push(slides);\n    }\n    update(true);\n  }\n  function prependSlide(slides) {\n    const activeIndex = swiper.activeIndex;\n    let newActiveIndex = activeIndex + 1;\n    let numberOfNewSlides = 1;\n    if (Array.isArray(slides)) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.unshift(slides[i]);\n      }\n      newActiveIndex = activeIndex + slides.length;\n      numberOfNewSlides = slides.length;\n    } else {\n      swiper.virtual.slides.unshift(slides);\n    }\n    if (swiper.params.virtual.cache) {\n      const cache = swiper.virtual.cache;\n      const newCache = {};\n      Object.keys(cache).forEach(cachedIndex => {\n        const cachedEl = cache[cachedIndex];\n        const cachedElIndex = cachedEl.getAttribute('data-swiper-slide-index');\n        if (cachedElIndex) {\n          cachedEl.setAttribute('data-swiper-slide-index', parseInt(cachedElIndex, 10) + numberOfNewSlides);\n        }\n        newCache[parseInt(cachedIndex, 10) + numberOfNewSlides] = cachedEl;\n      });\n      swiper.virtual.cache = newCache;\n    }\n    update(true);\n    swiper.slideTo(newActiveIndex, 0);\n  }\n  function removeSlide(slidesIndexes) {\n    if (typeof slidesIndexes === 'undefined' || slidesIndexes === null) return;\n    let activeIndex = swiper.activeIndex;\n    if (Array.isArray(slidesIndexes)) {\n      for (let i = slidesIndexes.length - 1; i >= 0; i -= 1) {\n        if (swiper.params.virtual.cache) {\n          delete swiper.virtual.cache[slidesIndexes[i]];\n          // shift cache indexes\n          Object.keys(swiper.virtual.cache).forEach(key => {\n            if (key > slidesIndexes) {\n              swiper.virtual.cache[key - 1] = swiper.virtual.cache[key];\n              swiper.virtual.cache[key - 1].setAttribute('data-swiper-slide-index', key - 1);\n              delete swiper.virtual.cache[key];\n            }\n          });\n        }\n        swiper.virtual.slides.splice(slidesIndexes[i], 1);\n        if (slidesIndexes[i] < activeIndex) activeIndex -= 1;\n        activeIndex = Math.max(activeIndex, 0);\n      }\n    } else {\n      if (swiper.params.virtual.cache) {\n        delete swiper.virtual.cache[slidesIndexes];\n        // shift cache indexes\n        Object.keys(swiper.virtual.cache).forEach(key => {\n          if (key > slidesIndexes) {\n            swiper.virtual.cache[key - 1] = swiper.virtual.cache[key];\n            swiper.virtual.cache[key - 1].setAttribute('data-swiper-slide-index', key - 1);\n            delete swiper.virtual.cache[key];\n          }\n        });\n      }\n      swiper.virtual.slides.splice(slidesIndexes, 1);\n      if (slidesIndexes < activeIndex) activeIndex -= 1;\n      activeIndex = Math.max(activeIndex, 0);\n    }\n    update(true);\n    swiper.slideTo(activeIndex, 0);\n  }\n  function removeAllSlides() {\n    swiper.virtual.slides = [];\n    if (swiper.params.virtual.cache) {\n      swiper.virtual.cache = {};\n    }\n    update(true);\n    swiper.slideTo(0, 0);\n  }\n  on('beforeInit', () => {\n    if (!swiper.params.virtual.enabled) return;\n    let domSlidesAssigned;\n    if (typeof swiper.passedParams.virtual.slides === 'undefined') {\n      const slides = [...swiper.slidesEl.children].filter(el => el.matches(`.${swiper.params.slideClass}, swiper-slide`));\n      if (slides && slides.length) {\n        swiper.virtual.slides = [...slides];\n        domSlidesAssigned = true;\n        slides.forEach((slideEl, slideIndex) => {\n          slideEl.setAttribute('data-swiper-slide-index', slideIndex);\n          swiper.virtual.cache[slideIndex] = slideEl;\n          slideEl.remove();\n        });\n      }\n    }\n    if (!domSlidesAssigned) {\n      swiper.virtual.slides = swiper.params.virtual.slides;\n    }\n    swiper.classNames.push(`${swiper.params.containerModifierClass}virtual`);\n    swiper.params.watchSlidesProgress = true;\n    swiper.originalParams.watchSlidesProgress = true;\n    update(false, true);\n  });\n  on('setTranslate', () => {\n    if (!swiper.params.virtual.enabled) return;\n    if (swiper.params.cssMode && !swiper._immediateVirtual) {\n      clearTimeout(cssModeTimeout);\n      cssModeTimeout = setTimeout(() => {\n        update();\n      }, 100);\n    } else {\n      update();\n    }\n  });\n  on('init update resize', () => {\n    if (!swiper.params.virtual.enabled) return;\n    if (swiper.params.cssMode) {\n      setCSSProperty(swiper.wrapperEl, '--swiper-virtual-size', `${swiper.virtualSize}px`);\n    }\n  });\n  Object.assign(swiper.virtual, {\n    appendSlide,\n    prependSlide,\n    removeSlide,\n    removeAllSlides,\n    update\n  });\n}\nexport { Virtual as default };", "map": {"version": 3, "names": ["g", "getDocument", "a", "setCSSProperty", "e", "elementChildren", "s", "setInnerHTML", "c", "createElement", "Virtual", "_ref", "swiper", "extendParams", "on", "emit", "virtual", "enabled", "slides", "cache", "renderSlide", "renderExternal", "renderExternalUpdate", "addSlidesBefore", "addSlidesAfter", "cssModeTimeout", "document", "from", "undefined", "to", "offset", "slidesGrid", "tempDOM", "slide", "index", "params", "slideEl", "call", "children", "isElement", "slideClass", "setAttribute", "update", "force", "beforeInit", "forceActiveIndex", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerGroup", "centeredSlides", "loop", "isLoop", "initialSlide", "previousFrom", "previousTo", "previousSlidesGrid", "previousOffset", "cssMode", "updateActiveIndex", "activeIndex", "offsetProp", "rtlTranslate", "isHorizontal", "slidesAfter", "slidesBefore", "Math", "floor", "max", "min", "length", "Object", "assign", "onRendered", "updateSlides", "updateProgress", "updateSlidesClasses", "for<PERSON>ach", "style", "abs", "cssOverflowAdjustment", "getSlides", "slidesToRender", "i", "push", "prependIndexes", "appendIndexes", "getSlideIndex", "slideIndex", "filter", "el", "matches", "remove", "loopFrom", "loopTo", "slidesEl", "append", "prepend", "sort", "b", "appendSlide", "prependSlide", "newActiveIndex", "numberOfNewSlides", "Array", "isArray", "unshift", "newCache", "keys", "cachedIndex", "cachedEl", "cachedElIndex", "getAttribute", "parseInt", "slideTo", "removeSlide", "slidesIndexes", "key", "splice", "removeAllSlides", "domSlidesAssigned", "passedParams", "classNames", "containerModifierClass", "watchSlidesProgress", "originalParams", "_immediateVirtual", "clearTimeout", "setTimeout", "wrapperEl", "virtualSize", "default"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/swiper/modules/virtual.mjs"], "sourcesContent": ["import { g as getDocument } from '../shared/ssr-window.esm.mjs';\nimport { a as setCSSProperty, e as elementChildren, s as setInnerHTML, c as createElement } from '../shared/utils.mjs';\n\nfunction Virtual(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  extendParams({\n    virtual: {\n      enabled: false,\n      slides: [],\n      cache: true,\n      renderSlide: null,\n      renderExternal: null,\n      renderExternalUpdate: true,\n      addSlidesBefore: 0,\n      addSlidesAfter: 0\n    }\n  });\n  let cssModeTimeout;\n  const document = getDocument();\n  swiper.virtual = {\n    cache: {},\n    from: undefined,\n    to: undefined,\n    slides: [],\n    offset: 0,\n    slidesGrid: []\n  };\n  const tempDOM = document.createElement('div');\n  function renderSlide(slide, index) {\n    const params = swiper.params.virtual;\n    if (params.cache && swiper.virtual.cache[index]) {\n      return swiper.virtual.cache[index];\n    }\n    // eslint-disable-next-line\n    let slideEl;\n    if (params.renderSlide) {\n      slideEl = params.renderSlide.call(swiper, slide, index);\n      if (typeof slideEl === 'string') {\n        setInnerHTML(tempDOM, slideEl);\n        slideEl = tempDOM.children[0];\n      }\n    } else if (swiper.isElement) {\n      slideEl = createElement('swiper-slide');\n    } else {\n      slideEl = createElement('div', swiper.params.slideClass);\n    }\n    slideEl.setAttribute('data-swiper-slide-index', index);\n    if (!params.renderSlide) {\n      setInnerHTML(slideEl, slide);\n    }\n    if (params.cache) {\n      swiper.virtual.cache[index] = slideEl;\n    }\n    return slideEl;\n  }\n  function update(force, beforeInit, forceActiveIndex) {\n    const {\n      slidesPerView,\n      slidesPerGroup,\n      centeredSlides,\n      loop: isLoop,\n      initialSlide\n    } = swiper.params;\n    if (beforeInit && !isLoop && initialSlide > 0) {\n      return;\n    }\n    const {\n      addSlidesBefore,\n      addSlidesAfter\n    } = swiper.params.virtual;\n    const {\n      from: previousFrom,\n      to: previousTo,\n      slides,\n      slidesGrid: previousSlidesGrid,\n      offset: previousOffset\n    } = swiper.virtual;\n    if (!swiper.params.cssMode) {\n      swiper.updateActiveIndex();\n    }\n    const activeIndex = typeof forceActiveIndex === 'undefined' ? swiper.activeIndex || 0 : forceActiveIndex;\n    let offsetProp;\n    if (swiper.rtlTranslate) offsetProp = 'right';else offsetProp = swiper.isHorizontal() ? 'left' : 'top';\n    let slidesAfter;\n    let slidesBefore;\n    if (centeredSlides) {\n      slidesAfter = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesAfter;\n      slidesBefore = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesBefore;\n    } else {\n      slidesAfter = slidesPerView + (slidesPerGroup - 1) + addSlidesAfter;\n      slidesBefore = (isLoop ? slidesPerView : slidesPerGroup) + addSlidesBefore;\n    }\n    let from = activeIndex - slidesBefore;\n    let to = activeIndex + slidesAfter;\n    if (!isLoop) {\n      from = Math.max(from, 0);\n      to = Math.min(to, slides.length - 1);\n    }\n    let offset = (swiper.slidesGrid[from] || 0) - (swiper.slidesGrid[0] || 0);\n    if (isLoop && activeIndex >= slidesBefore) {\n      from -= slidesBefore;\n      if (!centeredSlides) offset += swiper.slidesGrid[0];\n    } else if (isLoop && activeIndex < slidesBefore) {\n      from = -slidesBefore;\n      if (centeredSlides) offset += swiper.slidesGrid[0];\n    }\n    Object.assign(swiper.virtual, {\n      from,\n      to,\n      offset,\n      slidesGrid: swiper.slidesGrid,\n      slidesBefore,\n      slidesAfter\n    });\n    function onRendered() {\n      swiper.updateSlides();\n      swiper.updateProgress();\n      swiper.updateSlidesClasses();\n      emit('virtualUpdate');\n    }\n    if (previousFrom === from && previousTo === to && !force) {\n      if (swiper.slidesGrid !== previousSlidesGrid && offset !== previousOffset) {\n        swiper.slides.forEach(slideEl => {\n          slideEl.style[offsetProp] = `${offset - Math.abs(swiper.cssOverflowAdjustment())}px`;\n        });\n      }\n      swiper.updateProgress();\n      emit('virtualUpdate');\n      return;\n    }\n    if (swiper.params.virtual.renderExternal) {\n      swiper.params.virtual.renderExternal.call(swiper, {\n        offset,\n        from,\n        to,\n        slides: function getSlides() {\n          const slidesToRender = [];\n          for (let i = from; i <= to; i += 1) {\n            slidesToRender.push(slides[i]);\n          }\n          return slidesToRender;\n        }()\n      });\n      if (swiper.params.virtual.renderExternalUpdate) {\n        onRendered();\n      } else {\n        emit('virtualUpdate');\n      }\n      return;\n    }\n    const prependIndexes = [];\n    const appendIndexes = [];\n    const getSlideIndex = index => {\n      let slideIndex = index;\n      if (index < 0) {\n        slideIndex = slides.length + index;\n      } else if (slideIndex >= slides.length) {\n        // eslint-disable-next-line\n        slideIndex = slideIndex - slides.length;\n      }\n      return slideIndex;\n    };\n    if (force) {\n      swiper.slides.filter(el => el.matches(`.${swiper.params.slideClass}, swiper-slide`)).forEach(slideEl => {\n        slideEl.remove();\n      });\n    } else {\n      for (let i = previousFrom; i <= previousTo; i += 1) {\n        if (i < from || i > to) {\n          const slideIndex = getSlideIndex(i);\n          swiper.slides.filter(el => el.matches(`.${swiper.params.slideClass}[data-swiper-slide-index=\"${slideIndex}\"], swiper-slide[data-swiper-slide-index=\"${slideIndex}\"]`)).forEach(slideEl => {\n            slideEl.remove();\n          });\n        }\n      }\n    }\n    const loopFrom = isLoop ? -slides.length : 0;\n    const loopTo = isLoop ? slides.length * 2 : slides.length;\n    for (let i = loopFrom; i < loopTo; i += 1) {\n      if (i >= from && i <= to) {\n        const slideIndex = getSlideIndex(i);\n        if (typeof previousTo === 'undefined' || force) {\n          appendIndexes.push(slideIndex);\n        } else {\n          if (i > previousTo) appendIndexes.push(slideIndex);\n          if (i < previousFrom) prependIndexes.push(slideIndex);\n        }\n      }\n    }\n    appendIndexes.forEach(index => {\n      swiper.slidesEl.append(renderSlide(slides[index], index));\n    });\n    if (isLoop) {\n      for (let i = prependIndexes.length - 1; i >= 0; i -= 1) {\n        const index = prependIndexes[i];\n        swiper.slidesEl.prepend(renderSlide(slides[index], index));\n      }\n    } else {\n      prependIndexes.sort((a, b) => b - a);\n      prependIndexes.forEach(index => {\n        swiper.slidesEl.prepend(renderSlide(slides[index], index));\n      });\n    }\n    elementChildren(swiper.slidesEl, '.swiper-slide, swiper-slide').forEach(slideEl => {\n      slideEl.style[offsetProp] = `${offset - Math.abs(swiper.cssOverflowAdjustment())}px`;\n    });\n    onRendered();\n  }\n  function appendSlide(slides) {\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.push(slides[i]);\n      }\n    } else {\n      swiper.virtual.slides.push(slides);\n    }\n    update(true);\n  }\n  function prependSlide(slides) {\n    const activeIndex = swiper.activeIndex;\n    let newActiveIndex = activeIndex + 1;\n    let numberOfNewSlides = 1;\n    if (Array.isArray(slides)) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.unshift(slides[i]);\n      }\n      newActiveIndex = activeIndex + slides.length;\n      numberOfNewSlides = slides.length;\n    } else {\n      swiper.virtual.slides.unshift(slides);\n    }\n    if (swiper.params.virtual.cache) {\n      const cache = swiper.virtual.cache;\n      const newCache = {};\n      Object.keys(cache).forEach(cachedIndex => {\n        const cachedEl = cache[cachedIndex];\n        const cachedElIndex = cachedEl.getAttribute('data-swiper-slide-index');\n        if (cachedElIndex) {\n          cachedEl.setAttribute('data-swiper-slide-index', parseInt(cachedElIndex, 10) + numberOfNewSlides);\n        }\n        newCache[parseInt(cachedIndex, 10) + numberOfNewSlides] = cachedEl;\n      });\n      swiper.virtual.cache = newCache;\n    }\n    update(true);\n    swiper.slideTo(newActiveIndex, 0);\n  }\n  function removeSlide(slidesIndexes) {\n    if (typeof slidesIndexes === 'undefined' || slidesIndexes === null) return;\n    let activeIndex = swiper.activeIndex;\n    if (Array.isArray(slidesIndexes)) {\n      for (let i = slidesIndexes.length - 1; i >= 0; i -= 1) {\n        if (swiper.params.virtual.cache) {\n          delete swiper.virtual.cache[slidesIndexes[i]];\n          // shift cache indexes\n          Object.keys(swiper.virtual.cache).forEach(key => {\n            if (key > slidesIndexes) {\n              swiper.virtual.cache[key - 1] = swiper.virtual.cache[key];\n              swiper.virtual.cache[key - 1].setAttribute('data-swiper-slide-index', key - 1);\n              delete swiper.virtual.cache[key];\n            }\n          });\n        }\n        swiper.virtual.slides.splice(slidesIndexes[i], 1);\n        if (slidesIndexes[i] < activeIndex) activeIndex -= 1;\n        activeIndex = Math.max(activeIndex, 0);\n      }\n    } else {\n      if (swiper.params.virtual.cache) {\n        delete swiper.virtual.cache[slidesIndexes];\n        // shift cache indexes\n        Object.keys(swiper.virtual.cache).forEach(key => {\n          if (key > slidesIndexes) {\n            swiper.virtual.cache[key - 1] = swiper.virtual.cache[key];\n            swiper.virtual.cache[key - 1].setAttribute('data-swiper-slide-index', key - 1);\n            delete swiper.virtual.cache[key];\n          }\n        });\n      }\n      swiper.virtual.slides.splice(slidesIndexes, 1);\n      if (slidesIndexes < activeIndex) activeIndex -= 1;\n      activeIndex = Math.max(activeIndex, 0);\n    }\n    update(true);\n    swiper.slideTo(activeIndex, 0);\n  }\n  function removeAllSlides() {\n    swiper.virtual.slides = [];\n    if (swiper.params.virtual.cache) {\n      swiper.virtual.cache = {};\n    }\n    update(true);\n    swiper.slideTo(0, 0);\n  }\n  on('beforeInit', () => {\n    if (!swiper.params.virtual.enabled) return;\n    let domSlidesAssigned;\n    if (typeof swiper.passedParams.virtual.slides === 'undefined') {\n      const slides = [...swiper.slidesEl.children].filter(el => el.matches(`.${swiper.params.slideClass}, swiper-slide`));\n      if (slides && slides.length) {\n        swiper.virtual.slides = [...slides];\n        domSlidesAssigned = true;\n        slides.forEach((slideEl, slideIndex) => {\n          slideEl.setAttribute('data-swiper-slide-index', slideIndex);\n          swiper.virtual.cache[slideIndex] = slideEl;\n          slideEl.remove();\n        });\n      }\n    }\n    if (!domSlidesAssigned) {\n      swiper.virtual.slides = swiper.params.virtual.slides;\n    }\n    swiper.classNames.push(`${swiper.params.containerModifierClass}virtual`);\n    swiper.params.watchSlidesProgress = true;\n    swiper.originalParams.watchSlidesProgress = true;\n    update(false, true);\n  });\n  on('setTranslate', () => {\n    if (!swiper.params.virtual.enabled) return;\n    if (swiper.params.cssMode && !swiper._immediateVirtual) {\n      clearTimeout(cssModeTimeout);\n      cssModeTimeout = setTimeout(() => {\n        update();\n      }, 100);\n    } else {\n      update();\n    }\n  });\n  on('init update resize', () => {\n    if (!swiper.params.virtual.enabled) return;\n    if (swiper.params.cssMode) {\n      setCSSProperty(swiper.wrapperEl, '--swiper-virtual-size', `${swiper.virtualSize}px`);\n    }\n  });\n  Object.assign(swiper.virtual, {\n    appendSlide,\n    prependSlide,\n    removeSlide,\n    removeAllSlides,\n    update\n  });\n}\n\nexport { Virtual as default };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,WAAW,QAAQ,8BAA8B;AAC/D,SAASC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,aAAa,QAAQ,qBAAqB;AAEtH,SAASC,OAAOA,CAACC,IAAI,EAAE;EACrB,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC,EAAE;IACFC;EACF,CAAC,GAAGJ,IAAI;EACRE,YAAY,CAAC;IACXG,OAAO,EAAE;MACPC,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,IAAI;MACXC,WAAW,EAAE,IAAI;MACjBC,cAAc,EAAE,IAAI;MACpBC,oBAAoB,EAAE,IAAI;MAC1BC,eAAe,EAAE,CAAC;MAClBC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACF,IAAIC,cAAc;EAClB,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9BW,MAAM,CAACI,OAAO,GAAG;IACfG,KAAK,EAAE,CAAC,CAAC;IACTQ,IAAI,EAAEC,SAAS;IACfC,EAAE,EAAED,SAAS;IACbV,MAAM,EAAE,EAAE;IACVY,MAAM,EAAE,CAAC;IACTC,UAAU,EAAE;EACd,CAAC;EACD,MAAMC,OAAO,GAAGN,QAAQ,CAACjB,aAAa,CAAC,KAAK,CAAC;EAC7C,SAASW,WAAWA,CAACa,KAAK,EAAEC,KAAK,EAAE;IACjC,MAAMC,MAAM,GAAGvB,MAAM,CAACuB,MAAM,CAACnB,OAAO;IACpC,IAAImB,MAAM,CAAChB,KAAK,IAAIP,MAAM,CAACI,OAAO,CAACG,KAAK,CAACe,KAAK,CAAC,EAAE;MAC/C,OAAOtB,MAAM,CAACI,OAAO,CAACG,KAAK,CAACe,KAAK,CAAC;IACpC;IACA;IACA,IAAIE,OAAO;IACX,IAAID,MAAM,CAACf,WAAW,EAAE;MACtBgB,OAAO,GAAGD,MAAM,CAACf,WAAW,CAACiB,IAAI,CAACzB,MAAM,EAAEqB,KAAK,EAAEC,KAAK,CAAC;MACvD,IAAI,OAAOE,OAAO,KAAK,QAAQ,EAAE;QAC/B7B,YAAY,CAACyB,OAAO,EAAEI,OAAO,CAAC;QAC9BA,OAAO,GAAGJ,OAAO,CAACM,QAAQ,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC,MAAM,IAAI1B,MAAM,CAAC2B,SAAS,EAAE;MAC3BH,OAAO,GAAG3B,aAAa,CAAC,cAAc,CAAC;IACzC,CAAC,MAAM;MACL2B,OAAO,GAAG3B,aAAa,CAAC,KAAK,EAAEG,MAAM,CAACuB,MAAM,CAACK,UAAU,CAAC;IAC1D;IACAJ,OAAO,CAACK,YAAY,CAAC,yBAAyB,EAAEP,KAAK,CAAC;IACtD,IAAI,CAACC,MAAM,CAACf,WAAW,EAAE;MACvBb,YAAY,CAAC6B,OAAO,EAAEH,KAAK,CAAC;IAC9B;IACA,IAAIE,MAAM,CAAChB,KAAK,EAAE;MAChBP,MAAM,CAACI,OAAO,CAACG,KAAK,CAACe,KAAK,CAAC,GAAGE,OAAO;IACvC;IACA,OAAOA,OAAO;EAChB;EACA,SAASM,MAAMA,CAACC,KAAK,EAAEC,UAAU,EAAEC,gBAAgB,EAAE;IACnD,MAAM;MACJC,aAAa;MACbC,cAAc;MACdC,cAAc;MACdC,IAAI,EAAEC,MAAM;MACZC;IACF,CAAC,GAAGvC,MAAM,CAACuB,MAAM;IACjB,IAAIS,UAAU,IAAI,CAACM,MAAM,IAAIC,YAAY,GAAG,CAAC,EAAE;MAC7C;IACF;IACA,MAAM;MACJ5B,eAAe;MACfC;IACF,CAAC,GAAGZ,MAAM,CAACuB,MAAM,CAACnB,OAAO;IACzB,MAAM;MACJW,IAAI,EAAEyB,YAAY;MAClBvB,EAAE,EAAEwB,UAAU;MACdnC,MAAM;MACNa,UAAU,EAAEuB,kBAAkB;MAC9BxB,MAAM,EAAEyB;IACV,CAAC,GAAG3C,MAAM,CAACI,OAAO;IAClB,IAAI,CAACJ,MAAM,CAACuB,MAAM,CAACqB,OAAO,EAAE;MAC1B5C,MAAM,CAAC6C,iBAAiB,CAAC,CAAC;IAC5B;IACA,MAAMC,WAAW,GAAG,OAAOb,gBAAgB,KAAK,WAAW,GAAGjC,MAAM,CAAC8C,WAAW,IAAI,CAAC,GAAGb,gBAAgB;IACxG,IAAIc,UAAU;IACd,IAAI/C,MAAM,CAACgD,YAAY,EAAED,UAAU,GAAG,OAAO,CAAC,KAAKA,UAAU,GAAG/C,MAAM,CAACiD,YAAY,CAAC,CAAC,GAAG,MAAM,GAAG,KAAK;IACtG,IAAIC,WAAW;IACf,IAAIC,YAAY;IAChB,IAAIf,cAAc,EAAE;MAClBc,WAAW,GAAGE,IAAI,CAACC,KAAK,CAACnB,aAAa,GAAG,CAAC,CAAC,GAAGC,cAAc,GAAGvB,cAAc;MAC7EuC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACnB,aAAa,GAAG,CAAC,CAAC,GAAGC,cAAc,GAAGxB,eAAe;IACjF,CAAC,MAAM;MACLuC,WAAW,GAAGhB,aAAa,IAAIC,cAAc,GAAG,CAAC,CAAC,GAAGvB,cAAc;MACnEuC,YAAY,GAAG,CAACb,MAAM,GAAGJ,aAAa,GAAGC,cAAc,IAAIxB,eAAe;IAC5E;IACA,IAAII,IAAI,GAAG+B,WAAW,GAAGK,YAAY;IACrC,IAAIlC,EAAE,GAAG6B,WAAW,GAAGI,WAAW;IAClC,IAAI,CAACZ,MAAM,EAAE;MACXvB,IAAI,GAAGqC,IAAI,CAACE,GAAG,CAACvC,IAAI,EAAE,CAAC,CAAC;MACxBE,EAAE,GAAGmC,IAAI,CAACG,GAAG,CAACtC,EAAE,EAAEX,MAAM,CAACkD,MAAM,GAAG,CAAC,CAAC;IACtC;IACA,IAAItC,MAAM,GAAG,CAAClB,MAAM,CAACmB,UAAU,CAACJ,IAAI,CAAC,IAAI,CAAC,KAAKf,MAAM,CAACmB,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACzE,IAAImB,MAAM,IAAIQ,WAAW,IAAIK,YAAY,EAAE;MACzCpC,IAAI,IAAIoC,YAAY;MACpB,IAAI,CAACf,cAAc,EAAElB,MAAM,IAAIlB,MAAM,CAACmB,UAAU,CAAC,CAAC,CAAC;IACrD,CAAC,MAAM,IAAImB,MAAM,IAAIQ,WAAW,GAAGK,YAAY,EAAE;MAC/CpC,IAAI,GAAG,CAACoC,YAAY;MACpB,IAAIf,cAAc,EAAElB,MAAM,IAAIlB,MAAM,CAACmB,UAAU,CAAC,CAAC,CAAC;IACpD;IACAsC,MAAM,CAACC,MAAM,CAAC1D,MAAM,CAACI,OAAO,EAAE;MAC5BW,IAAI;MACJE,EAAE;MACFC,MAAM;MACNC,UAAU,EAAEnB,MAAM,CAACmB,UAAU;MAC7BgC,YAAY;MACZD;IACF,CAAC,CAAC;IACF,SAASS,UAAUA,CAAA,EAAG;MACpB3D,MAAM,CAAC4D,YAAY,CAAC,CAAC;MACrB5D,MAAM,CAAC6D,cAAc,CAAC,CAAC;MACvB7D,MAAM,CAAC8D,mBAAmB,CAAC,CAAC;MAC5B3D,IAAI,CAAC,eAAe,CAAC;IACvB;IACA,IAAIqC,YAAY,KAAKzB,IAAI,IAAI0B,UAAU,KAAKxB,EAAE,IAAI,CAACc,KAAK,EAAE;MACxD,IAAI/B,MAAM,CAACmB,UAAU,KAAKuB,kBAAkB,IAAIxB,MAAM,KAAKyB,cAAc,EAAE;QACzE3C,MAAM,CAACM,MAAM,CAACyD,OAAO,CAACvC,OAAO,IAAI;UAC/BA,OAAO,CAACwC,KAAK,CAACjB,UAAU,CAAC,GAAG,GAAG7B,MAAM,GAAGkC,IAAI,CAACa,GAAG,CAACjE,MAAM,CAACkE,qBAAqB,CAAC,CAAC,CAAC,IAAI;QACtF,CAAC,CAAC;MACJ;MACAlE,MAAM,CAAC6D,cAAc,CAAC,CAAC;MACvB1D,IAAI,CAAC,eAAe,CAAC;MACrB;IACF;IACA,IAAIH,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACK,cAAc,EAAE;MACxCT,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACK,cAAc,CAACgB,IAAI,CAACzB,MAAM,EAAE;QAChDkB,MAAM;QACNH,IAAI;QACJE,EAAE;QACFX,MAAM,EAAE,SAAS6D,SAASA,CAAA,EAAG;UAC3B,MAAMC,cAAc,GAAG,EAAE;UACzB,KAAK,IAAIC,CAAC,GAAGtD,IAAI,EAAEsD,CAAC,IAAIpD,EAAE,EAAEoD,CAAC,IAAI,CAAC,EAAE;YAClCD,cAAc,CAACE,IAAI,CAAChE,MAAM,CAAC+D,CAAC,CAAC,CAAC;UAChC;UACA,OAAOD,cAAc;QACvB,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,IAAIpE,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACM,oBAAoB,EAAE;QAC9CiD,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACLxD,IAAI,CAAC,eAAe,CAAC;MACvB;MACA;IACF;IACA,MAAMoE,cAAc,GAAG,EAAE;IACzB,MAAMC,aAAa,GAAG,EAAE;IACxB,MAAMC,aAAa,GAAGnD,KAAK,IAAI;MAC7B,IAAIoD,UAAU,GAAGpD,KAAK;MACtB,IAAIA,KAAK,GAAG,CAAC,EAAE;QACboD,UAAU,GAAGpE,MAAM,CAACkD,MAAM,GAAGlC,KAAK;MACpC,CAAC,MAAM,IAAIoD,UAAU,IAAIpE,MAAM,CAACkD,MAAM,EAAE;QACtC;QACAkB,UAAU,GAAGA,UAAU,GAAGpE,MAAM,CAACkD,MAAM;MACzC;MACA,OAAOkB,UAAU;IACnB,CAAC;IACD,IAAI3C,KAAK,EAAE;MACT/B,MAAM,CAACM,MAAM,CAACqE,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACC,OAAO,CAAC,IAAI7E,MAAM,CAACuB,MAAM,CAACK,UAAU,gBAAgB,CAAC,CAAC,CAACmC,OAAO,CAACvC,OAAO,IAAI;QACtGA,OAAO,CAACsD,MAAM,CAAC,CAAC;MAClB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,KAAK,IAAIT,CAAC,GAAG7B,YAAY,EAAE6B,CAAC,IAAI5B,UAAU,EAAE4B,CAAC,IAAI,CAAC,EAAE;QAClD,IAAIA,CAAC,GAAGtD,IAAI,IAAIsD,CAAC,GAAGpD,EAAE,EAAE;UACtB,MAAMyD,UAAU,GAAGD,aAAa,CAACJ,CAAC,CAAC;UACnCrE,MAAM,CAACM,MAAM,CAACqE,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACC,OAAO,CAAC,IAAI7E,MAAM,CAACuB,MAAM,CAACK,UAAU,6BAA6B8C,UAAU,6CAA6CA,UAAU,IAAI,CAAC,CAAC,CAACX,OAAO,CAACvC,OAAO,IAAI;YACxLA,OAAO,CAACsD,MAAM,CAAC,CAAC;UAClB,CAAC,CAAC;QACJ;MACF;IACF;IACA,MAAMC,QAAQ,GAAGzC,MAAM,GAAG,CAAChC,MAAM,CAACkD,MAAM,GAAG,CAAC;IAC5C,MAAMwB,MAAM,GAAG1C,MAAM,GAAGhC,MAAM,CAACkD,MAAM,GAAG,CAAC,GAAGlD,MAAM,CAACkD,MAAM;IACzD,KAAK,IAAIa,CAAC,GAAGU,QAAQ,EAAEV,CAAC,GAAGW,MAAM,EAAEX,CAAC,IAAI,CAAC,EAAE;MACzC,IAAIA,CAAC,IAAItD,IAAI,IAAIsD,CAAC,IAAIpD,EAAE,EAAE;QACxB,MAAMyD,UAAU,GAAGD,aAAa,CAACJ,CAAC,CAAC;QACnC,IAAI,OAAO5B,UAAU,KAAK,WAAW,IAAIV,KAAK,EAAE;UAC9CyC,aAAa,CAACF,IAAI,CAACI,UAAU,CAAC;QAChC,CAAC,MAAM;UACL,IAAIL,CAAC,GAAG5B,UAAU,EAAE+B,aAAa,CAACF,IAAI,CAACI,UAAU,CAAC;UAClD,IAAIL,CAAC,GAAG7B,YAAY,EAAE+B,cAAc,CAACD,IAAI,CAACI,UAAU,CAAC;QACvD;MACF;IACF;IACAF,aAAa,CAACT,OAAO,CAACzC,KAAK,IAAI;MAC7BtB,MAAM,CAACiF,QAAQ,CAACC,MAAM,CAAC1E,WAAW,CAACF,MAAM,CAACgB,KAAK,CAAC,EAAEA,KAAK,CAAC,CAAC;IAC3D,CAAC,CAAC;IACF,IAAIgB,MAAM,EAAE;MACV,KAAK,IAAI+B,CAAC,GAAGE,cAAc,CAACf,MAAM,GAAG,CAAC,EAAEa,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;QACtD,MAAM/C,KAAK,GAAGiD,cAAc,CAACF,CAAC,CAAC;QAC/BrE,MAAM,CAACiF,QAAQ,CAACE,OAAO,CAAC3E,WAAW,CAACF,MAAM,CAACgB,KAAK,CAAC,EAAEA,KAAK,CAAC,CAAC;MAC5D;IACF,CAAC,MAAM;MACLiD,cAAc,CAACa,IAAI,CAAC,CAAC9F,CAAC,EAAE+F,CAAC,KAAKA,CAAC,GAAG/F,CAAC,CAAC;MACpCiF,cAAc,CAACR,OAAO,CAACzC,KAAK,IAAI;QAC9BtB,MAAM,CAACiF,QAAQ,CAACE,OAAO,CAAC3E,WAAW,CAACF,MAAM,CAACgB,KAAK,CAAC,EAAEA,KAAK,CAAC,CAAC;MAC5D,CAAC,CAAC;IACJ;IACA7B,eAAe,CAACO,MAAM,CAACiF,QAAQ,EAAE,6BAA6B,CAAC,CAAClB,OAAO,CAACvC,OAAO,IAAI;MACjFA,OAAO,CAACwC,KAAK,CAACjB,UAAU,CAAC,GAAG,GAAG7B,MAAM,GAAGkC,IAAI,CAACa,GAAG,CAACjE,MAAM,CAACkE,qBAAqB,CAAC,CAAC,CAAC,IAAI;IACtF,CAAC,CAAC;IACFP,UAAU,CAAC,CAAC;EACd;EACA,SAAS2B,WAAWA,CAAChF,MAAM,EAAE;IAC3B,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,QAAQ,IAAIA,MAAM,EAAE;MACpD,KAAK,IAAI+D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/D,MAAM,CAACkD,MAAM,EAAEa,CAAC,IAAI,CAAC,EAAE;QACzC,IAAI/D,MAAM,CAAC+D,CAAC,CAAC,EAAErE,MAAM,CAACI,OAAO,CAACE,MAAM,CAACgE,IAAI,CAAChE,MAAM,CAAC+D,CAAC,CAAC,CAAC;MACtD;IACF,CAAC,MAAM;MACLrE,MAAM,CAACI,OAAO,CAACE,MAAM,CAACgE,IAAI,CAAChE,MAAM,CAAC;IACpC;IACAwB,MAAM,CAAC,IAAI,CAAC;EACd;EACA,SAASyD,YAAYA,CAACjF,MAAM,EAAE;IAC5B,MAAMwC,WAAW,GAAG9C,MAAM,CAAC8C,WAAW;IACtC,IAAI0C,cAAc,GAAG1C,WAAW,GAAG,CAAC;IACpC,IAAI2C,iBAAiB,GAAG,CAAC;IACzB,IAAIC,KAAK,CAACC,OAAO,CAACrF,MAAM,CAAC,EAAE;MACzB,KAAK,IAAI+D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/D,MAAM,CAACkD,MAAM,EAAEa,CAAC,IAAI,CAAC,EAAE;QACzC,IAAI/D,MAAM,CAAC+D,CAAC,CAAC,EAAErE,MAAM,CAACI,OAAO,CAACE,MAAM,CAACsF,OAAO,CAACtF,MAAM,CAAC+D,CAAC,CAAC,CAAC;MACzD;MACAmB,cAAc,GAAG1C,WAAW,GAAGxC,MAAM,CAACkD,MAAM;MAC5CiC,iBAAiB,GAAGnF,MAAM,CAACkD,MAAM;IACnC,CAAC,MAAM;MACLxD,MAAM,CAACI,OAAO,CAACE,MAAM,CAACsF,OAAO,CAACtF,MAAM,CAAC;IACvC;IACA,IAAIN,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACG,KAAK,EAAE;MAC/B,MAAMA,KAAK,GAAGP,MAAM,CAACI,OAAO,CAACG,KAAK;MAClC,MAAMsF,QAAQ,GAAG,CAAC,CAAC;MACnBpC,MAAM,CAACqC,IAAI,CAACvF,KAAK,CAAC,CAACwD,OAAO,CAACgC,WAAW,IAAI;QACxC,MAAMC,QAAQ,GAAGzF,KAAK,CAACwF,WAAW,CAAC;QACnC,MAAME,aAAa,GAAGD,QAAQ,CAACE,YAAY,CAAC,yBAAyB,CAAC;QACtE,IAAID,aAAa,EAAE;UACjBD,QAAQ,CAACnE,YAAY,CAAC,yBAAyB,EAAEsE,QAAQ,CAACF,aAAa,EAAE,EAAE,CAAC,GAAGR,iBAAiB,CAAC;QACnG;QACAI,QAAQ,CAACM,QAAQ,CAACJ,WAAW,EAAE,EAAE,CAAC,GAAGN,iBAAiB,CAAC,GAAGO,QAAQ;MACpE,CAAC,CAAC;MACFhG,MAAM,CAACI,OAAO,CAACG,KAAK,GAAGsF,QAAQ;IACjC;IACA/D,MAAM,CAAC,IAAI,CAAC;IACZ9B,MAAM,CAACoG,OAAO,CAACZ,cAAc,EAAE,CAAC,CAAC;EACnC;EACA,SAASa,WAAWA,CAACC,aAAa,EAAE;IAClC,IAAI,OAAOA,aAAa,KAAK,WAAW,IAAIA,aAAa,KAAK,IAAI,EAAE;IACpE,IAAIxD,WAAW,GAAG9C,MAAM,CAAC8C,WAAW;IACpC,IAAI4C,KAAK,CAACC,OAAO,CAACW,aAAa,CAAC,EAAE;MAChC,KAAK,IAAIjC,CAAC,GAAGiC,aAAa,CAAC9C,MAAM,GAAG,CAAC,EAAEa,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;QACrD,IAAIrE,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACG,KAAK,EAAE;UAC/B,OAAOP,MAAM,CAACI,OAAO,CAACG,KAAK,CAAC+F,aAAa,CAACjC,CAAC,CAAC,CAAC;UAC7C;UACAZ,MAAM,CAACqC,IAAI,CAAC9F,MAAM,CAACI,OAAO,CAACG,KAAK,CAAC,CAACwD,OAAO,CAACwC,GAAG,IAAI;YAC/C,IAAIA,GAAG,GAAGD,aAAa,EAAE;cACvBtG,MAAM,CAACI,OAAO,CAACG,KAAK,CAACgG,GAAG,GAAG,CAAC,CAAC,GAAGvG,MAAM,CAACI,OAAO,CAACG,KAAK,CAACgG,GAAG,CAAC;cACzDvG,MAAM,CAACI,OAAO,CAACG,KAAK,CAACgG,GAAG,GAAG,CAAC,CAAC,CAAC1E,YAAY,CAAC,yBAAyB,EAAE0E,GAAG,GAAG,CAAC,CAAC;cAC9E,OAAOvG,MAAM,CAACI,OAAO,CAACG,KAAK,CAACgG,GAAG,CAAC;YAClC;UACF,CAAC,CAAC;QACJ;QACAvG,MAAM,CAACI,OAAO,CAACE,MAAM,CAACkG,MAAM,CAACF,aAAa,CAACjC,CAAC,CAAC,EAAE,CAAC,CAAC;QACjD,IAAIiC,aAAa,CAACjC,CAAC,CAAC,GAAGvB,WAAW,EAAEA,WAAW,IAAI,CAAC;QACpDA,WAAW,GAAGM,IAAI,CAACE,GAAG,CAACR,WAAW,EAAE,CAAC,CAAC;MACxC;IACF,CAAC,MAAM;MACL,IAAI9C,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACG,KAAK,EAAE;QAC/B,OAAOP,MAAM,CAACI,OAAO,CAACG,KAAK,CAAC+F,aAAa,CAAC;QAC1C;QACA7C,MAAM,CAACqC,IAAI,CAAC9F,MAAM,CAACI,OAAO,CAACG,KAAK,CAAC,CAACwD,OAAO,CAACwC,GAAG,IAAI;UAC/C,IAAIA,GAAG,GAAGD,aAAa,EAAE;YACvBtG,MAAM,CAACI,OAAO,CAACG,KAAK,CAACgG,GAAG,GAAG,CAAC,CAAC,GAAGvG,MAAM,CAACI,OAAO,CAACG,KAAK,CAACgG,GAAG,CAAC;YACzDvG,MAAM,CAACI,OAAO,CAACG,KAAK,CAACgG,GAAG,GAAG,CAAC,CAAC,CAAC1E,YAAY,CAAC,yBAAyB,EAAE0E,GAAG,GAAG,CAAC,CAAC;YAC9E,OAAOvG,MAAM,CAACI,OAAO,CAACG,KAAK,CAACgG,GAAG,CAAC;UAClC;QACF,CAAC,CAAC;MACJ;MACAvG,MAAM,CAACI,OAAO,CAACE,MAAM,CAACkG,MAAM,CAACF,aAAa,EAAE,CAAC,CAAC;MAC9C,IAAIA,aAAa,GAAGxD,WAAW,EAAEA,WAAW,IAAI,CAAC;MACjDA,WAAW,GAAGM,IAAI,CAACE,GAAG,CAACR,WAAW,EAAE,CAAC,CAAC;IACxC;IACAhB,MAAM,CAAC,IAAI,CAAC;IACZ9B,MAAM,CAACoG,OAAO,CAACtD,WAAW,EAAE,CAAC,CAAC;EAChC;EACA,SAAS2D,eAAeA,CAAA,EAAG;IACzBzG,MAAM,CAACI,OAAO,CAACE,MAAM,GAAG,EAAE;IAC1B,IAAIN,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACG,KAAK,EAAE;MAC/BP,MAAM,CAACI,OAAO,CAACG,KAAK,GAAG,CAAC,CAAC;IAC3B;IACAuB,MAAM,CAAC,IAAI,CAAC;IACZ9B,MAAM,CAACoG,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EACtB;EACAlG,EAAE,CAAC,YAAY,EAAE,MAAM;IACrB,IAAI,CAACF,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACC,OAAO,EAAE;IACpC,IAAIqG,iBAAiB;IACrB,IAAI,OAAO1G,MAAM,CAAC2G,YAAY,CAACvG,OAAO,CAACE,MAAM,KAAK,WAAW,EAAE;MAC7D,MAAMA,MAAM,GAAG,CAAC,GAAGN,MAAM,CAACiF,QAAQ,CAACvD,QAAQ,CAAC,CAACiD,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACC,OAAO,CAAC,IAAI7E,MAAM,CAACuB,MAAM,CAACK,UAAU,gBAAgB,CAAC,CAAC;MACnH,IAAItB,MAAM,IAAIA,MAAM,CAACkD,MAAM,EAAE;QAC3BxD,MAAM,CAACI,OAAO,CAACE,MAAM,GAAG,CAAC,GAAGA,MAAM,CAAC;QACnCoG,iBAAiB,GAAG,IAAI;QACxBpG,MAAM,CAACyD,OAAO,CAAC,CAACvC,OAAO,EAAEkD,UAAU,KAAK;UACtClD,OAAO,CAACK,YAAY,CAAC,yBAAyB,EAAE6C,UAAU,CAAC;UAC3D1E,MAAM,CAACI,OAAO,CAACG,KAAK,CAACmE,UAAU,CAAC,GAAGlD,OAAO;UAC1CA,OAAO,CAACsD,MAAM,CAAC,CAAC;QAClB,CAAC,CAAC;MACJ;IACF;IACA,IAAI,CAAC4B,iBAAiB,EAAE;MACtB1G,MAAM,CAACI,OAAO,CAACE,MAAM,GAAGN,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACE,MAAM;IACtD;IACAN,MAAM,CAAC4G,UAAU,CAACtC,IAAI,CAAC,GAAGtE,MAAM,CAACuB,MAAM,CAACsF,sBAAsB,SAAS,CAAC;IACxE7G,MAAM,CAACuB,MAAM,CAACuF,mBAAmB,GAAG,IAAI;IACxC9G,MAAM,CAAC+G,cAAc,CAACD,mBAAmB,GAAG,IAAI;IAChDhF,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC;EACrB,CAAC,CAAC;EACF5B,EAAE,CAAC,cAAc,EAAE,MAAM;IACvB,IAAI,CAACF,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACC,OAAO,EAAE;IACpC,IAAIL,MAAM,CAACuB,MAAM,CAACqB,OAAO,IAAI,CAAC5C,MAAM,CAACgH,iBAAiB,EAAE;MACtDC,YAAY,CAACpG,cAAc,CAAC;MAC5BA,cAAc,GAAGqG,UAAU,CAAC,MAAM;QAChCpF,MAAM,CAAC,CAAC;MACV,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,MAAM;MACLA,MAAM,CAAC,CAAC;IACV;EACF,CAAC,CAAC;EACF5B,EAAE,CAAC,oBAAoB,EAAE,MAAM;IAC7B,IAAI,CAACF,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACC,OAAO,EAAE;IACpC,IAAIL,MAAM,CAACuB,MAAM,CAACqB,OAAO,EAAE;MACzBrD,cAAc,CAACS,MAAM,CAACmH,SAAS,EAAE,uBAAuB,EAAE,GAAGnH,MAAM,CAACoH,WAAW,IAAI,CAAC;IACtF;EACF,CAAC,CAAC;EACF3D,MAAM,CAACC,MAAM,CAAC1D,MAAM,CAACI,OAAO,EAAE;IAC5BkF,WAAW;IACXC,YAAY;IACZc,WAAW;IACXI,eAAe;IACf3E;EACF,CAAC,CAAC;AACJ;AAEA,SAAShC,OAAO,IAAIuH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}