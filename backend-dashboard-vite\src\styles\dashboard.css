.dashboard-container {
  padding: 1.5rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.stat-info h3 {
  color: #6c757d;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  margin: 0 0 0.5rem 0;
}

.stat-info .value {
  font-size: 1.75rem;
  font-weight: 600;
  color: #2a3042;
  margin: 0;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-icon.books {
  background-color: rgba(255, 107, 107, 0.1);
  color: #ff6b6b;
}

.stat-icon.orders {
  background-color: rgba(10, 207, 151, 0.1);
  color: #0acf97;
}

.stat-icon.users {
  background-color: rgba(65, 84, 241, 0.1);
  color: #4154f1;
}

.stat-icon.revenue {
  background-color: rgba(251, 188, 6, 0.1);
  color: #fbc006;
}

.recent-activity {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.recent-activity h2 {
  font-size: 1.25rem;
  color: #2a3042;
  margin: 0 0 1.5rem 0;
  font-weight: 600;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.activity-item:hover {
  background-color: #f8f9fa;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(65, 84, 241, 0.1);
  color: #4154f1;
}

.activity-content h3 {
  font-size: 0.875rem;
  color: #2a3042;
  margin: 0 0 0.25rem 0;
  font-weight: 500;
}

.activity-content p {
  font-size: 0.75rem;
  color: #6c757d;
  margin: 0;
}

@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-container {
    padding: 1rem;
  }
}

/* Page Styles */
.page-content {
  padding: 1.5rem;
}

.page-header {
  margin-bottom: 1.5rem;
}

.page-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2a3042;
  margin: 0;
}

.page-body {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.card-body {
  padding: 1.5rem;
}

.card-body h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2a3042;
  margin: 0 0 1rem 0;
}

@media (max-width: 768px) {
  .page-content {
    padding: 1rem;
  }
  
  .card-body {
    padding: 1rem;
  }
} 