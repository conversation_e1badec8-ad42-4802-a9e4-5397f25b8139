import { BrowserRouter as Router, Route, Routes, Navigate } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Suspense, lazy } from 'react';
import SidebarMenu from './components/SidebarMenu';
import UserMenu from './components/UserMenu';
import './App.css';
import './styles/dashboard.css';
import './styles/auth.css';

// Lazy load components
const Dashboard = lazy(() => import('./components/Dashboard'));
const Login = lazy(() => import('./components/auth/Login'));
const Register = lazy(() => import('./components/auth/Register'));
const Orders = lazy(() => import('./pages/Orders'));
const OrderForm = lazy(() => import('./components/orders/OrderForm'));
const Books = lazy(() => import('./pages/Books'));
const BookForm = lazy(() => import('./components/books/BookForm'));
const Categories = lazy(() => import('./pages/Categories'));
const CategoryForm = lazy(() => import('./components/categories/CategoryForm'));
const Purchases = lazy(() => import('./pages/Purchases'));
const Users = lazy(() => import('./pages/Users'));
const Pages = lazy(() => import('./pages/Pages'));
const UserForm = lazy(() => import('./components/users/UserForm'));
const EditUser = lazy(() => import('./components/users/EditUser'));

// Loading component
const LoadingSpinner = () => (
  <div className="d-flex justify-content-center align-items-center" style={{ height: '100vh' }}>
    <div className="spinner-border text-primary" role="status">
      <span className="visually-hidden">Loading...</span>
    </div>
  </div>
);

function App() {
  const isAuthenticated = localStorage.getItem('token');

  if (!isAuthenticated) {
    return (
      <Router>
        <ToastContainer />
        <Suspense fallback={<LoadingSpinner />}>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="*" element={<Navigate to="/login" replace />} />
          </Routes>
        </Suspense>
      </Router>
    );
  }

  return (
    <Router>
      <ToastContainer />
      <div className="app-wrapper">
        {/* Sidebar */}
        <SidebarMenu />

        {/* Main Content */}
        <main className="main-content">
          {/* Header */}
          <header className="header">
            <div className="header-content">
              <UserMenu />
            </div>
          </header>

          {/* Content Area */}
          <div className="content-area">
            <Suspense fallback={<LoadingSpinner />}>
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/orders" element={<Orders />} />
                <Route path="/orders/new" element={<OrderForm />} />
                <Route path="/orders/:id/edit" element={<OrderForm />} />
                <Route path="/books" element={<Books />} />
                <Route path="/books/new" element={<BookForm />} />
                <Route path="/books/:id/edit" element={<BookForm />} />
                <Route path="/categories" element={<Categories />} />
                <Route path="/categories/new" element={<CategoryForm />} />
                <Route path="/categories/:id/edit" element={<CategoryForm />} />
                <Route path="/purchases" element={<Purchases />} />
                <Route path="/users" element={<Users />} />
                <Route path="/users/new" element={<UserForm />} />
                <Route path="/users/:id/edit" element={<EditUser />} />
                <Route path="/pages" element={<Pages />} />
                <Route path="/larkon" element={
                  <iframe
                    src="/larkon/index.html"
                    style={{
                      width: '100%',
                      height: '100vh',
                      border: 'none'
                    }}
                    title="Larkon Page"
                  />
                } />
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </Suspense>
          </div>
        </main>
      </div>
    </Router>
  );
}

export default App;
