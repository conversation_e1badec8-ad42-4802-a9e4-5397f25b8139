{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\LaravelReact\\\\frontend-ui\\\\src\\\\shop\\\\ProductDisplay.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Link } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductDisplay = ({\n  item\n}) => {\n  _s();\n  const {\n    title,\n    id,\n    price,\n    brand,\n    stock,\n    description,\n    BookID\n  } = item;\n  const [prequantity, setQuantity] = useState(1);\n  const [coupon, setCoupon] = useState(\"\");\n  const [addedToCart, setAddedToCart] = useState(false);\n  const handleIncrease = () => {\n    setQuantity(prequantity + 1);\n  };\n  const handleDecrease = () => {\n    if (prequantity > 1) {\n      setQuantity(prequantity - 1);\n    }\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    try {\n      // Ensure price is stored as a number\n      const numericPrice = parseFloat(price);\n\n      // Create product with parsed values\n      const product = {\n        id: id,\n        BookID: BookID,\n        // Store BookID for image\n        name: title,\n        price: numericPrice,\n        quantity: prequantity,\n        coupon: coupon\n      };\n\n      // Retrieve cart from local storage or initialize a new one\n      const existingCart = JSON.parse(localStorage.getItem(\"cart\")) || [];\n      const existingProductIndex = existingCart.findIndex(item => item.id === id);\n      if (existingProductIndex !== -1) {\n        existingCart[existingProductIndex].quantity += prequantity;\n      } else {\n        existingCart.push(product);\n      }\n\n      // Update local storage\n      localStorage.setItem(\"cart\", JSON.stringify(existingCart));\n\n      // Reset form fields\n      setQuantity(1);\n      setCoupon(\"\");\n\n      // Show success message\n      setAddedToCart(true);\n      setTimeout(() => setAddedToCart(false), 3000);\n    } catch (error) {\n      console.error(\"Error adding product to cart:\", error);\n      alert(\"There was an error adding the product to your cart.\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n        children: [\"$\", parseFloat(price).toFixed(2)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n        children: brand\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-3 d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"select-product\",\n            style: {\n              width: \"100%\"\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex align-items-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"cart-plus-minus\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"dec qtybutton\",\n                  onClick: handleDecrease,\n                  children: \"-\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  className: \"cart-plus-minus-box\",\n                  name: \"qtybutton\",\n                  id: \"qtybutton\",\n                  value: prequantity,\n                  onChange: e => setQuantity(parseInt(e.target.value, 10) || 1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"inc qtybutton\",\n                  onClick: handleIncrease,\n                  children: \"+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 96,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"select-product\",\n            style: {\n              width: \"100%\"\n            },\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Enter Discount Code\",\n              onChange: e => setCoupon(e.target.value),\n              className: \"form-control\",\n              style: {\n                fontSize: \"0.85rem\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), addedToCart && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"alert alert-success mb-3\",\n          role: \"alert\",\n          children: \"Book added to cart successfully!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"lab-btn\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Add to Cart\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/cart-page\",\n          className: \"lab-btn bg-primary\",\n          children: [\"Check Out\", /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 22\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductDisplay, \"YthA26uIX4xLoaj3Vqw0Kt6L6Ao=\");\n_c = ProductDisplay;\nexport default ProductDisplay;\nvar _c;\n$RefreshReg$(_c, \"ProductDisplay\");", "map": {"version": 3, "names": ["React", "useState", "Link", "jsxDEV", "_jsxDEV", "ProductDisplay", "item", "_s", "title", "id", "price", "brand", "stock", "description", "BookID", "prequantity", "setQuantity", "coupon", "setCoupon", "addedToCart", "setAddedToCart", "handleIncrease", "handleDecrease", "handleSubmit", "e", "preventDefault", "numericPrice", "parseFloat", "product", "name", "quantity", "existingCart", "JSON", "parse", "localStorage", "getItem", "existingProductIndex", "findIndex", "push", "setItem", "stringify", "setTimeout", "error", "console", "alert", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toFixed", "onSubmit", "className", "style", "width", "onClick", "type", "value", "onChange", "parseInt", "target", "placeholder", "fontSize", "role", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/shop/ProductDisplay.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { Link } from \"react-router-dom\";\n\nconst ProductDisplay = ({ item }) => {\n  const { title, id, price, brand, stock, description, BookID } = item;\n  const [prequantity, setQuantity] = useState(1);\n  const [coupon, setCoupon] = useState(\"\");\n  const [addedToCart, setAddedToCart] = useState(false);\n\n  const handleIncrease = () => {\n    setQuantity(prequantity + 1);\n  };\n\n  const handleDecrease = () => {\n    if (prequantity > 1) {\n      setQuantity(prequantity - 1);\n    }\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    try {\n      // Ensure price is stored as a number\n      const numericPrice = parseFloat(price);\n\n      // Create product with parsed values\n      const product = {\n        id: id,\n        BookID: BookID, // Store BookID for image\n        name: title,\n        price: numericPrice,\n        quantity: prequantity,\n        coupon: coupon,\n      };\n\n      // Retrieve cart from local storage or initialize a new one\n      const existingCart = JSON.parse(localStorage.getItem(\"cart\")) || [];\n\n      const existingProductIndex = existingCart.findIndex(\n        (item) => item.id === id\n      );\n\n      if (existingProductIndex !== -1) {\n        existingCart[existingProductIndex].quantity += prequantity;\n      } else {\n        existingCart.push(product);\n      }\n\n      // Update local storage\n      localStorage.setItem(\"cart\", JSON.stringify(existingCart));\n\n      // Reset form fields\n      setQuantity(1);\n      setCoupon(\"\");\n\n      // Show success message\n      setAddedToCart(true);\n      setTimeout(() => setAddedToCart(false), 3000);\n    } catch (error) {\n      console.error(\"Error adding product to cart:\", error);\n      alert(\"There was an error adding the product to your cart.\");\n    }\n  };\n\n  return (\n    <div>\n      <div>\n        <h4>{title}</h4>\n        <h4>${parseFloat(price).toFixed(2)}</h4>\n        <h6>{brand}</h6>\n        <p>{description}</p>\n      </div>\n\n      {/* Cart component */}\n      <div>\n        <form onSubmit={handleSubmit}>\n          {/* Container for quantity and discount code */}\n          <div className=\"mb-3 d-flex justify-content-between align-items-center\">\n            {/* Updated Cart quantity input */}\n            <div className=\"select-product\" style={{ width: \"100%\" }}>\n              <div className=\"d-flex align-items-center\">\n                <div className=\"cart-plus-minus\">\n                  <div className=\"dec qtybutton\" onClick={handleDecrease}>\n                    -\n                  </div>\n                  <input\n                    type=\"text\"\n                    className=\"cart-plus-minus-box\"\n                    name=\"qtybutton\"\n                    id=\"qtybutton\"\n                    value={prequantity}\n                    onChange={(e) =>\n                      setQuantity(parseInt(e.target.value, 10) || 1)\n                    }\n                  />\n                  <div className=\"inc qtybutton\" onClick={handleIncrease}>\n                    +\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Coupon field */}\n            <div className=\"select-product\" style={{ width: \"100%\" }}>\n              <input\n                type=\"text\"\n                placeholder=\"Enter Discount Code\"\n                onChange={(e) => setCoupon(e.target.value)}\n                className=\"form-control\"\n                style={{ fontSize: \"0.85rem\" }}\n              />\n            </div>\n          </div>\n\n          {/* Success message */}\n          {addedToCart && (\n            <div className=\"alert alert-success mb-3\" role=\"alert\">\n              Book added to cart successfully!\n            </div>\n          )}\n\n          {/* Button section */}\n          <button type=\"submit\" className=\"lab-btn\">\n            <span>Add to Cart</span>\n          </button>\n          <Link to=\"/cart-page\" className=\"lab-btn bg-primary\">\n            Check Out<span></span>\n          </Link>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductDisplay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACnC,MAAM;IAAEC,KAAK;IAAEC,EAAE;IAAEC,KAAK;IAAEC,KAAK;IAAEC,KAAK;IAAEC,WAAW;IAAEC;EAAO,CAAC,GAAGR,IAAI;EACpE,MAAM,CAACS,WAAW,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACgB,MAAM,EAAEC,SAAS,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMoB,cAAc,GAAGA,CAAA,KAAM;IAC3BL,WAAW,CAACD,WAAW,GAAG,CAAC,CAAC;EAC9B,CAAC;EAED,MAAMO,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIP,WAAW,GAAG,CAAC,EAAE;MACnBC,WAAW,CAACD,WAAW,GAAG,CAAC,CAAC;IAC9B;EACF,CAAC;EAED,MAAMQ,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF;MACA,MAAMC,YAAY,GAAGC,UAAU,CAACjB,KAAK,CAAC;;MAEtC;MACA,MAAMkB,OAAO,GAAG;QACdnB,EAAE,EAAEA,EAAE;QACNK,MAAM,EAAEA,MAAM;QAAE;QAChBe,IAAI,EAAErB,KAAK;QACXE,KAAK,EAAEgB,YAAY;QACnBI,QAAQ,EAAEf,WAAW;QACrBE,MAAM,EAAEA;MACV,CAAC;;MAED;MACA,MAAMc,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE;MAEnE,MAAMC,oBAAoB,GAAGL,YAAY,CAACM,SAAS,CAChD/B,IAAI,IAAKA,IAAI,CAACG,EAAE,KAAKA,EACxB,CAAC;MAED,IAAI2B,oBAAoB,KAAK,CAAC,CAAC,EAAE;QAC/BL,YAAY,CAACK,oBAAoB,CAAC,CAACN,QAAQ,IAAIf,WAAW;MAC5D,CAAC,MAAM;QACLgB,YAAY,CAACO,IAAI,CAACV,OAAO,CAAC;MAC5B;;MAEA;MACAM,YAAY,CAACK,OAAO,CAAC,MAAM,EAAEP,IAAI,CAACQ,SAAS,CAACT,YAAY,CAAC,CAAC;;MAE1D;MACAf,WAAW,CAAC,CAAC,CAAC;MACdE,SAAS,CAAC,EAAE,CAAC;;MAEb;MACAE,cAAc,CAAC,IAAI,CAAC;MACpBqB,UAAU,CAAC,MAAMrB,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IAC/C,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDE,KAAK,CAAC,qDAAqD,CAAC;IAC9D;EACF,CAAC;EAED,oBACExC,OAAA;IAAAyC,QAAA,gBACEzC,OAAA;MAAAyC,QAAA,gBACEzC,OAAA;QAAAyC,QAAA,EAAKrC;MAAK;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAChB7C,OAAA;QAAAyC,QAAA,GAAI,GAAC,EAAClB,UAAU,CAACjB,KAAK,CAAC,CAACwC,OAAO,CAAC,CAAC,CAAC;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACxC7C,OAAA;QAAAyC,QAAA,EAAKlC;MAAK;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAChB7C,OAAA;QAAAyC,QAAA,EAAIhC;MAAW;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eAGN7C,OAAA;MAAAyC,QAAA,eACEzC,OAAA;QAAM+C,QAAQ,EAAE5B,YAAa;QAAAsB,QAAA,gBAE3BzC,OAAA;UAAKgD,SAAS,EAAC,wDAAwD;UAAAP,QAAA,gBAErEzC,OAAA;YAAKgD,SAAS,EAAC,gBAAgB;YAACC,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAT,QAAA,eACvDzC,OAAA;cAAKgD,SAAS,EAAC,2BAA2B;cAAAP,QAAA,eACxCzC,OAAA;gBAAKgD,SAAS,EAAC,iBAAiB;gBAAAP,QAAA,gBAC9BzC,OAAA;kBAAKgD,SAAS,EAAC,eAAe;kBAACG,OAAO,EAAEjC,cAAe;kBAAAuB,QAAA,EAAC;gBAExD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN7C,OAAA;kBACEoD,IAAI,EAAC,MAAM;kBACXJ,SAAS,EAAC,qBAAqB;kBAC/BvB,IAAI,EAAC,WAAW;kBAChBpB,EAAE,EAAC,WAAW;kBACdgD,KAAK,EAAE1C,WAAY;kBACnB2C,QAAQ,EAAGlC,CAAC,IACVR,WAAW,CAAC2C,QAAQ,CAACnC,CAAC,CAACoC,MAAM,CAACH,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC;gBAC9C;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACF7C,OAAA;kBAAKgD,SAAS,EAAC,eAAe;kBAACG,OAAO,EAAElC,cAAe;kBAAAwB,QAAA,EAAC;gBAExD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7C,OAAA;YAAKgD,SAAS,EAAC,gBAAgB;YAACC,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAT,QAAA,eACvDzC,OAAA;cACEoD,IAAI,EAAC,MAAM;cACXK,WAAW,EAAC,qBAAqB;cACjCH,QAAQ,EAAGlC,CAAC,IAAKN,SAAS,CAACM,CAAC,CAACoC,MAAM,CAACH,KAAK,CAAE;cAC3CL,SAAS,EAAC,cAAc;cACxBC,KAAK,EAAE;gBAAES,QAAQ,EAAE;cAAU;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL9B,WAAW,iBACVf,OAAA;UAAKgD,SAAS,EAAC,0BAA0B;UAACW,IAAI,EAAC,OAAO;UAAAlB,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,eAGD7C,OAAA;UAAQoD,IAAI,EAAC,QAAQ;UAACJ,SAAS,EAAC,SAAS;UAAAP,QAAA,eACvCzC,OAAA;YAAAyC,QAAA,EAAM;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACT7C,OAAA,CAACF,IAAI;UAAC8D,EAAE,EAAC,YAAY;UAACZ,SAAS,EAAC,oBAAoB;UAAAP,QAAA,GAAC,WAC1C,eAAAzC,OAAA;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1C,EAAA,CAjIIF,cAAc;AAAA4D,EAAA,GAAd5D,cAAc;AAmIpB,eAAeA,cAAc;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}