{"ast": null, "code": "/**\n * Get the width of the vertical window scrollbar if it's visible\n */\nexport default function getBodyScrollbarWidth(ownerDocument = document) {\n  const window = ownerDocument.defaultView;\n  return Math.abs(window.innerWidth - ownerDocument.documentElement.clientWidth);\n}", "map": {"version": 3, "names": ["getBodyScrollbarWidth", "ownerDocument", "document", "window", "defaultView", "Math", "abs", "innerWidth", "documentElement", "clientWidth"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/@restart/ui/esm/getScrollbarWidth.js"], "sourcesContent": ["/**\n * Get the width of the vertical window scrollbar if it's visible\n */\nexport default function getBodyScrollbarWidth(ownerDocument = document) {\n  const window = ownerDocument.defaultView;\n  return Math.abs(window.innerWidth - ownerDocument.documentElement.clientWidth);\n}"], "mappings": "AAAA;AACA;AACA;AACA,eAAe,SAASA,qBAAqBA,CAACC,aAAa,GAAGC,QAAQ,EAAE;EACtE,MAAMC,MAAM,GAAGF,aAAa,CAACG,WAAW;EACxC,OAAOC,IAAI,CAACC,GAAG,CAACH,MAAM,CAACI,UAAU,GAAGN,aAAa,CAACO,eAAe,CAACC,WAAW,CAAC;AAChF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}