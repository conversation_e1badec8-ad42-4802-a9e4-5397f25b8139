{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ModalDialog = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  contentClassName,\n  centered,\n  size,\n  fullscreen,\n  children,\n  scrollable,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'modal');\n  const dialogClass = `${bsPrefix}-dialog`;\n  const fullScreenClass = typeof fullscreen === 'string' ? `${bsPrefix}-fullscreen-${fullscreen}` : `${bsPrefix}-fullscreen`;\n  return /*#__PURE__*/_jsx(\"div\", {\n    ...props,\n    ref: ref,\n    className: classNames(dialogClass, className, size && `${bsPrefix}-${size}`, centered && `${dialogClass}-centered`, scrollable && `${dialogClass}-scrollable`, fullscreen && fullScreenClass),\n    children: /*#__PURE__*/_jsx(\"div\", {\n      className: classNames(`${bsPrefix}-content`, contentClassName),\n      children: children\n    })\n  });\n});\nModalDialog.displayName = 'ModalDialog';\nexport default ModalDialog;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "ModalDialog", "forwardRef", "bsPrefix", "className", "contentClassName", "centered", "size", "fullscreen", "children", "scrollable", "props", "ref", "dialogClass", "fullScreenClass", "displayName"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/react-bootstrap/esm/ModalDialog.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ModalDialog = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  contentClassName,\n  centered,\n  size,\n  fullscreen,\n  children,\n  scrollable,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'modal');\n  const dialogClass = `${bsPrefix}-dialog`;\n  const fullScreenClass = typeof fullscreen === 'string' ? `${bsPrefix}-fullscreen-${fullscreen}` : `${bsPrefix}-fullscreen`;\n  return /*#__PURE__*/_jsx(\"div\", {\n    ...props,\n    ref: ref,\n    className: classNames(dialogClass, className, size && `${bsPrefix}-${size}`, centered && `${dialogClass}-centered`, scrollable && `${dialogClass}-scrollable`, fullscreen && fullScreenClass),\n    children: /*#__PURE__*/_jsx(\"div\", {\n      className: classNames(`${bsPrefix}-content`, contentClassName),\n      children: children\n    })\n  });\n});\nModalDialog.displayName = 'ModalDialog';\nexport default ModalDialog;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,WAAW,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAAC;EACjDC,QAAQ;EACRC,SAAS;EACTC,gBAAgB;EAChBC,QAAQ;EACRC,IAAI;EACJC,UAAU;EACVC,QAAQ;EACRC,UAAU;EACV,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTT,QAAQ,GAAGL,kBAAkB,CAACK,QAAQ,EAAE,OAAO,CAAC;EAChD,MAAMU,WAAW,GAAG,GAAGV,QAAQ,SAAS;EACxC,MAAMW,eAAe,GAAG,OAAON,UAAU,KAAK,QAAQ,GAAG,GAAGL,QAAQ,eAAeK,UAAU,EAAE,GAAG,GAAGL,QAAQ,aAAa;EAC1H,OAAO,aAAaH,IAAI,CAAC,KAAK,EAAE;IAC9B,GAAGW,KAAK;IACRC,GAAG,EAAEA,GAAG;IACRR,SAAS,EAAER,UAAU,CAACiB,WAAW,EAAET,SAAS,EAAEG,IAAI,IAAI,GAAGJ,QAAQ,IAAII,IAAI,EAAE,EAAED,QAAQ,IAAI,GAAGO,WAAW,WAAW,EAAEH,UAAU,IAAI,GAAGG,WAAW,aAAa,EAAEL,UAAU,IAAIM,eAAe,CAAC;IAC7LL,QAAQ,EAAE,aAAaT,IAAI,CAAC,KAAK,EAAE;MACjCI,SAAS,EAAER,UAAU,CAAC,GAAGO,QAAQ,UAAU,EAAEE,gBAAgB,CAAC;MAC9DI,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFR,WAAW,CAACc,WAAW,GAAG,aAAa;AACvC,eAAed,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}