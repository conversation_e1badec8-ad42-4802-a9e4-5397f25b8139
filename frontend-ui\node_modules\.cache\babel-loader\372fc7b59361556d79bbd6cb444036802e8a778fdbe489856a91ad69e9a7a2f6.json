{"ast": null, "code": "import { c as createShadow } from '../shared/create-shadow.mjs';\nimport { e as effectInit } from '../shared/effect-init.mjs';\nimport { e as effectTarget } from '../shared/effect-target.mjs';\nimport { e as effectVirtualTransitionEnd } from '../shared/effect-virtual-transition-end.mjs';\nimport { g as getSlideTransformEl, p as getRotateFix } from '../shared/utils.mjs';\nfunction EffectFlip(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    flipEffect: {\n      slideShadows: true,\n      limitRotation: true\n    }\n  });\n  const createSlideShadows = (slideEl, progress) => {\n    let shadowBefore = swiper.isHorizontal() ? slideEl.querySelector('.swiper-slide-shadow-left') : slideEl.querySelector('.swiper-slide-shadow-top');\n    let shadowAfter = swiper.isHorizontal() ? slideEl.querySelector('.swiper-slide-shadow-right') : slideEl.querySelector('.swiper-slide-shadow-bottom');\n    if (!shadowBefore) {\n      shadowBefore = createShadow('flip', slideEl, swiper.isHorizontal() ? 'left' : 'top');\n    }\n    if (!shadowAfter) {\n      shadowAfter = createShadow('flip', slideEl, swiper.isHorizontal() ? 'right' : 'bottom');\n    }\n    if (shadowBefore) shadowBefore.style.opacity = Math.max(-progress, 0);\n    if (shadowAfter) shadowAfter.style.opacity = Math.max(progress, 0);\n  };\n  const recreateShadows = () => {\n    // Set shadows\n    swiper.params.flipEffect;\n    swiper.slides.forEach(slideEl => {\n      let progress = slideEl.progress;\n      if (swiper.params.flipEffect.limitRotation) {\n        progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      }\n      createSlideShadows(slideEl, progress);\n    });\n  };\n  const setTranslate = () => {\n    const {\n      slides,\n      rtlTranslate: rtl\n    } = swiper;\n    const params = swiper.params.flipEffect;\n    const rotateFix = getRotateFix(swiper);\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      let progress = slideEl.progress;\n      if (swiper.params.flipEffect.limitRotation) {\n        progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      }\n      const offset = slideEl.swiperSlideOffset;\n      const rotate = -180 * progress;\n      let rotateY = rotate;\n      let rotateX = 0;\n      let tx = swiper.params.cssMode ? -offset - swiper.translate : -offset;\n      let ty = 0;\n      if (!swiper.isHorizontal()) {\n        ty = tx;\n        tx = 0;\n        rotateX = -rotateY;\n        rotateY = 0;\n      } else if (rtl) {\n        rotateY = -rotateY;\n      }\n      slideEl.style.zIndex = -Math.abs(Math.round(progress)) + slides.length;\n      if (params.slideShadows) {\n        createSlideShadows(slideEl, progress);\n      }\n      const transform = `translate3d(${tx}px, ${ty}px, 0px) rotateX(${rotateFix(rotateX)}deg) rotateY(${rotateFix(rotateY)}deg)`;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = transform;\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements\n    });\n  };\n  effectInit({\n    effect: 'flip',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    recreateShadows,\n    getEffectParams: () => swiper.params.flipEffect,\n    perspective: () => true,\n    overwriteParams: () => ({\n      slidesPerView: 1,\n      slidesPerGroup: 1,\n      watchSlidesProgress: true,\n      spaceBetween: 0,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}\nexport { EffectFlip as default };", "map": {"version": 3, "names": ["c", "createShadow", "e", "effectInit", "effect<PERSON>arget", "effectVirtualTransitionEnd", "g", "getSlideTransformEl", "p", "getRotateFix", "EffectFlip", "_ref", "swiper", "extendParams", "on", "flipEffect", "slideShadows", "limitRotation", "createSlideShadows", "slideEl", "progress", "shadowBefore", "isHorizontal", "querySelector", "shadowAfter", "style", "opacity", "Math", "max", "recreateShadows", "params", "slides", "for<PERSON>ach", "min", "setTranslate", "rtlTranslate", "rtl", "rotateFix", "i", "length", "offset", "swiperSlideOffset", "rotate", "rotateY", "rotateX", "tx", "cssMode", "translate", "ty", "zIndex", "abs", "round", "transform", "targetEl", "setTransition", "duration", "transformElements", "map", "el", "transitionDuration", "querySelectorAll", "shadowEl", "effect", "getEffectParams", "perspective", "overwriteParams", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerGroup", "watchSlidesProgress", "spaceBetween", "virtualTranslate", "default"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/swiper/modules/effect-flip.mjs"], "sourcesContent": ["import { c as createShadow } from '../shared/create-shadow.mjs';\nimport { e as effectInit } from '../shared/effect-init.mjs';\nimport { e as effectTarget } from '../shared/effect-target.mjs';\nimport { e as effectVirtualTransitionEnd } from '../shared/effect-virtual-transition-end.mjs';\nimport { g as getSlideTransformEl, p as getRotateFix } from '../shared/utils.mjs';\n\nfunction EffectFlip(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    flipEffect: {\n      slideShadows: true,\n      limitRotation: true\n    }\n  });\n  const createSlideShadows = (slideEl, progress) => {\n    let shadowBefore = swiper.isHorizontal() ? slideEl.querySelector('.swiper-slide-shadow-left') : slideEl.querySelector('.swiper-slide-shadow-top');\n    let shadowAfter = swiper.isHorizontal() ? slideEl.querySelector('.swiper-slide-shadow-right') : slideEl.querySelector('.swiper-slide-shadow-bottom');\n    if (!shadowBefore) {\n      shadowBefore = createShadow('flip', slideEl, swiper.isHorizontal() ? 'left' : 'top');\n    }\n    if (!shadowAfter) {\n      shadowAfter = createShadow('flip', slideEl, swiper.isHorizontal() ? 'right' : 'bottom');\n    }\n    if (shadowBefore) shadowBefore.style.opacity = Math.max(-progress, 0);\n    if (shadowAfter) shadowAfter.style.opacity = Math.max(progress, 0);\n  };\n  const recreateShadows = () => {\n    // Set shadows\n    swiper.params.flipEffect;\n    swiper.slides.forEach(slideEl => {\n      let progress = slideEl.progress;\n      if (swiper.params.flipEffect.limitRotation) {\n        progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      }\n      createSlideShadows(slideEl, progress);\n    });\n  };\n  const setTranslate = () => {\n    const {\n      slides,\n      rtlTranslate: rtl\n    } = swiper;\n    const params = swiper.params.flipEffect;\n    const rotateFix = getRotateFix(swiper);\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      let progress = slideEl.progress;\n      if (swiper.params.flipEffect.limitRotation) {\n        progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      }\n      const offset = slideEl.swiperSlideOffset;\n      const rotate = -180 * progress;\n      let rotateY = rotate;\n      let rotateX = 0;\n      let tx = swiper.params.cssMode ? -offset - swiper.translate : -offset;\n      let ty = 0;\n      if (!swiper.isHorizontal()) {\n        ty = tx;\n        tx = 0;\n        rotateX = -rotateY;\n        rotateY = 0;\n      } else if (rtl) {\n        rotateY = -rotateY;\n      }\n      slideEl.style.zIndex = -Math.abs(Math.round(progress)) + slides.length;\n      if (params.slideShadows) {\n        createSlideShadows(slideEl, progress);\n      }\n      const transform = `translate3d(${tx}px, ${ty}px, 0px) rotateX(${rotateFix(rotateX)}deg) rotateY(${rotateFix(rotateY)}deg)`;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = transform;\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements\n    });\n  };\n  effectInit({\n    effect: 'flip',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    recreateShadows,\n    getEffectParams: () => swiper.params.flipEffect,\n    perspective: () => true,\n    overwriteParams: () => ({\n      slidesPerView: 1,\n      slidesPerGroup: 1,\n      watchSlidesProgress: true,\n      spaceBetween: 0,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}\n\nexport { EffectFlip as default };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,YAAY,QAAQ,6BAA6B;AAC/D,SAASC,CAAC,IAAIC,UAAU,QAAQ,2BAA2B;AAC3D,SAASD,CAAC,IAAIE,YAAY,QAAQ,6BAA6B;AAC/D,SAASF,CAAC,IAAIG,0BAA0B,QAAQ,6CAA6C;AAC7F,SAASC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,YAAY,QAAQ,qBAAqB;AAEjF,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAGH,IAAI;EACRE,YAAY,CAAC;IACXE,UAAU,EAAE;MACVC,YAAY,EAAE,IAAI;MAClBC,aAAa,EAAE;IACjB;EACF,CAAC,CAAC;EACF,MAAMC,kBAAkB,GAAGA,CAACC,OAAO,EAAEC,QAAQ,KAAK;IAChD,IAAIC,YAAY,GAAGT,MAAM,CAACU,YAAY,CAAC,CAAC,GAAGH,OAAO,CAACI,aAAa,CAAC,2BAA2B,CAAC,GAAGJ,OAAO,CAACI,aAAa,CAAC,0BAA0B,CAAC;IACjJ,IAAIC,WAAW,GAAGZ,MAAM,CAACU,YAAY,CAAC,CAAC,GAAGH,OAAO,CAACI,aAAa,CAAC,4BAA4B,CAAC,GAAGJ,OAAO,CAACI,aAAa,CAAC,6BAA6B,CAAC;IACpJ,IAAI,CAACF,YAAY,EAAE;MACjBA,YAAY,GAAGpB,YAAY,CAAC,MAAM,EAAEkB,OAAO,EAAEP,MAAM,CAACU,YAAY,CAAC,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC;IACtF;IACA,IAAI,CAACE,WAAW,EAAE;MAChBA,WAAW,GAAGvB,YAAY,CAAC,MAAM,EAAEkB,OAAO,EAAEP,MAAM,CAACU,YAAY,CAAC,CAAC,GAAG,OAAO,GAAG,QAAQ,CAAC;IACzF;IACA,IAAID,YAAY,EAAEA,YAAY,CAACI,KAAK,CAACC,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACR,QAAQ,EAAE,CAAC,CAAC;IACrE,IAAII,WAAW,EAAEA,WAAW,CAACC,KAAK,CAACC,OAAO,GAAGC,IAAI,CAACC,GAAG,CAACR,QAAQ,EAAE,CAAC,CAAC;EACpE,CAAC;EACD,MAAMS,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACAjB,MAAM,CAACkB,MAAM,CAACf,UAAU;IACxBH,MAAM,CAACmB,MAAM,CAACC,OAAO,CAACb,OAAO,IAAI;MAC/B,IAAIC,QAAQ,GAAGD,OAAO,CAACC,QAAQ;MAC/B,IAAIR,MAAM,CAACkB,MAAM,CAACf,UAAU,CAACE,aAAa,EAAE;QAC1CG,QAAQ,GAAGO,IAAI,CAACC,GAAG,CAACD,IAAI,CAACM,GAAG,CAACd,OAAO,CAACC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACxD;MACAF,kBAAkB,CAACC,OAAO,EAAEC,QAAQ,CAAC;IACvC,CAAC,CAAC;EACJ,CAAC;EACD,MAAMc,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAM;MACJH,MAAM;MACNI,YAAY,EAAEC;IAChB,CAAC,GAAGxB,MAAM;IACV,MAAMkB,MAAM,GAAGlB,MAAM,CAACkB,MAAM,CAACf,UAAU;IACvC,MAAMsB,SAAS,GAAG5B,YAAY,CAACG,MAAM,CAAC;IACtC,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,MAAM,CAACQ,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACzC,MAAMnB,OAAO,GAAGY,MAAM,CAACO,CAAC,CAAC;MACzB,IAAIlB,QAAQ,GAAGD,OAAO,CAACC,QAAQ;MAC/B,IAAIR,MAAM,CAACkB,MAAM,CAACf,UAAU,CAACE,aAAa,EAAE;QAC1CG,QAAQ,GAAGO,IAAI,CAACC,GAAG,CAACD,IAAI,CAACM,GAAG,CAACd,OAAO,CAACC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACxD;MACA,MAAMoB,MAAM,GAAGrB,OAAO,CAACsB,iBAAiB;MACxC,MAAMC,MAAM,GAAG,CAAC,GAAG,GAAGtB,QAAQ;MAC9B,IAAIuB,OAAO,GAAGD,MAAM;MACpB,IAAIE,OAAO,GAAG,CAAC;MACf,IAAIC,EAAE,GAAGjC,MAAM,CAACkB,MAAM,CAACgB,OAAO,GAAG,CAACN,MAAM,GAAG5B,MAAM,CAACmC,SAAS,GAAG,CAACP,MAAM;MACrE,IAAIQ,EAAE,GAAG,CAAC;MACV,IAAI,CAACpC,MAAM,CAACU,YAAY,CAAC,CAAC,EAAE;QAC1B0B,EAAE,GAAGH,EAAE;QACPA,EAAE,GAAG,CAAC;QACND,OAAO,GAAG,CAACD,OAAO;QAClBA,OAAO,GAAG,CAAC;MACb,CAAC,MAAM,IAAIP,GAAG,EAAE;QACdO,OAAO,GAAG,CAACA,OAAO;MACpB;MACAxB,OAAO,CAACM,KAAK,CAACwB,MAAM,GAAG,CAACtB,IAAI,CAACuB,GAAG,CAACvB,IAAI,CAACwB,KAAK,CAAC/B,QAAQ,CAAC,CAAC,GAAGW,MAAM,CAACQ,MAAM;MACtE,IAAIT,MAAM,CAACd,YAAY,EAAE;QACvBE,kBAAkB,CAACC,OAAO,EAAEC,QAAQ,CAAC;MACvC;MACA,MAAMgC,SAAS,GAAG,eAAeP,EAAE,OAAOG,EAAE,oBAAoBX,SAAS,CAACO,OAAO,CAAC,gBAAgBP,SAAS,CAACM,OAAO,CAAC,MAAM;MAC1H,MAAMU,QAAQ,GAAGjD,YAAY,CAAC0B,MAAM,EAAEX,OAAO,CAAC;MAC9CkC,QAAQ,CAAC5B,KAAK,CAAC2B,SAAS,GAAGA,SAAS;IACtC;EACF,CAAC;EACD,MAAME,aAAa,GAAGC,QAAQ,IAAI;IAChC,MAAMC,iBAAiB,GAAG5C,MAAM,CAACmB,MAAM,CAAC0B,GAAG,CAACtC,OAAO,IAAIZ,mBAAmB,CAACY,OAAO,CAAC,CAAC;IACpFqC,iBAAiB,CAACxB,OAAO,CAAC0B,EAAE,IAAI;MAC9BA,EAAE,CAACjC,KAAK,CAACkC,kBAAkB,GAAG,GAAGJ,QAAQ,IAAI;MAC7CG,EAAE,CAACE,gBAAgB,CAAC,8GAA8G,CAAC,CAAC5B,OAAO,CAAC6B,QAAQ,IAAI;QACtJA,QAAQ,CAACpC,KAAK,CAACkC,kBAAkB,GAAG,GAAGJ,QAAQ,IAAI;MACrD,CAAC,CAAC;IACJ,CAAC,CAAC;IACFlD,0BAA0B,CAAC;MACzBO,MAAM;MACN2C,QAAQ;MACRC;IACF,CAAC,CAAC;EACJ,CAAC;EACDrD,UAAU,CAAC;IACT2D,MAAM,EAAE,MAAM;IACdlD,MAAM;IACNE,EAAE;IACFoB,YAAY;IACZoB,aAAa;IACbzB,eAAe;IACfkC,eAAe,EAAEA,CAAA,KAAMnD,MAAM,CAACkB,MAAM,CAACf,UAAU;IAC/CiD,WAAW,EAAEA,CAAA,KAAM,IAAI;IACvBC,eAAe,EAAEA,CAAA,MAAO;MACtBC,aAAa,EAAE,CAAC;MAChBC,cAAc,EAAE,CAAC;MACjBC,mBAAmB,EAAE,IAAI;MACzBC,YAAY,EAAE,CAAC;MACfC,gBAAgB,EAAE,CAAC1D,MAAM,CAACkB,MAAM,CAACgB;IACnC,CAAC;EACH,CAAC,CAAC;AACJ;AAEA,SAASpC,UAAU,IAAI6D,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}