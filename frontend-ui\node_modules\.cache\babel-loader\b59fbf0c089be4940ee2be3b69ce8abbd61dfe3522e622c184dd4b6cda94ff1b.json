{"ast": null, "code": "/*\nimport React from \"react\";\nimport { Render } from \"@measured/puck\";\nimport config from \"./puck-config\"; // Create this file with the same components as your editor\n\n// Simple version - use Puck's built-in Render component\nconst PuckRenderer = ({ data }) => {\n  if (!data) return null;\n\n  console.log(\"Rendering Puck data:\", data);\n\n  return <Render config={config} data={data} />;\n};\n\nexport default PuckRenderer;\n*/", "map": {"version": 3, "names": [], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/components/PuckRenderer.jsx"], "sourcesContent": ["/*\nimport React from \"react\";\nimport { Render } from \"@measured/puck\";\nimport config from \"./puck-config\"; // Create this file with the same components as your editor\n\n// Simple version - use Puck's built-in Render component\nconst PuckRenderer = ({ data }) => {\n  if (!data) return null;\n\n  console.log(\"Rendering Puck data:\", data);\n\n  return <Render config={config} data={data} />;\n};\n\nexport default PuckRenderer;\n*/\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}