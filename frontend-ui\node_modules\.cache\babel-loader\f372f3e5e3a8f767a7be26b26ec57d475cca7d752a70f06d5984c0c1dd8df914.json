{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\LaravelReact\\\\frontend-ui\\\\src\\\\components\\\\PageRenderer\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport DynamicPage from \"../DynamicPage\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PageRenderer = () => {\n  _s();\n  const {\n    slug\n  } = useParams();\n\n  // Pass the slug to the DynamicPage component\n  return /*#__PURE__*/_jsxDEV(DynamicPage, {\n    slug: slug\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 10\n  }, this);\n};\n_s(PageRenderer, \"DpOdpe+T7d3Ytb7f6neHj0L13w0=\", false, function () {\n  return [useParams];\n});\n_c = PageRenderer;\nexport default PageRenderer;\nvar _c;\n$RefreshReg$(_c, \"PageRenderer\");", "map": {"version": 3, "names": ["React", "useParams", "DynamicPage", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON>", "_s", "slug", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/components/PageRenderer/index.jsx"], "sourcesContent": ["import React from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport DynamicPage from \"../DynamicPage\";\n\nconst PageRenderer = () => {\n  const { slug } = useParams();\n\n  // Pass the slug to the DynamicPage component\n  return <DynamicPage slug={slug} />;\n};\n\nexport default PageRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,WAAW,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAK,CAAC,GAAGN,SAAS,CAAC,CAAC;;EAE5B;EACA,oBAAOG,OAAA,CAACF,WAAW;IAACK,IAAI,EAAEA;EAAK;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACpC,CAAC;AAACL,EAAA,CALID,YAAY;EAAA,QACCJ,SAAS;AAAA;AAAAW,EAAA,GADtBP,YAAY;AAOlB,eAAeA,YAAY;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}