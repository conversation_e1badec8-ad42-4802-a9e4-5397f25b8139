{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\LaravelReact\\\\frontend-ui\\\\src\\\\components\\\\GoogleMap.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GoogleMap = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"map-area\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"maps\",\n      children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n        src: \"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d125080.73713749202!2d104.74787994335937!3d11.568121800000014!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3109519fe4077d69%3A0x20138e822e434660!2sRoyal%20University%20of%20Phnom%20Penh!5e0!3m2!1sen!2skh!4v1732191511330!5m2!1sen!2skh\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 9\n  }, this);\n};\n_c = GoogleMap;\nexport default GoogleMap;\nvar _c;\n$RefreshReg$(_c, \"GoogleMap\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "GoogleMap", "className", "children", "src", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/components/GoogleMap.jsx"], "sourcesContent": ["import React from 'react';\n\nconst GoogleMap = () => {\n    return (\n        <div className='map-area'>\n            <div className=\"maps\">\n                <iframe src=\"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d125080.73713749202!2d104.74787994335937!3d11.568121800000014!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3109519fe4077d69%3A0x20138e822e434660!2sRoyal%20University%20of%20Phnom%20Penh!5e0!3m2!1sen!2skh!4v1732191511330!5m2!1sen!2skh\"></iframe>\n            </div>\n        </div>\n    )\n}\n\nexport default GoogleMap;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACpB,oBACID,OAAA;IAAKE,SAAS,EAAC,UAAU;IAAAC,QAAA,eACrBH,OAAA;MAAKE,SAAS,EAAC,MAAM;MAAAC,QAAA,eACjBH,OAAA;QAAQI,GAAG,EAAC;MAAuS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5T;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAC,EAAA,GARKR,SAAS;AAUf,eAAeA,SAAS;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}