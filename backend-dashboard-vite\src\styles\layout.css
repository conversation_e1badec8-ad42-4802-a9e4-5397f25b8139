/* Layout */
.app-wrapper {
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fa;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  min-height: 100vh;
}

/* Header */
.header {
  height: 64px;
  background-color: #fff;
  border-bottom: 1px solid #e5e9f2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1.5rem;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
}

.menu-trigger {
  display: none;
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.theme-toggle {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0.5rem;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.user-menu:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.user-avatar {
  width: 32px;
  height: 32px;
  background-color: #556ee6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 0.875rem;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #2a3042;
}

.user-role {
  font-size: 0.75rem;
  color: #6c757d;
}

/* Content Area */
.content-area {
  flex: 1;
  padding: 1.5rem;
  background-color: #f8f9fa;
}

/* Responsive */
@media (max-width: 768px) {
  .menu-trigger {
    display: block;
  }

  .header {
    padding: 0 1rem;
  }

  .user-info {
    display: none;
  }

  .content-area {
    padding: 1rem;
  }
} 