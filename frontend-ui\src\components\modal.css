.modalCard {
  /* height: 100vh; */
  justify-content: center;
  align-items: center;
  display: flex;
  background-color: #fff;
}

.launch {
  height: 50px;
}

.close {
  font-size: 21px;
  cursor: pointer;
}

.modal-body {
  min-height: 450px;
  max-height: calc(90vh - 100px);
  overflow-y: auto;
}

.nav-tabs {
  border: none !important;
}

.nav-tabs .nav-link.active {
  color: #495057;
  background-color: #fff;
  border-color: #ffffff #ffffff #fff;
  border-top: 3px solid blue !important;
}

.nav-tabs .nav-link {
  margin-bottom: -1px;
  border: 1px solid transparent;
  border-top-left-radius: 0rem;
  border-top-right-radius: 0rem;
  border-top: 3px solid #eee;
  font-size: 20px;
}

.nav-tabs .nav-link:hover {
  border-color: #e9ecef #ffffff #ffffff;
}

.nav-tabs {
  display: table !important;
  width: 100%;
}

.nav-item {
  display: table-cell;
}

.payment-icon {
  display: inline-block;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-weight: 600;
  font-size: 16px;
  color: #495057;
  text-align: center;
  width: 80px;
}

.qr-placeholder {
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.form-control {
  border-bottom: 1px solid #eee !important;
  border: none;
  font-weight: 600;
}

.form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #8bbafe;
  outline: 0;
  box-shadow: none;
}

.inputbox {
  position: relative;
  margin-bottom: 20px;
  width: 100%;
}

.inputbox span {
  position: absolute;
  top: 7px;
  left: 11px;
  transition: 0.5s;
}

.inputbox i {
  position: absolute;
  top: 13px;
  right: 8px;
  transition: 0.5s;
  color: #3f51b5;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.inputbox input:focus ~ span {
  transform: translateX(-0px) translateY(-15px);
  font-size: 12px;
}

.inputbox input:valid ~ span {
  transform: translateX(-0px) translateY(-15px);
  font-size: 12px;
}

.pay button {
  height: 47px;
  border-radius: 37px;
}

.p-Disclaimer {
  font-size: 14px;
}

/* Guest form styles */
.guest-form {
  padding: 0 15px;
}

.guest-form label {
  font-weight: 500;
  margin-bottom: 5px;
}

.guest-form .form-control {
  border: 1px solid #ced4da !important;
  padding: 10px;
  height: auto;
  font-weight: 400;
}

/* Enhanced modal scrolling */
.modal-dialog {
  max-height: 95vh;
  overflow-y: auto;
}

.modal-content {
  max-height: 90vh;
  overflow-y: auto;
  scroll-behavior: smooth;
}

/* Ensure PayPal buttons are properly contained */
#paypal-button-container {
  position: relative !important;
  z-index: 1 !important;
}

/* Form validation styles */
.form-control.is-valid {
  border-color: #28a745;
}

.form-control.is-invalid {
  border-color: #dc3545;
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #dc3545;
}

.valid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #28a745;
}

/* PayPal validation warning */
.alert-warning {
  background-color: #fff3cd;
  border-color: #ffeaa7;
  color: #856404;
  padding: 1rem;
  border-radius: 0.375rem;
  border: 1px solid transparent;
}

.guest-form .form-control:focus {
  border-color: #80bdff !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.alert-danger {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border-radius: 0.25rem;
}
