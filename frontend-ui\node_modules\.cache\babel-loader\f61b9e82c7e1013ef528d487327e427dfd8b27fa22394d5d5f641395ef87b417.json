{"ast": null, "code": "export { default as useUncontrolled, useUncontrolledProp } from './hook';\nexport { default as uncontrollable } from './uncontrollable';", "map": {"version": 3, "names": ["default", "useUncontrolled", "useUncontrolledProp", "uncontrollable"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/uncontrollable/lib/esm/index.js"], "sourcesContent": ["export { default as useUncontrolled, useUncontrolledProp } from './hook';\nexport { default as uncontrollable } from './uncontrollable';"], "mappings": "AAAA,SAASA,OAAO,IAAIC,eAAe,EAAEC,mBAAmB,QAAQ,QAAQ;AACxE,SAASF,OAAO,IAAIG,cAAc,QAAQ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}