{"ast": null, "code": "export const scrollToTop = () => {\n  window.scrollTo({\n    top: 0,\n    behavior: \"smooth\"\n  });\n};", "map": {"version": 3, "names": ["scrollToTop", "window", "scrollTo", "top", "behavior"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/utilis/scrollToTop.js"], "sourcesContent": ["export const scrollToTop = () => {\n  window.scrollTo({\n    top: 0,\n    behavior: \"smooth\",\n  });\n};\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAGA,CAAA,KAAM;EAC/BC,MAAM,CAACC,QAAQ,CAAC;IACdC,GAAG,EAAE,CAAC;IACNC,QAAQ,EAAE;EACZ,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}