{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nconst DropdownContext = /*#__PURE__*/React.createContext({});\nDropdownContext.displayName = 'DropdownContext';\nexport default DropdownContext;", "map": {"version": 3, "names": ["React", "DropdownContext", "createContext", "displayName"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/react-bootstrap/esm/DropdownContext.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nconst DropdownContext = /*#__PURE__*/React.createContext({});\nDropdownContext.displayName = 'DropdownContext';\nexport default DropdownContext;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,eAAe,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AAC5DD,eAAe,CAACE,WAAW,GAAG,iBAAiB;AAC/C,eAAeF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}