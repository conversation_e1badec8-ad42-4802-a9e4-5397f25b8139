{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport divWithClassName from './divWithClassName';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst OffcanvasTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'offcanvas-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nOffcanvasTitle.displayName = 'OffcanvasTitle';\nexport default OffcanvasTitle;", "map": {"version": 3, "names": ["React", "classNames", "divWithClassName", "useBootstrapPrefix", "jsx", "_jsx", "DivStyledAsH5", "OffcanvasTitle", "forwardRef", "className", "bsPrefix", "as", "Component", "props", "ref", "displayName"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/react-bootstrap/esm/OffcanvasTitle.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport divWithClassName from './divWithClassName';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst OffcanvasTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'offcanvas-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nOffcanvasTitle.displayName = 'OffcanvasTitle';\nexport default OffcanvasTitle;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,aAAa,GAAGJ,gBAAgB,CAAC,IAAI,CAAC;AAC5C,MAAMK,cAAc,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAAC;EACpDC,SAAS;EACTC,QAAQ;EACRC,EAAE,EAAEC,SAAS,GAAGN,aAAa;EAC7B,GAAGO;AACL,CAAC,EAAEC,GAAG,KAAK;EACTJ,QAAQ,GAAGP,kBAAkB,CAACO,QAAQ,EAAE,iBAAiB,CAAC;EAC1D,OAAO,aAAaL,IAAI,CAACO,SAAS,EAAE;IAClCE,GAAG,EAAEA,GAAG;IACRL,SAAS,EAAER,UAAU,CAACQ,SAAS,EAAEC,QAAQ,CAAC;IAC1C,GAAGG;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,cAAc,CAACQ,WAAW,GAAG,gBAAgB;AAC7C,eAAeR,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}