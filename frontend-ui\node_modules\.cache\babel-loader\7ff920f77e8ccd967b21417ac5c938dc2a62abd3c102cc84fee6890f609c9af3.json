{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AlertLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = Anchor,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertLink.displayName = 'AlertLink';\nexport default AlertLink;", "map": {"version": 3, "names": ["React", "classNames", "<PERSON><PERSON>", "useBootstrapPrefix", "jsx", "_jsx", "AlertLink", "forwardRef", "className", "bsPrefix", "as", "Component", "props", "ref", "displayName"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/react-bootstrap/esm/AlertLink.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AlertLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = Anchor,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertLink.displayName = 'AlertLink';\nexport default AlertLink;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,MAAM,MAAM,oBAAoB;AACvC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,SAAS,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAC;EAC/CC,SAAS;EACTC,QAAQ;EACRC,EAAE,EAAEC,SAAS,GAAGT,MAAM;EACtB,GAAGU;AACL,CAAC,EAAEC,GAAG,KAAK;EACTJ,QAAQ,GAAGN,kBAAkB,CAACM,QAAQ,EAAE,YAAY,CAAC;EACrD,OAAO,aAAaJ,IAAI,CAACM,SAAS,EAAE;IAClCE,GAAG,EAAEA,GAAG;IACRL,SAAS,EAAEP,UAAU,CAACO,SAAS,EAAEC,QAAQ,CAAC;IAC1C,GAAGG;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,SAAS,CAACQ,WAAW,GAAG,WAAW;AACnC,eAAeR,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}