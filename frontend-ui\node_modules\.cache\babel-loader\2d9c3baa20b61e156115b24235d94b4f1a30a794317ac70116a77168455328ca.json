{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\LaravelReact\\\\frontend-ui\\\\src\\\\components\\\\CartToast.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CartToast = ({\n  message,\n  type = 'success',\n  duration = 3000,\n  onClose\n}) => {\n  _s();\n  const [isVisible, setIsVisible] = useState(true);\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setIsVisible(false);\n      setTimeout(onClose, 300); // Wait for fade out animation\n    }, duration);\n    return () => clearTimeout(timer);\n  }, [duration, onClose]);\n  const getToastClass = () => {\n    const baseClass = 'cart-toast';\n    const typeClass = type === 'success' ? 'cart-toast-success' : 'cart-toast-error';\n    const visibilityClass = isVisible ? 'cart-toast-visible' : 'cart-toast-hidden';\n    return `${baseClass} ${typeClass} ${visibilityClass}`;\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n          .cart-toast {\n            position: fixed;\n            top: 20px;\n            right: 20px;\n            z-index: 10000;\n            padding: 12px 20px;\n            border-radius: 8px;\n            color: white;\n            font-weight: 500;\n            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n            transition: all 0.3s ease;\n            max-width: 300px;\n            display: flex;\n            align-items: center;\n            gap: 8px;\n          }\n          \n          .cart-toast-success {\n            background-color: #28a745;\n          }\n          \n          .cart-toast-error {\n            background-color: #dc3545;\n          }\n          \n          .cart-toast-visible {\n            opacity: 1;\n            transform: translateX(0);\n          }\n          \n          .cart-toast-hidden {\n            opacity: 0;\n            transform: translateX(100%);\n          }\n          \n          .cart-toast-icon {\n            font-size: 1.2em;\n          }\n          \n          .cart-toast-close {\n            background: none;\n            border: none;\n            color: white;\n            font-size: 1.2em;\n            cursor: pointer;\n            margin-left: auto;\n            padding: 0;\n            opacity: 0.8;\n          }\n          \n          .cart-toast-close:hover {\n            opacity: 1;\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: getToastClass(),\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"cart-toast-icon\",\n        children: type === 'success' ? '✓' : '⚠'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"cart-toast-message\",\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"cart-toast-close\",\n        onClick: () => {\n          setIsVisible(false);\n          setTimeout(onClose, 300);\n        },\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(CartToast, \"m22S9IQwDfEe/fCJY7LYj8YPDMo=\");\n_c = CartToast;\nexport default CartToast;\nvar _c;\n$RefreshReg$(_c, \"CartToast\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CartToast", "message", "type", "duration", "onClose", "_s", "isVisible", "setIsVisible", "timer", "setTimeout", "clearTimeout", "getToastClass", "baseClass", "typeClass", "visibilityClass", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/components/CartToast.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst CartToast = ({ message, type = 'success', duration = 3000, onClose }) => {\n  const [isVisible, setIsVisible] = useState(true);\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setIsVisible(false);\n      setTimeout(onClose, 300); // Wait for fade out animation\n    }, duration);\n\n    return () => clearTimeout(timer);\n  }, [duration, onClose]);\n\n  const getToastClass = () => {\n    const baseClass = 'cart-toast';\n    const typeClass = type === 'success' ? 'cart-toast-success' : 'cart-toast-error';\n    const visibilityClass = isVisible ? 'cart-toast-visible' : 'cart-toast-hidden';\n    return `${baseClass} ${typeClass} ${visibilityClass}`;\n  };\n\n  return (\n    <>\n      <style>\n        {`\n          .cart-toast {\n            position: fixed;\n            top: 20px;\n            right: 20px;\n            z-index: 10000;\n            padding: 12px 20px;\n            border-radius: 8px;\n            color: white;\n            font-weight: 500;\n            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n            transition: all 0.3s ease;\n            max-width: 300px;\n            display: flex;\n            align-items: center;\n            gap: 8px;\n          }\n          \n          .cart-toast-success {\n            background-color: #28a745;\n          }\n          \n          .cart-toast-error {\n            background-color: #dc3545;\n          }\n          \n          .cart-toast-visible {\n            opacity: 1;\n            transform: translateX(0);\n          }\n          \n          .cart-toast-hidden {\n            opacity: 0;\n            transform: translateX(100%);\n          }\n          \n          .cart-toast-icon {\n            font-size: 1.2em;\n          }\n          \n          .cart-toast-close {\n            background: none;\n            border: none;\n            color: white;\n            font-size: 1.2em;\n            cursor: pointer;\n            margin-left: auto;\n            padding: 0;\n            opacity: 0.8;\n          }\n          \n          .cart-toast-close:hover {\n            opacity: 1;\n          }\n        `}\n      </style>\n      \n      <div className={getToastClass()}>\n        <span className=\"cart-toast-icon\">\n          {type === 'success' ? '✓' : '⚠'}\n        </span>\n        <span className=\"cart-toast-message\">{message}</span>\n        <button \n          className=\"cart-toast-close\" \n          onClick={() => {\n            setIsVisible(false);\n            setTimeout(onClose, 300);\n          }}\n        >\n          ×\n        </button>\n      </div>\n    </>\n  );\n};\n\nexport default CartToast;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnD,MAAMC,SAAS,GAAGA,CAAC;EAAEC,OAAO;EAAEC,IAAI,GAAG,SAAS;EAAEC,QAAQ,GAAG,IAAI;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC7E,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACd,MAAMa,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BF,YAAY,CAAC,KAAK,CAAC;MACnBE,UAAU,CAACL,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAED,QAAQ,CAAC;IAEZ,OAAO,MAAMO,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,CAACL,QAAQ,EAAEC,OAAO,CAAC,CAAC;EAEvB,MAAMO,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,SAAS,GAAG,YAAY;IAC9B,MAAMC,SAAS,GAAGX,IAAI,KAAK,SAAS,GAAG,oBAAoB,GAAG,kBAAkB;IAChF,MAAMY,eAAe,GAAGR,SAAS,GAAG,oBAAoB,GAAG,mBAAmB;IAC9E,OAAO,GAAGM,SAAS,IAAIC,SAAS,IAAIC,eAAe,EAAE;EACvD,CAAC;EAED,oBACEjB,OAAA,CAAAE,SAAA;IAAAgB,QAAA,gBACElB,OAAA;MAAAkB,QAAA,EACG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAERtB,OAAA;MAAKuB,SAAS,EAAET,aAAa,CAAC,CAAE;MAAAI,QAAA,gBAC9BlB,OAAA;QAAMuB,SAAS,EAAC,iBAAiB;QAAAL,QAAA,EAC9Bb,IAAI,KAAK,SAAS,GAAG,GAAG,GAAG;MAAG;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eACPtB,OAAA;QAAMuB,SAAS,EAAC,oBAAoB;QAAAL,QAAA,EAAEd;MAAO;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACrDtB,OAAA;QACEuB,SAAS,EAAC,kBAAkB;QAC5BC,OAAO,EAAEA,CAAA,KAAM;UACbd,YAAY,CAAC,KAAK,CAAC;UACnBE,UAAU,CAACL,OAAO,EAAE,GAAG,CAAC;QAC1B,CAAE;QAAAW,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACd,EAAA,CAhGIL,SAAS;AAAAsB,EAAA,GAATtB,SAAS;AAkGf,eAAeA,SAAS;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}