{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\LaravelReact\\\\frontend-ui\\\\src\\\\App.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Outlet } from \"react-router-dom\";\nimport NavItems from \"./components/NavItems\";\nimport Footer from \"./components/Footer\";\nimport CartToast from \"./components/CartToast\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst App = () => {\n  _s();\n  const [toasts, setToasts] = useState([]);\n  useEffect(() => {\n    let toastId = 0;\n    const handleShowToast = event => {\n      const {\n        message,\n        type = 'success',\n        duration = 3000\n      } = event.detail;\n      const id = ++toastId;\n      const newToast = {\n        id,\n        message,\n        type,\n        duration\n      };\n      setToasts(prev => [...prev, newToast]);\n\n      // Auto remove toast\n      setTimeout(() => {\n        setToasts(prev => prev.filter(toast => toast.id !== id));\n      }, duration + 300);\n    };\n    window.addEventListener('showToast', handleShowToast);\n    return () => {\n      window.removeEventListener('showToast', handleShowToast);\n    };\n  }, []);\n  const removeToast = id => {\n    setToasts(prev => prev.filter(toast => toast.id !== id));\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(NavItems, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), toasts.map((toast, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: `${20 + index * 70}px`,\n        right: '20px',\n        zIndex: 10000 + index\n      },\n      children: /*#__PURE__*/_jsxDEV(CartToast, {\n        message: toast.message,\n        type: toast.type,\n        duration: toast.duration,\n        onClose: () => removeToast(toast.id)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 11\n      }, this)\n    }, toast.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 9\n    }, this))]\n  }, void 0, true);\n};\n_s(App, \"oL0MrtDCqig+amxuKH2EOlnBcjg=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Outlet", "NavItems", "Footer", "CartToast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "toasts", "setToasts", "toastId", "handleShowToast", "event", "message", "type", "duration", "detail", "id", "newToast", "prev", "setTimeout", "filter", "toast", "window", "addEventListener", "removeEventListener", "removeToast", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "index", "style", "position", "top", "right", "zIndex", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/App.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { Outlet } from \"react-router-dom\";\nimport NavItems from \"./components/NavItems\";\nimport Footer from \"./components/Footer\";\nimport CartToast from \"./components/CartToast\";\n\nconst App = () => {\n  const [toasts, setToasts] = useState([]);\n\n  useEffect(() => {\n    let toastId = 0;\n\n    const handleShowToast = (event) => {\n      const { message, type = 'success', duration = 3000 } = event.detail;\n      const id = ++toastId;\n\n      const newToast = { id, message, type, duration };\n      setToasts(prev => [...prev, newToast]);\n\n      // Auto remove toast\n      setTimeout(() => {\n        setToasts(prev => prev.filter(toast => toast.id !== id));\n      }, duration + 300);\n    };\n\n    window.addEventListener('showToast', handleShowToast);\n\n    return () => {\n      window.removeEventListener('showToast', handleShowToast);\n    };\n  }, []);\n\n  const removeToast = (id) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id));\n  };\n\n  return (\n    <>\n      <NavItems />\n      <Outlet />\n      <Footer />\n\n      {/* Toast Container */}\n      {toasts.map((toast, index) => (\n        <div\n          key={toast.id}\n          style={{\n            position: 'fixed',\n            top: `${20 + (index * 70)}px`,\n            right: '20px',\n            zIndex: 10000 + index\n          }}\n        >\n          <CartToast\n            message={toast.message}\n            type={toast.type}\n            duration={toast.duration}\n            onClose={() => removeToast(toast.id)}\n          />\n        </div>\n      ))}\n    </>\n  );\n};\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,kBAAkB;AACzC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,SAAS,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAMC,GAAG,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAExCC,SAAS,CAAC,MAAM;IACd,IAAIa,OAAO,GAAG,CAAC;IAEf,MAAMC,eAAe,GAAIC,KAAK,IAAK;MACjC,MAAM;QAAEC,OAAO;QAAEC,IAAI,GAAG,SAAS;QAAEC,QAAQ,GAAG;MAAK,CAAC,GAAGH,KAAK,CAACI,MAAM;MACnE,MAAMC,EAAE,GAAG,EAAEP,OAAO;MAEpB,MAAMQ,QAAQ,GAAG;QAAED,EAAE;QAAEJ,OAAO;QAAEC,IAAI;QAAEC;MAAS,CAAC;MAChDN,SAAS,CAACU,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,QAAQ,CAAC,CAAC;;MAEtC;MACAE,UAAU,CAAC,MAAM;QACfX,SAAS,CAACU,IAAI,IAAIA,IAAI,CAACE,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACL,EAAE,KAAKA,EAAE,CAAC,CAAC;MAC1D,CAAC,EAAEF,QAAQ,GAAG,GAAG,CAAC;IACpB,CAAC;IAEDQ,MAAM,CAACC,gBAAgB,CAAC,WAAW,EAAEb,eAAe,CAAC;IAErD,OAAO,MAAM;MACXY,MAAM,CAACE,mBAAmB,CAAC,WAAW,EAAEd,eAAe,CAAC;IAC1D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMe,WAAW,GAAIT,EAAE,IAAK;IAC1BR,SAAS,CAACU,IAAI,IAAIA,IAAI,CAACE,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACL,EAAE,KAAKA,EAAE,CAAC,CAAC;EAC1D,CAAC;EAED,oBACEd,OAAA,CAAAE,SAAA;IAAAsB,QAAA,gBACExB,OAAA,CAACJ,QAAQ;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACZ5B,OAAA,CAACL,MAAM;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACV5B,OAAA,CAACH,MAAM;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGTvB,MAAM,CAACwB,GAAG,CAAC,CAACV,KAAK,EAAEW,KAAK,kBACvB9B,OAAA;MAEE+B,KAAK,EAAE;QACLC,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,GAAG,EAAE,GAAIH,KAAK,GAAG,EAAG,IAAI;QAC7BI,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,KAAK,GAAGL;MAClB,CAAE;MAAAN,QAAA,eAEFxB,OAAA,CAACF,SAAS;QACRY,OAAO,EAAES,KAAK,CAACT,OAAQ;QACvBC,IAAI,EAAEQ,KAAK,CAACR,IAAK;QACjBC,QAAQ,EAAEO,KAAK,CAACP,QAAS;QACzBwB,OAAO,EAAEA,CAAA,KAAMb,WAAW,CAACJ,KAAK,CAACL,EAAE;MAAE;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC;IAAC,GAbGT,KAAK,CAACL,EAAE;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAcV,CACN,CAAC;EAAA,eACF,CAAC;AAEP,CAAC;AAACxB,EAAA,CAzDID,GAAG;AAAAkC,EAAA,GAAHlC,GAAG;AA2DT,eAAeA,GAAG;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}