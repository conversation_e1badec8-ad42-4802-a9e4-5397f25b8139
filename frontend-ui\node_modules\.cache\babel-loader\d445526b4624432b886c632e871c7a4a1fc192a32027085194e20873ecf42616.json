{"ast": null, "code": "import * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport useCallbackRef from '@restart/hooks/useCallbackRef';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport { useState } from 'react';\nimport usePopper from './usePopper';\nimport useRootClose from './useRootClose';\nimport useWaitForDOMRef from './useWaitForDOMRef';\nimport mergeOptionsWithPopperConfig from './mergeOptionsWithPopperConfig';\nimport { renderTransition } from './ImperativeTransition';\n/**\n * Built on top of `Popper.js`, the overlay component is\n * great for custom tooltip overlays.\n */\nconst Overlay = /*#__PURE__*/React.forwardRef((props, outerRef) => {\n  const {\n    flip,\n    offset,\n    placement,\n    containerPadding,\n    popperConfig = {},\n    transition: Transition,\n    runTransition\n  } = props;\n  const [rootElement, attachRef] = useCallbackRef();\n  const [arrowElement, attachArrowRef] = useCallbackRef();\n  const mergedRef = useMergedRefs(attachRef, outerRef);\n  const container = useWaitForDOMRef(props.container);\n  const target = useWaitForDOMRef(props.target);\n  const [exited, setExited] = useState(!props.show);\n  const popper = usePopper(target, rootElement, mergeOptionsWithPopperConfig({\n    placement,\n    enableEvents: !!props.show,\n    containerPadding: containerPadding || 5,\n    flip,\n    offset,\n    arrowElement,\n    popperConfig\n  }));\n\n  // TODO: I think this needs to be in an effect\n  if (props.show && exited) {\n    setExited(false);\n  }\n  const handleHidden = (...args) => {\n    setExited(true);\n    if (props.onExited) {\n      props.onExited(...args);\n    }\n  };\n\n  // Don't un-render the overlay while it's transitioning out.\n  const mountOverlay = props.show || !exited;\n  useRootClose(rootElement, props.onHide, {\n    disabled: !props.rootClose || props.rootCloseDisabled,\n    clickTrigger: props.rootCloseEvent\n  });\n  if (!mountOverlay) {\n    // Don't bother showing anything if we don't have to.\n    return null;\n  }\n  const {\n    onExit,\n    onExiting,\n    onEnter,\n    onEntering,\n    onEntered\n  } = props;\n  let child = props.children(Object.assign({}, popper.attributes.popper, {\n    style: popper.styles.popper,\n    ref: mergedRef\n  }), {\n    popper,\n    placement,\n    show: !!props.show,\n    arrowProps: Object.assign({}, popper.attributes.arrow, {\n      style: popper.styles.arrow,\n      ref: attachArrowRef\n    })\n  });\n  child = renderTransition(Transition, runTransition, {\n    in: !!props.show,\n    appear: true,\n    mountOnEnter: true,\n    unmountOnExit: true,\n    children: child,\n    onExit,\n    onExiting,\n    onExited: handleHidden,\n    onEnter,\n    onEntering,\n    onEntered\n  });\n  return container ? /*#__PURE__*/ReactDOM.createPortal(child, container) : null;\n});\nOverlay.displayName = 'Overlay';\nexport default Overlay;", "map": {"version": 3, "names": ["React", "ReactDOM", "useCallbackRef", "useMergedRefs", "useState", "usePopper", "useRootClose", "useWaitForDOMRef", "mergeOptionsWithPopperConfig", "renderTransition", "Overlay", "forwardRef", "props", "outerRef", "flip", "offset", "placement", "containerPadding", "popperConfig", "transition", "Transition", "runTransition", "rootElement", "attachRef", "arrowElement", "attachArrowRef", "mergedRef", "container", "target", "exited", "setExited", "show", "popper", "enableEvents", "handleHidden", "args", "onExited", "mountOverlay", "onHide", "disabled", "rootClose", "rootCloseDisabled", "clickTrigger", "rootCloseEvent", "onExit", "onExiting", "onEnter", "onEntering", "onEntered", "child", "children", "Object", "assign", "attributes", "style", "styles", "ref", "arrowProps", "arrow", "in", "appear", "mountOnEnter", "unmountOnExit", "createPortal", "displayName"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/@restart/ui/esm/Overlay.js"], "sourcesContent": ["import * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport useCallbackRef from '@restart/hooks/useCallbackRef';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport { useState } from 'react';\nimport usePopper from './usePopper';\nimport useRootClose from './useRootClose';\nimport useWaitForDOMRef from './useWaitForDOMRef';\nimport mergeOptionsWithPopperConfig from './mergeOptionsWithPopperConfig';\nimport { renderTransition } from './ImperativeTransition';\n/**\n * Built on top of `Popper.js`, the overlay component is\n * great for custom tooltip overlays.\n */\nconst Overlay = /*#__PURE__*/React.forwardRef((props, outerRef) => {\n  const {\n    flip,\n    offset,\n    placement,\n    containerPadding,\n    popperConfig = {},\n    transition: Transition,\n    runTransition\n  } = props;\n  const [rootElement, attachRef] = useCallbackRef();\n  const [arrowElement, attachArrowRef] = useCallbackRef();\n  const mergedRef = useMergedRefs(attachRef, outerRef);\n  const container = useWaitForDOMRef(props.container);\n  const target = useWaitForDOMRef(props.target);\n  const [exited, setExited] = useState(!props.show);\n  const popper = usePopper(target, rootElement, mergeOptionsWithPopperConfig({\n    placement,\n    enableEvents: !!props.show,\n    containerPadding: containerPadding || 5,\n    flip,\n    offset,\n    arrowElement,\n    popperConfig\n  }));\n\n  // TODO: I think this needs to be in an effect\n  if (props.show && exited) {\n    setExited(false);\n  }\n  const handleHidden = (...args) => {\n    setExited(true);\n    if (props.onExited) {\n      props.onExited(...args);\n    }\n  };\n\n  // Don't un-render the overlay while it's transitioning out.\n  const mountOverlay = props.show || !exited;\n  useRootClose(rootElement, props.onHide, {\n    disabled: !props.rootClose || props.rootCloseDisabled,\n    clickTrigger: props.rootCloseEvent\n  });\n  if (!mountOverlay) {\n    // Don't bother showing anything if we don't have to.\n    return null;\n  }\n  const {\n    onExit,\n    onExiting,\n    onEnter,\n    onEntering,\n    onEntered\n  } = props;\n  let child = props.children(Object.assign({}, popper.attributes.popper, {\n    style: popper.styles.popper,\n    ref: mergedRef\n  }), {\n    popper,\n    placement,\n    show: !!props.show,\n    arrowProps: Object.assign({}, popper.attributes.arrow, {\n      style: popper.styles.arrow,\n      ref: attachArrowRef\n    })\n  });\n  child = renderTransition(Transition, runTransition, {\n    in: !!props.show,\n    appear: true,\n    mountOnEnter: true,\n    unmountOnExit: true,\n    children: child,\n    onExit,\n    onExiting,\n    onExited: handleHidden,\n    onEnter,\n    onEntering,\n    onEntered\n  });\n  return container ? /*#__PURE__*/ReactDOM.createPortal(child, container) : null;\n});\nOverlay.displayName = 'Overlay';\nexport default Overlay;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,aAAa,MAAM,8BAA8B;AACxD,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,4BAA4B,MAAM,gCAAgC;AACzE,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD;AACA;AACA;AACA;AACA,MAAMC,OAAO,GAAG,aAAaV,KAAK,CAACW,UAAU,CAAC,CAACC,KAAK,EAAEC,QAAQ,KAAK;EACjE,MAAM;IACJC,IAAI;IACJC,MAAM;IACNC,SAAS;IACTC,gBAAgB;IAChBC,YAAY,GAAG,CAAC,CAAC;IACjBC,UAAU,EAAEC,UAAU;IACtBC;EACF,CAAC,GAAGT,KAAK;EACT,MAAM,CAACU,WAAW,EAAEC,SAAS,CAAC,GAAGrB,cAAc,CAAC,CAAC;EACjD,MAAM,CAACsB,YAAY,EAAEC,cAAc,CAAC,GAAGvB,cAAc,CAAC,CAAC;EACvD,MAAMwB,SAAS,GAAGvB,aAAa,CAACoB,SAAS,EAAEV,QAAQ,CAAC;EACpD,MAAMc,SAAS,GAAGpB,gBAAgB,CAACK,KAAK,CAACe,SAAS,CAAC;EACnD,MAAMC,MAAM,GAAGrB,gBAAgB,CAACK,KAAK,CAACgB,MAAM,CAAC;EAC7C,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC,CAACQ,KAAK,CAACmB,IAAI,CAAC;EACjD,MAAMC,MAAM,GAAG3B,SAAS,CAACuB,MAAM,EAAEN,WAAW,EAAEd,4BAA4B,CAAC;IACzEQ,SAAS;IACTiB,YAAY,EAAE,CAAC,CAACrB,KAAK,CAACmB,IAAI;IAC1Bd,gBAAgB,EAAEA,gBAAgB,IAAI,CAAC;IACvCH,IAAI;IACJC,MAAM;IACNS,YAAY;IACZN;EACF,CAAC,CAAC,CAAC;;EAEH;EACA,IAAIN,KAAK,CAACmB,IAAI,IAAIF,MAAM,EAAE;IACxBC,SAAS,CAAC,KAAK,CAAC;EAClB;EACA,MAAMI,YAAY,GAAGA,CAAC,GAAGC,IAAI,KAAK;IAChCL,SAAS,CAAC,IAAI,CAAC;IACf,IAAIlB,KAAK,CAACwB,QAAQ,EAAE;MAClBxB,KAAK,CAACwB,QAAQ,CAAC,GAAGD,IAAI,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAME,YAAY,GAAGzB,KAAK,CAACmB,IAAI,IAAI,CAACF,MAAM;EAC1CvB,YAAY,CAACgB,WAAW,EAAEV,KAAK,CAAC0B,MAAM,EAAE;IACtCC,QAAQ,EAAE,CAAC3B,KAAK,CAAC4B,SAAS,IAAI5B,KAAK,CAAC6B,iBAAiB;IACrDC,YAAY,EAAE9B,KAAK,CAAC+B;EACtB,CAAC,CAAC;EACF,IAAI,CAACN,YAAY,EAAE;IACjB;IACA,OAAO,IAAI;EACb;EACA,MAAM;IACJO,MAAM;IACNC,SAAS;IACTC,OAAO;IACPC,UAAU;IACVC;EACF,CAAC,GAAGpC,KAAK;EACT,IAAIqC,KAAK,GAAGrC,KAAK,CAACsC,QAAQ,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEpB,MAAM,CAACqB,UAAU,CAACrB,MAAM,EAAE;IACrEsB,KAAK,EAAEtB,MAAM,CAACuB,MAAM,CAACvB,MAAM;IAC3BwB,GAAG,EAAE9B;EACP,CAAC,CAAC,EAAE;IACFM,MAAM;IACNhB,SAAS;IACTe,IAAI,EAAE,CAAC,CAACnB,KAAK,CAACmB,IAAI;IAClB0B,UAAU,EAAEN,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEpB,MAAM,CAACqB,UAAU,CAACK,KAAK,EAAE;MACrDJ,KAAK,EAAEtB,MAAM,CAACuB,MAAM,CAACG,KAAK;MAC1BF,GAAG,EAAE/B;IACP,CAAC;EACH,CAAC,CAAC;EACFwB,KAAK,GAAGxC,gBAAgB,CAACW,UAAU,EAAEC,aAAa,EAAE;IAClDsC,EAAE,EAAE,CAAC,CAAC/C,KAAK,CAACmB,IAAI;IAChB6B,MAAM,EAAE,IAAI;IACZC,YAAY,EAAE,IAAI;IAClBC,aAAa,EAAE,IAAI;IACnBZ,QAAQ,EAAED,KAAK;IACfL,MAAM;IACNC,SAAS;IACTT,QAAQ,EAAEF,YAAY;IACtBY,OAAO;IACPC,UAAU;IACVC;EACF,CAAC,CAAC;EACF,OAAOrB,SAAS,GAAG,aAAa1B,QAAQ,CAAC8D,YAAY,CAACd,KAAK,EAAEtB,SAAS,CAAC,GAAG,IAAI;AAChF,CAAC,CAAC;AACFjB,OAAO,CAACsD,WAAW,GAAG,SAAS;AAC/B,eAAetD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}