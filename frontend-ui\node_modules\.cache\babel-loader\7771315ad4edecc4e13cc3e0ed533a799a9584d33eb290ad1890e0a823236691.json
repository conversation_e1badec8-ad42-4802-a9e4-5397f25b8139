{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport warning from 'warning';\nimport { useUncontrolled } from 'uncontrollable';\nimport BaseNav from '@restart/ui/Nav';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport ListGroupItem from './ListGroupItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ListGroup = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    className,\n    bsPrefix: initialBsPrefix,\n    variant,\n    horizontal,\n    numbered,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as = 'div',\n    ...controlledProps\n  } = useUncontrolled(props, {\n    activeKey: 'onSelect'\n  });\n  const bsPrefix = useBootstrapPrefix(initialBsPrefix, 'list-group');\n  let horizontalVariant;\n  if (horizontal) {\n    horizontalVariant = horizontal === true ? 'horizontal' : `horizontal-${horizontal}`;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(!(horizontal && variant === 'flush'), '`variant=\"flush\"` and `horizontal` should not be used together.') : void 0;\n  return /*#__PURE__*/_jsx(BaseNav, {\n    ref: ref,\n    ...controlledProps,\n    as: as,\n    className: classNames(className, bsPrefix, variant && `${bsPrefix}-${variant}`, horizontalVariant && `${bsPrefix}-${horizontalVariant}`, numbered && `${bsPrefix}-numbered`)\n  });\n});\nListGroup.displayName = 'ListGroup';\nexport default Object.assign(ListGroup, {\n  Item: ListGroupItem\n});", "map": {"version": 3, "names": ["classNames", "React", "warning", "useUncontrolled", "BaseNav", "useBootstrapPrefix", "ListGroupItem", "jsx", "_jsx", "ListGroup", "forwardRef", "props", "ref", "className", "bsPrefix", "initialBsPrefix", "variant", "horizontal", "numbered", "as", "controlledProps", "active<PERSON><PERSON>", "horizontalVariant", "process", "env", "NODE_ENV", "displayName", "Object", "assign", "<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/react-bootstrap/esm/ListGroup.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport warning from 'warning';\nimport { useUncontrolled } from 'uncontrollable';\nimport BaseNav from '@restart/ui/Nav';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport ListGroupItem from './ListGroupItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ListGroup = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    className,\n    bsPrefix: initialBsPrefix,\n    variant,\n    horizontal,\n    numbered,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as = 'div',\n    ...controlledProps\n  } = useUncontrolled(props, {\n    activeKey: 'onSelect'\n  });\n  const bsPrefix = useBootstrapPrefix(initialBsPrefix, 'list-group');\n  let horizontalVariant;\n  if (horizontal) {\n    horizontalVariant = horizontal === true ? 'horizontal' : `horizontal-${horizontal}`;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(!(horizontal && variant === 'flush'), '`variant=\"flush\"` and `horizontal` should not be used together.') : void 0;\n  return /*#__PURE__*/_jsx(BaseNav, {\n    ref: ref,\n    ...controlledProps,\n    as: as,\n    className: classNames(className, bsPrefix, variant && `${bsPrefix}-${variant}`, horizontalVariant && `${bsPrefix}-${horizontalVariant}`, numbered && `${bsPrefix}-numbered`)\n  });\n});\nListGroup.displayName = 'ListGroup';\nexport default Object.assign(ListGroup, {\n  Item: ListGroupItem\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,SAAS;AAC7B,SAASC,eAAe,QAAQ,gBAAgB;AAChD,OAAOC,OAAO,MAAM,iBAAiB;AACrC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,SAAS,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC9D,MAAM;IACJC,SAAS;IACTC,QAAQ,EAAEC,eAAe;IACzBC,OAAO;IACPC,UAAU;IACVC,QAAQ;IACR;IACAC,EAAE,GAAG,KAAK;IACV,GAAGC;EACL,CAAC,GAAGjB,eAAe,CAACQ,KAAK,EAAE;IACzBU,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAMP,QAAQ,GAAGT,kBAAkB,CAACU,eAAe,EAAE,YAAY,CAAC;EAClE,IAAIO,iBAAiB;EACrB,IAAIL,UAAU,EAAE;IACdK,iBAAiB,GAAGL,UAAU,KAAK,IAAI,GAAG,YAAY,GAAG,cAAcA,UAAU,EAAE;EACrF;EACAM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvB,OAAO,CAAC,EAAEe,UAAU,IAAID,OAAO,KAAK,OAAO,CAAC,EAAE,iEAAiE,CAAC,GAAG,KAAK,CAAC;EACjK,OAAO,aAAaR,IAAI,CAACJ,OAAO,EAAE;IAChCQ,GAAG,EAAEA,GAAG;IACR,GAAGQ,eAAe;IAClBD,EAAE,EAAEA,EAAE;IACNN,SAAS,EAAEb,UAAU,CAACa,SAAS,EAAEC,QAAQ,EAAEE,OAAO,IAAI,GAAGF,QAAQ,IAAIE,OAAO,EAAE,EAAEM,iBAAiB,IAAI,GAAGR,QAAQ,IAAIQ,iBAAiB,EAAE,EAAEJ,QAAQ,IAAI,GAAGJ,QAAQ,WAAW;EAC7K,CAAC,CAAC;AACJ,CAAC,CAAC;AACFL,SAAS,CAACiB,WAAW,GAAG,WAAW;AACnC,eAAeC,MAAM,CAACC,MAAM,CAACnB,SAAS,EAAE;EACtCoB,IAAI,EAAEvB;AACR,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}