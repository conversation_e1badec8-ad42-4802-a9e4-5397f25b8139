{"ast": null, "code": "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "map": {"version": 3, "names": ["getAltAxis", "axis"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/@popperjs/core/lib/utils/getAltAxis.js"], "sourcesContent": ["export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}"], "mappings": "AAAA,eAAe,SAASA,UAAUA,CAACC,IAAI,EAAE;EACvC,OAAOA,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}