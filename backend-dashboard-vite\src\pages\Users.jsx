import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from '../utils/axios';
import { toast } from 'react-toastify';
import Spinner from '../components/Spinner';

function Users() {
  const navigate = useNavigate();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [statsLoading, setStatsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [pagination, setPagination] = useState({
    currentPage: 1,
    perPage: 10,
    total: 0,
    lastPage: 1
  });
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    new: 0
  });

  // Fetch users with pagination
  useEffect(() => {
    let isMounted = true;
    
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/users', {
          params: {
            page: pagination.currentPage,
            per_page: pagination.perPage,
            search: searchQuery
          }
        });

        if (!isMounted) return;

        // Log response for debugging
        console.log('Users API Response:', response.data);

        // Extract data from response - handle nested structure
        let usersData = [];
        if (response.data?.data?.data && Array.isArray(response.data.data.data)) {
          usersData = response.data.data.data;
        } else if (response.data?.data && Array.isArray(response.data.data)) {
          usersData = response.data.data;
        }

        // Transform users data with error handling
        const transformedUsers = usersData.map((user, index) => {
          try {
            return {
              id: user?.UserID || user?.id || `temp-${index}`, // Fallback to index if no ID
              firstName: user?.FirstName || '',
              lastName: user?.LastName || '',
              email: user?.Email || '',
              phone: user?.Phone || '',
              role: (user?.Role || 'user').toLowerCase(),
              status: 'active', // Default to active since we don't have status in API
              created_at: user?.CreatedAt || new Date().toISOString()
            };
          } catch (error) {
            console.error('Error transforming user data:', error, user);
            return {
              id: `error-${index}`,
              firstName: 'Error',
              lastName: 'User',
              email: 'N/A',
              phone: 'N/A',
              role: 'user',
              status: 'inactive',
              created_at: new Date().toISOString()
            };
          }
        });

        if (isMounted) {
          setUsers(transformedUsers);
          
          // Update pagination with meta data
          const meta = response.data?.data || {};
          setPagination(prev => ({
            ...prev,
            total: meta.total || transformedUsers.length,
            lastPage: meta.last_page || Math.ceil(transformedUsers.length / prev.perPage),
            currentPage: meta.current_page || prev.currentPage,
            perPage: meta.per_page || prev.perPage
          }));

          // Calculate stats
          const statsUpdate = {
            total: transformedUsers.length,
            active: transformedUsers.length, // All users are considered active
            inactive: 0, // No inactive users in current API
            new: transformedUsers.filter(user => {
              const thirtyDaysAgo = new Date();
              thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
              return new Date(user.created_at) >= thirtyDaysAgo;
            }).length
          };

          setStats(statsUpdate);
        }
      } catch (error) {
        console.error('Error fetching users:', error);
        if (isMounted) {
          toast.error(error.response?.data?.message || 'Failed to fetch users. Please try again.');
          setUsers([]);
          setPagination(prev => ({
            ...prev,
            total: 0,
            lastPage: 1
          }));
          setStats({
            total: 0,
            active: 0,
            inactive: 0,
            new: 0
          });
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    const fetchStats = async () => {
      try {
        setStatsLoading(true);
        const statsResponse = await axios.get('/users/stats');
        
        if (!isMounted) return;

        console.log('Stats API Response:', statsResponse.data);

        if (statsResponse.data) {
          setStats({
            total: statsResponse.data.total || 0,
            active: statsResponse.data.active || 0,
            inactive: statsResponse.data.inactive || 0,
            new: statsResponse.data.new || 0
          });
        }
      } catch (error) {
        console.error('Error fetching user stats:', error);
        if (isMounted) {
          // Calculate stats from users data as fallback
          const total = users.length;
          const active = users.filter(user => user.status === 'active').length;
          const inactive = users.filter(user => user.status === 'inactive').length;
          const newUsers = users.filter(user => {
            try {
              const createdDate = new Date(user.created_at);
              const thirtyDaysAgo = new Date();
              thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
              return createdDate >= thirtyDaysAgo;
            } catch (error) {
              return false;
            }
          }).length;

          setStats({
            total,
            active,
            inactive,
            new: newUsers
          });
        }
      } finally {
        if (isMounted) {
          setStatsLoading(false);
        }
      }
    };

    const debounceTimer = setTimeout(() => {
      fetchUsers();
      fetchStats();
    }, 300);

    return () => {
      isMounted = false;
      clearTimeout(debounceTimer);
    };
  }, [pagination.currentPage, pagination.perPage, searchQuery]);

  const handlePageChange = (page) => {
    setPagination(prev => ({
      ...prev,
      currentPage: page
    }));
  };

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    setPagination(prev => ({
      ...prev,
      currentPage: 1
    }));
  };

  // Handle view user
  const handleView = (userId) => {
    navigate(`/users/${userId}`);
  };

  // Handle edit user
  const handleEdit = (userId) => {
    navigate(`/users/${userId}/edit`);
  };

  // Handle delete user
  const handleDelete = async (userId) => {
    if (window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      try {
        setLoading(true);
        const response = await axios.delete(`/users/${userId}`);
        
        if (response.data?.success) {
          toast.success('User deleted successfully');
          // Refresh the list
          const updatedResponse = await axios.get('/users', {
            params: {
              page: pagination.currentPage,
              per_page: pagination.perPage,
              search: searchQuery
            }
          });

          // Extract data from response - handle nested structure
          let usersData = [];
          if (updatedResponse.data?.data?.data && Array.isArray(updatedResponse.data.data.data)) {
            usersData = updatedResponse.data.data.data;
          } else if (updatedResponse.data?.data && Array.isArray(updatedResponse.data.data)) {
            usersData = updatedResponse.data.data;
          }

          // Transform users data
          const transformedUsers = usersData.map((user, index) => {
            try {
              return {
                id: user?.UserID || user?.id || `temp-${index}`,
                firstName: user?.FirstName || '',
                lastName: user?.LastName || '',
                email: user?.Email || '',
                phone: user?.Phone || '',
                role: (user?.Role || 'user').toLowerCase(),
                status: 'active',
                created_at: user?.CreatedAt || new Date().toISOString()
              };
            } catch (error) {
              console.error('Error transforming user data:', error, user);
              return {
                id: `error-${index}`,
                firstName: 'Error',
                lastName: 'User',
                email: 'N/A',
                phone: 'N/A',
                role: 'user',
                status: 'inactive',
                created_at: new Date().toISOString()
              };
            }
          });

          setUsers(transformedUsers);

          // Update pagination
          const meta = updatedResponse.data?.data?.meta || {};
          setPagination(prev => ({
            ...prev,
            total: meta.total || transformedUsers.length,
            lastPage: meta.last_page || Math.ceil(transformedUsers.length / prev.perPage),
            currentPage: meta.current_page || prev.currentPage
          }));

          // Update stats
          const statsUpdate = {
            total: transformedUsers.length,
            active: transformedUsers.length,
            inactive: 0,
            new: transformedUsers.filter(user => {
              const thirtyDaysAgo = new Date();
              thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
              return new Date(user.created_at) >= thirtyDaysAgo;
            }).length
          };
          setStats(statsUpdate);
        } else {
          toast.error(response.data?.message || 'Failed to delete user');
        }
      } catch (error) {
        console.error('Error deleting user:', error);
        const errorMessage = error.response?.data?.message 
          || error.response?.data?.error 
          || 'Failed to delete user. Please try again.';
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <div className="page-content">
      <div className="page-header mb-4">
        <h1>Users</h1>
      </div>

      {/* Stats Cards */}
      <div className="row mb-4">
        {/* Render stats cards with loading state */}
        {['total', 'active', 'inactive', 'new'].map((statKey) => (
          <div key={statKey} className="col-md-6 col-xl-3">
            <div className="card">
              <div className="card-body">
                <div className="d-flex align-items-center">
                  <div className={`avatar-md bg-${
                    statKey === 'total' ? 'primary' :
                    statKey === 'active' ? 'success' :
                    statKey === 'inactive' ? 'danger' :
                    'info'
                  } bg-opacity-10 rounded`}>
                    <i className={`ri-user-${
                      statKey === 'total' ? '' :
                      statKey === 'active' ? 'follow-' :
                      statKey === 'inactive' ? 'unfollow-' :
                      'add-'
                    }line fs-24 text-${
                      statKey === 'total' ? 'primary' :
                      statKey === 'active' ? 'success' :
                      statKey === 'inactive' ? 'danger' :
                      'info'
                    }`}></i>
                  </div>
                  <div className="ms-3">
                    <h4 className="mb-1">{
                      statKey.charAt(0).toUpperCase() + statKey.slice(1)
                    } Users</h4>
                    {statsLoading ? (
                      <div className="spinner-border spinner-border-sm text-primary" role="status">
                        <span className="visually-hidden">Loading...</span>
                      </div>
                    ) : (
                      <p className="fs-18 mb-0">{stats[statKey]}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Users Table */}
      <div className="card">
        <div className="card-header d-flex justify-content-between align-items-center">
          <h5 className="card-title mb-0">All Users</h5>
          <div className="d-flex gap-2">
            <input
              type="text"
              className="form-control"
              placeholder="Search users..."
              style={{ width: "200px" }}
              value={searchQuery}
              onChange={handleSearch}
            />
            <button 
              className="btn btn-primary"
              onClick={() => navigate('/users/new')}
            >
              <i className="ri-add-line me-1"></i>
              Add User
            </button>
          </div>
        </div>
        <div className="card-body">
          <div className="table-responsive">
            <table className="table table-hover">
              <thead className="bg-light">
                <tr>
                  <th>ID</th>
                  <th>Name</th>
                  <th>Email</th>
                  <th>Role</th>
                  <th>Status</th>
                  <th>Joined Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="7" className="text-center py-4">
                      <Spinner />
                    </td>
                  </tr>
                ) : users.length === 0 ? (
                  <tr>
                    <td colSpan="7" className="text-center py-4">
                      No users found
                    </td>
                  </tr>
                ) : (
                  users.map((user, index) => (
                    <tr key={`user-row-${user.id || index}`}>
                      <td>#{user.id}</td>
                      <td>
                        <div className="d-flex align-items-center">
                          <div className="avatar-sm me-2 bg-primary bg-opacity-10 rounded-circle">
                            <span className="avatar-title text-primary">
                              {user.firstName ? user.firstName.charAt(0).toUpperCase() : '?'}
                            </span>
                          </div>
                          {`${user.firstName} ${user.lastName}`.trim() || 'N/A'}
                        </div>
                      </td>
                      <td>{user.email}</td>
                      <td>
                        <span className={`badge bg-${user.role === 'admin' ? 'danger' : 'primary'}`}>
                          {user.role}
                        </span>
                      </td>
                      <td>
                        <span className={`badge bg-${user.status === 'active' ? 'success' : 'danger'}`}>
                          {user.status}
                        </span>
                      </td>
                      <td>{new Date(user.created_at).toLocaleDateString()}</td>
                      <td>
                        <button 
                          className="btn btn-sm btn-light me-2" 
                          title="View"
                          onClick={() => handleView(user.id)}
                        >
                          <i className="ri-eye-line"></i>
                        </button>
                        <button 
                          className="btn btn-sm btn-light me-2" 
                          title="Edit"
                          onClick={() => handleEdit(user.id)}
                        >
                          <i className="ri-edit-line"></i>
                        </button>
                        <button 
                          className="btn btn-sm btn-light" 
                          title="Delete"
                          onClick={() => handleDelete(user.id)}
                        >
                          <i className="ri-delete-bin-line"></i>
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {!loading && users.length > 0 && (
            <div className="d-flex justify-content-between align-items-center mt-4">
              <div>
                Showing {((pagination.currentPage - 1) * pagination.perPage) + 1} to {Math.min(pagination.currentPage * pagination.perPage, pagination.total)} of {pagination.total} entries
              </div>
              <div className="d-flex gap-2">
                <button
                  className="btn btn-light"
                  onClick={() => handlePageChange(pagination.currentPage - 1)}
                  disabled={pagination.currentPage === 1}
                >
                  Previous
                </button>
                {Array.from({ length: pagination.lastPage }, (_, i) => i + 1)
                  .filter(page => {
                    const current = pagination.currentPage;
                    return page === 1 || 
                           page === pagination.lastPage || 
                           (page >= current - 1 && page <= current + 1);
                  })
                  .map((page, index, array) => (
                    <React.Fragment key={page}>
                      {index > 0 && array[index - 1] !== page - 1 && (
                        <span className="btn btn-light disabled">...</span>
                      )}
                      <button
                        className={`btn ${pagination.currentPage === page ? 'btn-primary' : 'btn-light'}`}
                        onClick={() => handlePageChange(page)}
                      >
                        {page}
                      </button>
                    </React.Fragment>
                  ))}
                <button
                  className="btn btn-light"
                  onClick={() => handlePageChange(pagination.currentPage + 1)}
                  disabled={pagination.currentPage === pagination.lastPage}
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Users; 