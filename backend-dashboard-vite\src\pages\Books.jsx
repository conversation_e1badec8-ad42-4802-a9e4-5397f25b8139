import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from '../utils/axios';
import { toast } from 'react-toastify';

function Books() {
  const navigate = useNavigate();
  const [books, setBooks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [pagination, setPagination] = useState({
    currentPage: 1,
    perPage: 10,
    total: 0,
    lastPage: 1
  });
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    draft: 0,
    outOfStock: 0
  });

  // Fetch books
  useEffect(() => {
    const fetchBooks = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/books', {
          params: {
            page: pagination.currentPage,
            per_page: pagination.perPage,
            search: searchQuery
          }
        });

        // Log the response structure for debugging
        console.log('API Response:', response.data);

        // Handle the nested data structure
        const booksData = response.data?.data?.data || [];
        const metaData = response.data?.data?.meta || {};
        
        // Transform the books data to handle nested objects with capitalized keys
        const transformedBooks = booksData.map(book => {
          // Handle both object and string category cases
          let categoryName = '-';
          let categoryId = 0;

          if (book.category) {
            if (typeof book.category === 'object') {
              // Handle both lowercase and uppercase key variations
              categoryName = book.category.Name || book.category.name || '-';
              categoryId = book.category.CategoryID || book.category.id || 0;
            } else {
              categoryName = book.category;
            }
          }

          // Get the status and ensure it's lowercase for consistency
          const status = (book.Status || book.status || 'inactive').toLowerCase();

          return {
            ...book,
            id: book.BookID || book.bookID || book.id, // Use BookID from API response
            category: categoryName,
            categoryId: categoryId,
            title: book.Title || book.title || '-',
            author: book.Author || book.author || '-',
            status: status,
            created_at: book.CreatedAt || book.created_at || null
          };
        });
        
        setBooks(transformedBooks);
        
        // Calculate stats from the books data
        const total = transformedBooks.length;
        const active = transformedBooks.filter(book => book.status === 'active').length;
        const draft = transformedBooks.filter(book => book.status === 'draft').length;
        const outOfStock = transformedBooks.filter(book => book.stock === 0).length;
        
        setStats({ total, active, draft, outOfStock });
        setPagination(prev => ({
          ...prev,
          total: metaData.total || total,
          lastPage: metaData.last_page || Math.ceil(total / pagination.perPage),
          currentPage: metaData.current_page || prev.currentPage
        }));
      } catch (error) {
        console.error('Error fetching books:', error);
        toast.error('Failed to fetch books. Please try again.');
        setBooks([]);
        setStats({ total: 0, active: 0, draft: 0, outOfStock: 0 });
      } finally {
        setLoading(false);
      }
    };

    const debounceTimer = setTimeout(() => {
      fetchBooks();
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [pagination.currentPage, pagination.perPage, searchQuery]);

  const handlePageChange = (page) => {
    setPagination(prev => ({
      ...prev,
      currentPage: page
    }));
  };

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    setPagination(prev => ({
      ...prev,
      currentPage: 1
    }));
  };

  // Handle view book
  const handleView = (bookId) => {
    navigate(`/books/${bookId}`);
  };

  // Handle edit book
  const handleEdit = (bookId) => {
    navigate(`/books/${bookId}/edit`);
  };

  // Handle delete book
  const handleDelete = async (bookId) => {
    if (window.confirm('Are you sure you want to delete this book? This action cannot be undone.')) {
      try {
        setLoading(true);
        const response = await axios.delete(`/books/${bookId}`);
        
        if (response.data?.success) {
          toast.success('Book deleted successfully');
          // Refresh the list
          const updatedResponse = await axios.get('/books', {
            params: {
              page: pagination.currentPage,
              per_page: pagination.perPage,
              search: searchQuery
            }
          });

          // Handle the nested data structure
          const booksData = updatedResponse.data?.data?.data || [];
          const metaData = updatedResponse.data?.data?.meta || {};
          
          // Transform the books data
          const transformedBooks = booksData.map(book => {
            let categoryName = '-';
            let categoryId = 0;

            if (book.category) {
              if (typeof book.category === 'object') {
                categoryName = book.category.Name || book.category.name || '-';
                categoryId = book.category.CategoryID || book.category.id || 0;
              } else {
                categoryName = book.category;
              }
            }

            // Get the status and ensure it's lowercase for consistency
            const status = (book.Status || book.status || 'inactive').toLowerCase();

            return {
              ...book,
              id: book.BookID || book.bookID || book.id,
              category: categoryName,
              categoryId: categoryId,
              title: book.Title || book.title || '-',
              author: book.Author || book.author || '-',
              status: status,
              created_at: book.CreatedAt || book.created_at || null
            };
          });
          
          setBooks(transformedBooks);
          
          // Update stats
          const total = transformedBooks.length;
          const active = transformedBooks.filter(book => book.status === 'active').length;
          const draft = transformedBooks.filter(book => book.status === 'draft').length;
          const outOfStock = transformedBooks.filter(book => book.stock === 0).length;
          
          setStats({ total, active, draft, outOfStock });

          // Update pagination
          const lastPage = Math.ceil(metaData.total / pagination.perPage) || Math.ceil(total / pagination.perPage);
          if (pagination.currentPage > lastPage && lastPage > 0) {
            setPagination(prev => ({
              ...prev,
              currentPage: lastPage,
              total: metaData.total || total,
              lastPage
            }));
          } else {
            setPagination(prev => ({
              ...prev,
              total: metaData.total || total,
              lastPage
            }));
          }
        } else {
          toast.error(response.data?.message || 'Failed to delete book');
        }
      } catch (error) {
        console.error('Error deleting book:', error);
        const errorMessage = error.response?.data?.message 
          || error.response?.data?.error 
          || 'Failed to delete book. Please try again.';
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <div className="page-content">
      <div className="page-header mb-4">
        <h1>Books</h1>
      </div>

      {/* Stats Cards */}
      <div className="row mb-4">
        <div className="col-md-6 col-xl-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="avatar-md bg-primary bg-opacity-10 rounded">
                  <i className="ri-book-2-line fs-24 text-primary"></i>
                </div>
                <div className="ms-3">
                  <h4 className="mb-1">Total Books</h4>
                  <p className="fs-18 mb-0">{stats.total}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-md-6 col-xl-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="avatar-md bg-success bg-opacity-10 rounded">
                  <i className="ri-check-line fs-24 text-success"></i>
                </div>
                <div className="ms-3">
                  <h4 className="mb-1">Active Books</h4>
                  <p className="fs-18 mb-0">{stats.active}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-md-6 col-xl-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="avatar-md bg-warning bg-opacity-10 rounded">
                  <i className="ri-draft-line fs-24 text-warning"></i>
                </div>
                <div className="ms-3">
                  <h4 className="mb-1">Draft Books</h4>
                  <p className="fs-18 mb-0">{stats.draft}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-md-6 col-xl-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="avatar-md bg-danger bg-opacity-10 rounded">
                  <i className="ri-alert-line fs-24 text-danger"></i>
                </div>
                <div className="ms-3">
                  <h4 className="mb-1">Out of Stock</h4>
                  <p className="fs-18 mb-0">{stats.outOfStock}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Books Table */}
      <div className="card">
        <div className="card-header d-flex justify-content-between align-items-center">
          <h5 className="card-title mb-0">All Books</h5>
          <div className="d-flex gap-2">
            <input
              type="text"
              className="form-control"
              placeholder="Search books..."
              style={{ width: "200px" }}
              value={searchQuery}
              onChange={handleSearch}
            />
            <button
              className="btn btn-primary"
              onClick={() => navigate('/books/new')}
            >
              <i className="ri-add-line align-bottom me-1"></i>
              Add Book
            </button>
          </div>
        </div>
        <div className="card-body">
          <div className="table-responsive">
            <table className="table table-hover">
              <thead className="bg-light">
                <tr>
                  <th style={{width: "20px"}}>
                    <div className="form-check">
                      <input type="checkbox" className="form-check-input" id="selectAll" />
                      <label className="form-check-label" htmlFor="selectAll"></label>
                    </div>
                  </th>
                  <th>ID</th>
                  <th>Title</th>
                  <th>Author</th>
                  <th>Category</th>
                  <th>Created On</th>
                  <th>Status</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="8" className="text-center py-4">
                      <div className="spinner-border text-primary" role="status">
                        <span className="visually-hidden">Loading...</span>
                      </div>
                    </td>
                  </tr>
                ) : books.length === 0 ? (
                  <tr>
                    <td colSpan="8" className="text-center py-4">
                      No books found
                    </td>
                  </tr>
                ) : (
                  books.map((book) => (
                    <tr key={`book-${book.id}`}>
                      <td>
                        <div className="form-check">
                          <input 
                            type="checkbox" 
                            className="form-check-input"
                            id={`book-check-${book.id}`}
                          />
                          <label 
                            className="form-check-label" 
                            htmlFor={`book-check-${book.id}`}
                          ></label>
                        </div>
                      </td>
                      <td>#{book.id}</td>
                      <td>{book.title || '-'}</td>
                      <td>{book.author || '-'}</td>
                      <td>{book.category}</td>
                      <td>{book.created_at ? new Date(book.created_at).toLocaleDateString() : '-'}</td>
                      <td>
                        <span className={`badge bg-${
                          book.status === 'active' ? 'success' :
                          book.status === 'draft' ? 'warning' :
                          'danger'
                        }`}>
                          {book.status || 'N/A'}
                        </span>
                      </td>
                      <td>
                        <button 
                          className="btn btn-sm btn-light me-2" 
                          title="View"
                          onClick={() => handleView(book.id)}
                        >
                          <i className="ri-eye-line"></i>
                        </button>
                        <button 
                          className="btn btn-sm btn-light me-2" 
                          title="Edit"
                          onClick={() => handleEdit(book.id)}
                        >
                          <i className="ri-edit-line"></i>
                        </button>
                        <button 
                          className="btn btn-sm btn-light" 
                          title="Delete"
                          onClick={() => handleDelete(book.id)}
                        >
                          <i className="ri-delete-bin-line"></i>
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {!loading && books.length > 0 && (
            <div className="d-flex justify-content-between align-items-center mt-4">
              <div>
                Showing {((pagination.currentPage - 1) * pagination.perPage) + 1} to {Math.min(pagination.currentPage * pagination.perPage, pagination.total)} of {pagination.total} entries
              </div>
              <div className="d-flex gap-2">
                <button
                  className="btn btn-light"
                  onClick={() => handlePageChange(pagination.currentPage - 1)}
                  disabled={pagination.currentPage === 1}
                >
                  Previous
                </button>
                {Array.from({ length: pagination.lastPage }, (_, i) => i + 1)
                  .filter(page => {
                    const current = pagination.currentPage;
                    return page === 1 || 
                           page === pagination.lastPage || 
                           (page >= current - 1 && page <= current + 1);
                  })
                  .map((page, index, array) => (
                    <React.Fragment key={page}>
                      {index > 0 && array[index - 1] !== page - 1 && (
                        <span className="btn btn-light disabled">...</span>
                      )}
                      <button
                        className={`btn ${pagination.currentPage === page ? 'btn-primary' : 'btn-light'}`}
                        onClick={() => handlePageChange(page)}
                      >
                        {page}
                      </button>
                    </React.Fragment>
                  ))}
                <button
                  className="btn btn-light"
                  onClick={() => handlePageChange(pagination.currentPage + 1)}
                  disabled={pagination.currentPage === pagination.lastPage}
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Books; 