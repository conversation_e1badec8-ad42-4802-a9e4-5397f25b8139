{"ast": null, "code": "\"use client\";\n\nimport contains from 'dom-helpers/contains';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport { cloneElement, useCallback, useRef } from 'react';\nimport useTimeout from '@restart/hooks/useTimeout';\nimport warning from 'warning';\nimport { useUncontrolledProp } from 'uncontrollable';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport { getChildRef } from '@restart/ui/utils';\nimport Overlay from './Overlay';\nimport safeFindDOMNode from './safeFindDOMNode';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction normalizeDelay(delay) {\n  return delay && typeof delay === 'object' ? delay : {\n    show: delay,\n    hide: delay\n  };\n}\n\n// Simple implementation of mouseEnter and mouseLeave.\n// React's built version is broken: https://github.com/facebook/react/issues/4251\n// for cases when the trigger is disabled and mouseOut/Over can cause flicker\n// moving from one child element to another.\nfunction handleMouseOverOut(handler, args, relatedNative) {\n  const [e] = args;\n  const target = e.currentTarget;\n  const related = e.relatedTarget || e.nativeEvent[relatedNative];\n  if ((!related || related !== target) && !contains(target, related)) {\n    handler(...args);\n  }\n}\nconst triggerType = PropTypes.oneOf(['click', 'hover', 'focus']);\nconst OverlayTrigger = ({\n  trigger = ['hover', 'focus'],\n  overlay,\n  children,\n  popperConfig = {},\n  show: propsShow,\n  defaultShow = false,\n  onToggle,\n  delay: propsDelay,\n  placement,\n  flip = placement && placement.indexOf('auto') !== -1,\n  ...props\n}) => {\n  const triggerNodeRef = useRef(null);\n  const mergedRef = useMergedRefs(triggerNodeRef, getChildRef(children));\n  const timeout = useTimeout();\n  const hoverStateRef = useRef('');\n  const [show, setShow] = useUncontrolledProp(propsShow, defaultShow, onToggle);\n  const delay = normalizeDelay(propsDelay);\n  const {\n    onFocus,\n    onBlur,\n    onClick\n  } = typeof children !== 'function' ? React.Children.only(children).props : {};\n  const attachRef = r => {\n    mergedRef(safeFindDOMNode(r));\n  };\n  const handleShow = useCallback(() => {\n    timeout.clear();\n    hoverStateRef.current = 'show';\n    if (!delay.show) {\n      setShow(true);\n      return;\n    }\n    timeout.set(() => {\n      if (hoverStateRef.current === 'show') setShow(true);\n    }, delay.show);\n  }, [delay.show, setShow, timeout]);\n  const handleHide = useCallback(() => {\n    timeout.clear();\n    hoverStateRef.current = 'hide';\n    if (!delay.hide) {\n      setShow(false);\n      return;\n    }\n    timeout.set(() => {\n      if (hoverStateRef.current === 'hide') setShow(false);\n    }, delay.hide);\n  }, [delay.hide, setShow, timeout]);\n  const handleFocus = useCallback((...args) => {\n    handleShow();\n    onFocus == null || onFocus(...args);\n  }, [handleShow, onFocus]);\n  const handleBlur = useCallback((...args) => {\n    handleHide();\n    onBlur == null || onBlur(...args);\n  }, [handleHide, onBlur]);\n  const handleClick = useCallback((...args) => {\n    setShow(!show);\n    onClick == null || onClick(...args);\n  }, [onClick, setShow, show]);\n  const handleMouseOver = useCallback((...args) => {\n    handleMouseOverOut(handleShow, args, 'fromElement');\n  }, [handleShow]);\n  const handleMouseOut = useCallback((...args) => {\n    handleMouseOverOut(handleHide, args, 'toElement');\n  }, [handleHide]);\n  const triggers = trigger == null ? [] : [].concat(trigger);\n  const triggerProps = {\n    ref: attachRef\n  };\n  if (triggers.indexOf('click') !== -1) {\n    triggerProps.onClick = handleClick;\n  }\n  if (triggers.indexOf('focus') !== -1) {\n    triggerProps.onFocus = handleFocus;\n    triggerProps.onBlur = handleBlur;\n  }\n  if (triggers.indexOf('hover') !== -1) {\n    process.env.NODE_ENV !== \"production\" ? warning(triggers.length > 1, '[react-bootstrap] Specifying only the `\"hover\"` trigger limits the visibility of the overlay to just mouse users. Consider also including the `\"focus\"` trigger so that touch and keyboard only users can see the overlay as well.') : void 0;\n    triggerProps.onMouseOver = handleMouseOver;\n    triggerProps.onMouseOut = handleMouseOut;\n  }\n  return /*#__PURE__*/_jsxs(_Fragment, {\n    children: [typeof children === 'function' ? children(triggerProps) : /*#__PURE__*/cloneElement(children, triggerProps), /*#__PURE__*/_jsx(Overlay, {\n      ...props,\n      show: show,\n      onHide: handleHide,\n      flip: flip,\n      placement: placement,\n      popperConfig: popperConfig,\n      target: triggerNodeRef.current,\n      children: overlay\n    })]\n  });\n};\nexport default OverlayTrigger;", "map": {"version": 3, "names": ["contains", "PropTypes", "React", "cloneElement", "useCallback", "useRef", "useTimeout", "warning", "useUncontrolledProp", "useMergedRefs", "getChildRef", "Overlay", "safeFindDOMNode", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "normalizeDelay", "delay", "show", "hide", "handleMouseOverOut", "handler", "args", "relatedNative", "e", "target", "currentTarget", "related", "relatedTarget", "nativeEvent", "triggerType", "oneOf", "OverlayTrigger", "trigger", "overlay", "children", "popperConfig", "propsShow", "defaultShow", "onToggle", "props<PERSON><PERSON><PERSON>", "placement", "flip", "indexOf", "props", "triggerNodeRef", "mergedRef", "timeout", "hoverStateRef", "setShow", "onFocus", "onBlur", "onClick", "Children", "only", "attachRef", "r", "handleShow", "clear", "current", "set", "handleHide", "handleFocus", "handleBlur", "handleClick", "handleMouseOver", "handleMouseOut", "triggers", "concat", "triggerProps", "ref", "process", "env", "NODE_ENV", "length", "onMouseOver", "onMouseOut", "onHide"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/react-bootstrap/esm/OverlayTrigger.js"], "sourcesContent": ["\"use client\";\n\nimport contains from 'dom-helpers/contains';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport { cloneElement, useCallback, useRef } from 'react';\nimport useTimeout from '@restart/hooks/useTimeout';\nimport warning from 'warning';\nimport { useUncontrolledProp } from 'uncontrollable';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport { getChildRef } from '@restart/ui/utils';\nimport Overlay from './Overlay';\nimport safeFindDOMNode from './safeFindDOMNode';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction normalizeDelay(delay) {\n  return delay && typeof delay === 'object' ? delay : {\n    show: delay,\n    hide: delay\n  };\n}\n\n// Simple implementation of mouseEnter and mouseLeave.\n// React's built version is broken: https://github.com/facebook/react/issues/4251\n// for cases when the trigger is disabled and mouseOut/Over can cause flicker\n// moving from one child element to another.\nfunction handleMouseOverOut(handler, args, relatedNative) {\n  const [e] = args;\n  const target = e.currentTarget;\n  const related = e.relatedTarget || e.nativeEvent[relatedNative];\n  if ((!related || related !== target) && !contains(target, related)) {\n    handler(...args);\n  }\n}\nconst triggerType = PropTypes.oneOf(['click', 'hover', 'focus']);\nconst OverlayTrigger = ({\n  trigger = ['hover', 'focus'],\n  overlay,\n  children,\n  popperConfig = {},\n  show: propsShow,\n  defaultShow = false,\n  onToggle,\n  delay: propsDelay,\n  placement,\n  flip = placement && placement.indexOf('auto') !== -1,\n  ...props\n}) => {\n  const triggerNodeRef = useRef(null);\n  const mergedRef = useMergedRefs(triggerNodeRef, getChildRef(children));\n  const timeout = useTimeout();\n  const hoverStateRef = useRef('');\n  const [show, setShow] = useUncontrolledProp(propsShow, defaultShow, onToggle);\n  const delay = normalizeDelay(propsDelay);\n  const {\n    onFocus,\n    onBlur,\n    onClick\n  } = typeof children !== 'function' ? React.Children.only(children).props : {};\n  const attachRef = r => {\n    mergedRef(safeFindDOMNode(r));\n  };\n  const handleShow = useCallback(() => {\n    timeout.clear();\n    hoverStateRef.current = 'show';\n    if (!delay.show) {\n      setShow(true);\n      return;\n    }\n    timeout.set(() => {\n      if (hoverStateRef.current === 'show') setShow(true);\n    }, delay.show);\n  }, [delay.show, setShow, timeout]);\n  const handleHide = useCallback(() => {\n    timeout.clear();\n    hoverStateRef.current = 'hide';\n    if (!delay.hide) {\n      setShow(false);\n      return;\n    }\n    timeout.set(() => {\n      if (hoverStateRef.current === 'hide') setShow(false);\n    }, delay.hide);\n  }, [delay.hide, setShow, timeout]);\n  const handleFocus = useCallback((...args) => {\n    handleShow();\n    onFocus == null || onFocus(...args);\n  }, [handleShow, onFocus]);\n  const handleBlur = useCallback((...args) => {\n    handleHide();\n    onBlur == null || onBlur(...args);\n  }, [handleHide, onBlur]);\n  const handleClick = useCallback((...args) => {\n    setShow(!show);\n    onClick == null || onClick(...args);\n  }, [onClick, setShow, show]);\n  const handleMouseOver = useCallback((...args) => {\n    handleMouseOverOut(handleShow, args, 'fromElement');\n  }, [handleShow]);\n  const handleMouseOut = useCallback((...args) => {\n    handleMouseOverOut(handleHide, args, 'toElement');\n  }, [handleHide]);\n  const triggers = trigger == null ? [] : [].concat(trigger);\n  const triggerProps = {\n    ref: attachRef\n  };\n  if (triggers.indexOf('click') !== -1) {\n    triggerProps.onClick = handleClick;\n  }\n  if (triggers.indexOf('focus') !== -1) {\n    triggerProps.onFocus = handleFocus;\n    triggerProps.onBlur = handleBlur;\n  }\n  if (triggers.indexOf('hover') !== -1) {\n    process.env.NODE_ENV !== \"production\" ? warning(triggers.length > 1, '[react-bootstrap] Specifying only the `\"hover\"` trigger limits the visibility of the overlay to just mouse users. Consider also including the `\"focus\"` trigger so that touch and keyboard only users can see the overlay as well.') : void 0;\n    triggerProps.onMouseOver = handleMouseOver;\n    triggerProps.onMouseOut = handleMouseOut;\n  }\n  return /*#__PURE__*/_jsxs(_Fragment, {\n    children: [typeof children === 'function' ? children(triggerProps) : /*#__PURE__*/cloneElement(children, triggerProps), /*#__PURE__*/_jsx(Overlay, {\n      ...props,\n      show: show,\n      onHide: handleHide,\n      flip: flip,\n      placement: placement,\n      popperConfig: popperConfig,\n      target: triggerNodeRef.current,\n      children: overlay\n    })]\n  });\n};\nexport default OverlayTrigger;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AACzD,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,OAAO,MAAM,SAAS;AAC7B,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,OAAOC,aAAa,MAAM,8BAA8B;AACxD,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,QAAQ,IAAIC,SAAS,QAAQ,mBAAmB;AACzD,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,OAAOA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG;IAClDC,IAAI,EAAED,KAAK;IACXE,IAAI,EAAEF;EACR,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,SAASG,kBAAkBA,CAACC,OAAO,EAAEC,IAAI,EAAEC,aAAa,EAAE;EACxD,MAAM,CAACC,CAAC,CAAC,GAAGF,IAAI;EAChB,MAAMG,MAAM,GAAGD,CAAC,CAACE,aAAa;EAC9B,MAAMC,OAAO,GAAGH,CAAC,CAACI,aAAa,IAAIJ,CAAC,CAACK,WAAW,CAACN,aAAa,CAAC;EAC/D,IAAI,CAAC,CAACI,OAAO,IAAIA,OAAO,KAAKF,MAAM,KAAK,CAAC5B,QAAQ,CAAC4B,MAAM,EAAEE,OAAO,CAAC,EAAE;IAClEN,OAAO,CAAC,GAAGC,IAAI,CAAC;EAClB;AACF;AACA,MAAMQ,WAAW,GAAGhC,SAAS,CAACiC,KAAK,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AAChE,MAAMC,cAAc,GAAGA,CAAC;EACtBC,OAAO,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC;EAC5BC,OAAO;EACPC,QAAQ;EACRC,YAAY,GAAG,CAAC,CAAC;EACjBlB,IAAI,EAAEmB,SAAS;EACfC,WAAW,GAAG,KAAK;EACnBC,QAAQ;EACRtB,KAAK,EAAEuB,UAAU;EACjBC,SAAS;EACTC,IAAI,GAAGD,SAAS,IAAIA,SAAS,CAACE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;EACpD,GAAGC;AACL,CAAC,KAAK;EACJ,MAAMC,cAAc,GAAG3C,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM4C,SAAS,GAAGxC,aAAa,CAACuC,cAAc,EAAEtC,WAAW,CAAC4B,QAAQ,CAAC,CAAC;EACtE,MAAMY,OAAO,GAAG5C,UAAU,CAAC,CAAC;EAC5B,MAAM6C,aAAa,GAAG9C,MAAM,CAAC,EAAE,CAAC;EAChC,MAAM,CAACgB,IAAI,EAAE+B,OAAO,CAAC,GAAG5C,mBAAmB,CAACgC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,CAAC;EAC7E,MAAMtB,KAAK,GAAGD,cAAc,CAACwB,UAAU,CAAC;EACxC,MAAM;IACJU,OAAO;IACPC,MAAM;IACNC;EACF,CAAC,GAAG,OAAOjB,QAAQ,KAAK,UAAU,GAAGpC,KAAK,CAACsD,QAAQ,CAACC,IAAI,CAACnB,QAAQ,CAAC,CAACS,KAAK,GAAG,CAAC,CAAC;EAC7E,MAAMW,SAAS,GAAGC,CAAC,IAAI;IACrBV,SAAS,CAACrC,eAAe,CAAC+C,CAAC,CAAC,CAAC;EAC/B,CAAC;EACD,MAAMC,UAAU,GAAGxD,WAAW,CAAC,MAAM;IACnC8C,OAAO,CAACW,KAAK,CAAC,CAAC;IACfV,aAAa,CAACW,OAAO,GAAG,MAAM;IAC9B,IAAI,CAAC1C,KAAK,CAACC,IAAI,EAAE;MACf+B,OAAO,CAAC,IAAI,CAAC;MACb;IACF;IACAF,OAAO,CAACa,GAAG,CAAC,MAAM;MAChB,IAAIZ,aAAa,CAACW,OAAO,KAAK,MAAM,EAAEV,OAAO,CAAC,IAAI,CAAC;IACrD,CAAC,EAAEhC,KAAK,CAACC,IAAI,CAAC;EAChB,CAAC,EAAE,CAACD,KAAK,CAACC,IAAI,EAAE+B,OAAO,EAAEF,OAAO,CAAC,CAAC;EAClC,MAAMc,UAAU,GAAG5D,WAAW,CAAC,MAAM;IACnC8C,OAAO,CAACW,KAAK,CAAC,CAAC;IACfV,aAAa,CAACW,OAAO,GAAG,MAAM;IAC9B,IAAI,CAAC1C,KAAK,CAACE,IAAI,EAAE;MACf8B,OAAO,CAAC,KAAK,CAAC;MACd;IACF;IACAF,OAAO,CAACa,GAAG,CAAC,MAAM;MAChB,IAAIZ,aAAa,CAACW,OAAO,KAAK,MAAM,EAAEV,OAAO,CAAC,KAAK,CAAC;IACtD,CAAC,EAAEhC,KAAK,CAACE,IAAI,CAAC;EAChB,CAAC,EAAE,CAACF,KAAK,CAACE,IAAI,EAAE8B,OAAO,EAAEF,OAAO,CAAC,CAAC;EAClC,MAAMe,WAAW,GAAG7D,WAAW,CAAC,CAAC,GAAGqB,IAAI,KAAK;IAC3CmC,UAAU,CAAC,CAAC;IACZP,OAAO,IAAI,IAAI,IAAIA,OAAO,CAAC,GAAG5B,IAAI,CAAC;EACrC,CAAC,EAAE,CAACmC,UAAU,EAAEP,OAAO,CAAC,CAAC;EACzB,MAAMa,UAAU,GAAG9D,WAAW,CAAC,CAAC,GAAGqB,IAAI,KAAK;IAC1CuC,UAAU,CAAC,CAAC;IACZV,MAAM,IAAI,IAAI,IAAIA,MAAM,CAAC,GAAG7B,IAAI,CAAC;EACnC,CAAC,EAAE,CAACuC,UAAU,EAAEV,MAAM,CAAC,CAAC;EACxB,MAAMa,WAAW,GAAG/D,WAAW,CAAC,CAAC,GAAGqB,IAAI,KAAK;IAC3C2B,OAAO,CAAC,CAAC/B,IAAI,CAAC;IACdkC,OAAO,IAAI,IAAI,IAAIA,OAAO,CAAC,GAAG9B,IAAI,CAAC;EACrC,CAAC,EAAE,CAAC8B,OAAO,EAAEH,OAAO,EAAE/B,IAAI,CAAC,CAAC;EAC5B,MAAM+C,eAAe,GAAGhE,WAAW,CAAC,CAAC,GAAGqB,IAAI,KAAK;IAC/CF,kBAAkB,CAACqC,UAAU,EAAEnC,IAAI,EAAE,aAAa,CAAC;EACrD,CAAC,EAAE,CAACmC,UAAU,CAAC,CAAC;EAChB,MAAMS,cAAc,GAAGjE,WAAW,CAAC,CAAC,GAAGqB,IAAI,KAAK;IAC9CF,kBAAkB,CAACyC,UAAU,EAAEvC,IAAI,EAAE,WAAW,CAAC;EACnD,CAAC,EAAE,CAACuC,UAAU,CAAC,CAAC;EAChB,MAAMM,QAAQ,GAAGlC,OAAO,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAACmC,MAAM,CAACnC,OAAO,CAAC;EAC1D,MAAMoC,YAAY,GAAG;IACnBC,GAAG,EAAEf;EACP,CAAC;EACD,IAAIY,QAAQ,CAACxB,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;IACpC0B,YAAY,CAACjB,OAAO,GAAGY,WAAW;EACpC;EACA,IAAIG,QAAQ,CAACxB,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;IACpC0B,YAAY,CAACnB,OAAO,GAAGY,WAAW;IAClCO,YAAY,CAAClB,MAAM,GAAGY,UAAU;EAClC;EACA,IAAII,QAAQ,CAACxB,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;IACpC4B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGrE,OAAO,CAAC+D,QAAQ,CAACO,MAAM,GAAG,CAAC,EAAE,oOAAoO,CAAC,GAAG,KAAK,CAAC;IACnTL,YAAY,CAACM,WAAW,GAAGV,eAAe;IAC1CI,YAAY,CAACO,UAAU,GAAGV,cAAc;EAC1C;EACA,OAAO,aAAanD,KAAK,CAACF,SAAS,EAAE;IACnCsB,QAAQ,EAAE,CAAC,OAAOA,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACkC,YAAY,CAAC,GAAG,aAAarE,YAAY,CAACmC,QAAQ,EAAEkC,YAAY,CAAC,EAAE,aAAa1D,IAAI,CAACH,OAAO,EAAE;MACjJ,GAAGoC,KAAK;MACR1B,IAAI,EAAEA,IAAI;MACV2D,MAAM,EAAEhB,UAAU;MAClBnB,IAAI,EAAEA,IAAI;MACVD,SAAS,EAAEA,SAAS;MACpBL,YAAY,EAAEA,YAAY;MAC1BX,MAAM,EAAEoB,cAAc,CAACc,OAAO;MAC9BxB,QAAQ,EAAED;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AACD,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}