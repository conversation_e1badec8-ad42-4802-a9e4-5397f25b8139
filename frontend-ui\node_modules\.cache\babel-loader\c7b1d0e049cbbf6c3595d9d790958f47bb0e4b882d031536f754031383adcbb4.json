{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\LaravelReact\\\\frontend-ui\\\\src\\\\components\\\\PageHeader.jsx\";\nimport React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport shopBanner from \"../assets/images/banner/banner-shop.jpg\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst getBackgroundImage = curPage => {\n  const images = {\n    Shop: shopBanner,\n    // Using local image file\n    Blog: \"https://images.pexels.com/photos/590493/pexels-photo-590493.jpeg?auto=compress&cs=tinysrgb&w=1200\",\n    // Book-related image\n    About: \"https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg?auto=compress&cs=tinysrgb&w=1200\",\n    // Bookstore interior\n    Contact: \"https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg?auto=compress&cs=tinysrgb&w=1200\",\n    // Bookstore interior\n    \"Single Product\": \"https://images.pexels.com/photos/1370295/pexels-photo-1370295.jpeg?auto=compress&cs=tinysrgb&w=1200\" // Open book\n  };\n  return images[curPage] || images.Shop;\n};\nconst PageHeader = ({\n  title,\n  curPage\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pageheader-section\",\n    style: {\n      backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('${getBackgroundImage(curPage)}')`,\n      backgroundPosition: \"center\",\n      backgroundSize: \"cover\",\n      backgroundRepeat: \"no-repeat\",\n      padding: \"80px 0\",\n      position: \"relative\"\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pageheader-content text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-white mb-3\",\n              children: title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n              \"aria-label\": \"breadcrumb\",\n              children: /*#__PURE__*/_jsxDEV(\"ol\", {\n                className: \"breadcrumb justify-content-center mb-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"breadcrumb-item\",\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/\",\n                    className: \"text-white text-decoration-none\",\n                    children: \"Home\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 42,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 41,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"breadcrumb-item\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white\",\n                    children: curPage\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 47,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 46,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n};\n_c = PageHeader;\nexport default PageHeader;\nvar _c;\n$RefreshReg$(_c, \"PageHeader\");", "map": {"version": 3, "names": ["React", "Link", "shopBanner", "jsxDEV", "_jsxDEV", "getBackgroundImage", "curPage", "images", "Shop", "Blog", "About", "Contact", "<PERSON><PERSON><PERSON><PERSON>", "title", "className", "style", "backgroundImage", "backgroundPosition", "backgroundSize", "backgroundRepeat", "padding", "position", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/components/PageHeader.jsx"], "sourcesContent": ["import React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport shopBanner from \"../assets/images/banner/banner-shop.jpg\";\n\nconst getBackgroundImage = (curPage) => {\n  const images = {\n    Shop: shopBanner, // Using local image file\n    Blog: \"https://images.pexels.com/photos/590493/pexels-photo-590493.jpeg?auto=compress&cs=tinysrgb&w=1200\", // Book-related image\n    About:\n      \"https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg?auto=compress&cs=tinysrgb&w=1200\", // Bookstore interior\n    Contact:\n      \"https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg?auto=compress&cs=tinysrgb&w=1200\", // Bookstore interior\n    \"Single Product\":\n      \"https://images.pexels.com/photos/1370295/pexels-photo-1370295.jpeg?auto=compress&cs=tinysrgb&w=1200\", // Open book\n  };\n  return images[curPage] || images.Shop;\n};\n\nconst PageHeader = ({ title, curPage }) => {\n  return (\n    <div\n      className=\"pageheader-section\"\n      style={{\n        backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('${getBackgroundImage(\n          curPage\n        )}')`,\n        backgroundPosition: \"center\",\n        backgroundSize: \"cover\",\n        backgroundRepeat: \"no-repeat\",\n        padding: \"80px 0\",\n        position: \"relative\",\n      }}\n    >\n      <div className=\"container\">\n        <div className=\"row justify-content-center\">\n          <div className=\"col-12\">\n            <div className=\"pageheader-content text-center\">\n              <h2 className=\"text-white mb-3\">{title}</h2>\n              <nav aria-label=\"breadcrumb\">\n                <ol className=\"breadcrumb justify-content-center mb-0\">\n                  <li className=\"breadcrumb-item\">\n                    <Link to=\"/\" className=\"text-white text-decoration-none\">\n                      Home\n                    </Link>\n                  </li>\n                  <li className=\"breadcrumb-item\">\n                    <span className=\"text-white\">{curPage}</span>\n                  </li>\n                </ol>\n              </nav>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PageHeader;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,UAAU,MAAM,yCAAyC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjE,MAAMC,kBAAkB,GAAIC,OAAO,IAAK;EACtC,MAAMC,MAAM,GAAG;IACbC,IAAI,EAAEN,UAAU;IAAE;IAClBO,IAAI,EAAE,mGAAmG;IAAE;IAC3GC,KAAK,EACH,mHAAmH;IAAE;IACvHC,OAAO,EACL,mHAAmH;IAAE;IACvH,gBAAgB,EACd,qGAAqG,CAAE;EAC3G,CAAC;EACD,OAAOJ,MAAM,CAACD,OAAO,CAAC,IAAIC,MAAM,CAACC,IAAI;AACvC,CAAC;AAED,MAAMI,UAAU,GAAGA,CAAC;EAAEC,KAAK;EAAEP;AAAQ,CAAC,KAAK;EACzC,oBACEF,OAAA;IACEU,SAAS,EAAC,oBAAoB;IAC9BC,KAAK,EAAE;MACLC,eAAe,EAAE,iEAAiEX,kBAAkB,CAClGC,OACF,CAAC,IAAI;MACLW,kBAAkB,EAAE,QAAQ;MAC5BC,cAAc,EAAE,OAAO;MACvBC,gBAAgB,EAAE,WAAW;MAC7BC,OAAO,EAAE,QAAQ;MACjBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,eAEFlB,OAAA;MAAKU,SAAS,EAAC,WAAW;MAAAQ,QAAA,eACxBlB,OAAA;QAAKU,SAAS,EAAC,4BAA4B;QAAAQ,QAAA,eACzClB,OAAA;UAAKU,SAAS,EAAC,QAAQ;UAAAQ,QAAA,eACrBlB,OAAA;YAAKU,SAAS,EAAC,gCAAgC;YAAAQ,QAAA,gBAC7ClB,OAAA;cAAIU,SAAS,EAAC,iBAAiB;cAAAQ,QAAA,EAAET;YAAK;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5CtB,OAAA;cAAK,cAAW,YAAY;cAAAkB,QAAA,eAC1BlB,OAAA;gBAAIU,SAAS,EAAC,wCAAwC;gBAAAQ,QAAA,gBACpDlB,OAAA;kBAAIU,SAAS,EAAC,iBAAiB;kBAAAQ,QAAA,eAC7BlB,OAAA,CAACH,IAAI;oBAAC0B,EAAE,EAAC,GAAG;oBAACb,SAAS,EAAC,iCAAiC;oBAAAQ,QAAA,EAAC;kBAEzD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACLtB,OAAA;kBAAIU,SAAS,EAAC,iBAAiB;kBAAAQ,QAAA,eAC7BlB,OAAA;oBAAMU,SAAS,EAAC,YAAY;oBAAAQ,QAAA,EAAEhB;kBAAO;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACE,EAAA,GAtCIhB,UAAU;AAwChB,eAAeA,UAAU;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}