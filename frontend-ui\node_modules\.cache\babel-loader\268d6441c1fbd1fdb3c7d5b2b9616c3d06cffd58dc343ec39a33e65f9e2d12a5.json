{"ast": null, "code": "import { a as getWindow } from '../shared/ssr-window.esm.mjs';\nimport { n as nextTick, f as now } from '../shared/utils.mjs';\n\n/* eslint-disable consistent-return */\nfunction Mousewheel(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const window = getWindow();\n  extendParams({\n    mousewheel: {\n      enabled: false,\n      releaseOnEdges: false,\n      invert: false,\n      forceToAxis: false,\n      sensitivity: 1,\n      eventsTarget: 'container',\n      thresholdDelta: null,\n      thresholdTime: null,\n      noMousewheelClass: 'swiper-no-mousewheel'\n    }\n  });\n  swiper.mousewheel = {\n    enabled: false\n  };\n  let timeout;\n  let lastScrollTime = now();\n  let lastEventBeforeSnap;\n  const recentWheelEvents = [];\n  function normalize(e) {\n    // Reasonable defaults\n    const PIXEL_STEP = 10;\n    const LINE_HEIGHT = 40;\n    const PAGE_HEIGHT = 800;\n    let sX = 0;\n    let sY = 0; // spinX, spinY\n    let pX = 0;\n    let pY = 0; // pixelX, pixelY\n\n    // Legacy\n    if ('detail' in e) {\n      sY = e.detail;\n    }\n    if ('wheelDelta' in e) {\n      sY = -e.wheelDelta / 120;\n    }\n    if ('wheelDeltaY' in e) {\n      sY = -e.wheelDeltaY / 120;\n    }\n    if ('wheelDeltaX' in e) {\n      sX = -e.wheelDeltaX / 120;\n    }\n\n    // side scrolling on FF with DOMMouseScroll\n    if ('axis' in e && e.axis === e.HORIZONTAL_AXIS) {\n      sX = sY;\n      sY = 0;\n    }\n    pX = sX * PIXEL_STEP;\n    pY = sY * PIXEL_STEP;\n    if ('deltaY' in e) {\n      pY = e.deltaY;\n    }\n    if ('deltaX' in e) {\n      pX = e.deltaX;\n    }\n    if (e.shiftKey && !pX) {\n      // if user scrolls with shift he wants horizontal scroll\n      pX = pY;\n      pY = 0;\n    }\n    if ((pX || pY) && e.deltaMode) {\n      if (e.deltaMode === 1) {\n        // delta in LINE units\n        pX *= LINE_HEIGHT;\n        pY *= LINE_HEIGHT;\n      } else {\n        // delta in PAGE units\n        pX *= PAGE_HEIGHT;\n        pY *= PAGE_HEIGHT;\n      }\n    }\n\n    // Fall-back if spin cannot be determined\n    if (pX && !sX) {\n      sX = pX < 1 ? -1 : 1;\n    }\n    if (pY && !sY) {\n      sY = pY < 1 ? -1 : 1;\n    }\n    return {\n      spinX: sX,\n      spinY: sY,\n      pixelX: pX,\n      pixelY: pY\n    };\n  }\n  function handleMouseEnter() {\n    if (!swiper.enabled) return;\n    swiper.mouseEntered = true;\n  }\n  function handleMouseLeave() {\n    if (!swiper.enabled) return;\n    swiper.mouseEntered = false;\n  }\n  function animateSlider(newEvent) {\n    if (swiper.params.mousewheel.thresholdDelta && newEvent.delta < swiper.params.mousewheel.thresholdDelta) {\n      // Prevent if delta of wheel scroll delta is below configured threshold\n      return false;\n    }\n    if (swiper.params.mousewheel.thresholdTime && now() - lastScrollTime < swiper.params.mousewheel.thresholdTime) {\n      // Prevent if time between scrolls is below configured threshold\n      return false;\n    }\n\n    // If the movement is NOT big enough and\n    // if the last time the user scrolled was too close to the current one (avoid continuously triggering the slider):\n    //   Don't go any further (avoid insignificant scroll movement).\n    if (newEvent.delta >= 6 && now() - lastScrollTime < 60) {\n      // Return false as a default\n      return true;\n    }\n    // If user is scrolling towards the end:\n    //   If the slider hasn't hit the latest slide or\n    //   if the slider is a loop and\n    //   if the slider isn't moving right now:\n    //     Go to next slide and\n    //     emit a scroll event.\n    // Else (the user is scrolling towards the beginning) and\n    // if the slider hasn't hit the first slide or\n    // if the slider is a loop and\n    // if the slider isn't moving right now:\n    //   Go to prev slide and\n    //   emit a scroll event.\n    if (newEvent.direction < 0) {\n      if ((!swiper.isEnd || swiper.params.loop) && !swiper.animating) {\n        swiper.slideNext();\n        emit('scroll', newEvent.raw);\n      }\n    } else if ((!swiper.isBeginning || swiper.params.loop) && !swiper.animating) {\n      swiper.slidePrev();\n      emit('scroll', newEvent.raw);\n    }\n    // If you got here is because an animation has been triggered so store the current time\n    lastScrollTime = new window.Date().getTime();\n    // Return false as a default\n    return false;\n  }\n  function releaseScroll(newEvent) {\n    const params = swiper.params.mousewheel;\n    if (newEvent.direction < 0) {\n      if (swiper.isEnd && !swiper.params.loop && params.releaseOnEdges) {\n        // Return true to animate scroll on edges\n        return true;\n      }\n    } else if (swiper.isBeginning && !swiper.params.loop && params.releaseOnEdges) {\n      // Return true to animate scroll on edges\n      return true;\n    }\n    return false;\n  }\n  function handle(event) {\n    let e = event;\n    let disableParentSwiper = true;\n    if (!swiper.enabled) return;\n\n    // Ignore event if the target or its parents have the swiper-no-mousewheel class\n    if (event.target.closest(`.${swiper.params.mousewheel.noMousewheelClass}`)) return;\n    const params = swiper.params.mousewheel;\n    if (swiper.params.cssMode) {\n      e.preventDefault();\n    }\n    let targetEl = swiper.el;\n    if (swiper.params.mousewheel.eventsTarget !== 'container') {\n      targetEl = document.querySelector(swiper.params.mousewheel.eventsTarget);\n    }\n    const targetElContainsTarget = targetEl && targetEl.contains(e.target);\n    if (!swiper.mouseEntered && !targetElContainsTarget && !params.releaseOnEdges) return true;\n    if (e.originalEvent) e = e.originalEvent; // jquery fix\n    let delta = 0;\n    const rtlFactor = swiper.rtlTranslate ? -1 : 1;\n    const data = normalize(e);\n    if (params.forceToAxis) {\n      if (swiper.isHorizontal()) {\n        if (Math.abs(data.pixelX) > Math.abs(data.pixelY)) delta = -data.pixelX * rtlFactor;else return true;\n      } else if (Math.abs(data.pixelY) > Math.abs(data.pixelX)) delta = -data.pixelY;else return true;\n    } else {\n      delta = Math.abs(data.pixelX) > Math.abs(data.pixelY) ? -data.pixelX * rtlFactor : -data.pixelY;\n    }\n    if (delta === 0) return true;\n    if (params.invert) delta = -delta;\n\n    // Get the scroll positions\n    let positions = swiper.getTranslate() + delta * params.sensitivity;\n    if (positions >= swiper.minTranslate()) positions = swiper.minTranslate();\n    if (positions <= swiper.maxTranslate()) positions = swiper.maxTranslate();\n\n    // When loop is true:\n    //     the disableParentSwiper will be true.\n    // When loop is false:\n    //     if the scroll positions is not on edge,\n    //     then the disableParentSwiper will be true.\n    //     if the scroll on edge positions,\n    //     then the disableParentSwiper will be false.\n    disableParentSwiper = swiper.params.loop ? true : !(positions === swiper.minTranslate() || positions === swiper.maxTranslate());\n    if (disableParentSwiper && swiper.params.nested) e.stopPropagation();\n    if (!swiper.params.freeMode || !swiper.params.freeMode.enabled) {\n      // Register the new event in a variable which stores the relevant data\n      const newEvent = {\n        time: now(),\n        delta: Math.abs(delta),\n        direction: Math.sign(delta),\n        raw: event\n      };\n\n      // Keep the most recent events\n      if (recentWheelEvents.length >= 2) {\n        recentWheelEvents.shift(); // only store the last N events\n      }\n      const prevEvent = recentWheelEvents.length ? recentWheelEvents[recentWheelEvents.length - 1] : undefined;\n      recentWheelEvents.push(newEvent);\n\n      // If there is at least one previous recorded event:\n      //   If direction has changed or\n      //   if the scroll is quicker than the previous one:\n      //     Animate the slider.\n      // Else (this is the first time the wheel is moved):\n      //     Animate the slider.\n      if (prevEvent) {\n        if (newEvent.direction !== prevEvent.direction || newEvent.delta > prevEvent.delta || newEvent.time > prevEvent.time + 150) {\n          animateSlider(newEvent);\n        }\n      } else {\n        animateSlider(newEvent);\n      }\n\n      // If it's time to release the scroll:\n      //   Return now so you don't hit the preventDefault.\n      if (releaseScroll(newEvent)) {\n        return true;\n      }\n    } else {\n      // Freemode or scrollContainer:\n\n      // If we recently snapped after a momentum scroll, then ignore wheel events\n      // to give time for the deceleration to finish. Stop ignoring after 500 msecs\n      // or if it's a new scroll (larger delta or inverse sign as last event before\n      // an end-of-momentum snap).\n      const newEvent = {\n        time: now(),\n        delta: Math.abs(delta),\n        direction: Math.sign(delta)\n      };\n      const ignoreWheelEvents = lastEventBeforeSnap && newEvent.time < lastEventBeforeSnap.time + 500 && newEvent.delta <= lastEventBeforeSnap.delta && newEvent.direction === lastEventBeforeSnap.direction;\n      if (!ignoreWheelEvents) {\n        lastEventBeforeSnap = undefined;\n        let position = swiper.getTranslate() + delta * params.sensitivity;\n        const wasBeginning = swiper.isBeginning;\n        const wasEnd = swiper.isEnd;\n        if (position >= swiper.minTranslate()) position = swiper.minTranslate();\n        if (position <= swiper.maxTranslate()) position = swiper.maxTranslate();\n        swiper.setTransition(0);\n        swiper.setTranslate(position);\n        swiper.updateProgress();\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n        if (!wasBeginning && swiper.isBeginning || !wasEnd && swiper.isEnd) {\n          swiper.updateSlidesClasses();\n        }\n        if (swiper.params.loop) {\n          swiper.loopFix({\n            direction: newEvent.direction < 0 ? 'next' : 'prev',\n            byMousewheel: true\n          });\n        }\n        if (swiper.params.freeMode.sticky) {\n          // When wheel scrolling starts with sticky (aka snap) enabled, then detect\n          // the end of a momentum scroll by storing recent (N=15?) wheel events.\n          // 1. do all N events have decreasing or same (absolute value) delta?\n          // 2. did all N events arrive in the last M (M=500?) msecs?\n          // 3. does the earliest event have an (absolute value) delta that's\n          //    at least P (P=1?) larger than the most recent event's delta?\n          // 4. does the latest event have a delta that's smaller than Q (Q=6?) pixels?\n          // If 1-4 are \"yes\" then we're near the end of a momentum scroll deceleration.\n          // Snap immediately and ignore remaining wheel events in this scroll.\n          // See comment above for \"remaining wheel events in this scroll\" determination.\n          // If 1-4 aren't satisfied, then wait to snap until 500ms after the last event.\n          clearTimeout(timeout);\n          timeout = undefined;\n          if (recentWheelEvents.length >= 15) {\n            recentWheelEvents.shift(); // only store the last N events\n          }\n          const prevEvent = recentWheelEvents.length ? recentWheelEvents[recentWheelEvents.length - 1] : undefined;\n          const firstEvent = recentWheelEvents[0];\n          recentWheelEvents.push(newEvent);\n          if (prevEvent && (newEvent.delta > prevEvent.delta || newEvent.direction !== prevEvent.direction)) {\n            // Increasing or reverse-sign delta means the user started scrolling again. Clear the wheel event log.\n            recentWheelEvents.splice(0);\n          } else if (recentWheelEvents.length >= 15 && newEvent.time - firstEvent.time < 500 && firstEvent.delta - newEvent.delta >= 1 && newEvent.delta <= 6) {\n            // We're at the end of the deceleration of a momentum scroll, so there's no need\n            // to wait for more events. Snap ASAP on the next tick.\n            // Also, because there's some remaining momentum we'll bias the snap in the\n            // direction of the ongoing scroll because it's better UX for the scroll to snap\n            // in the same direction as the scroll instead of reversing to snap.  Therefore,\n            // if it's already scrolled more than 20% in the current direction, keep going.\n            const snapToThreshold = delta > 0 ? 0.8 : 0.2;\n            lastEventBeforeSnap = newEvent;\n            recentWheelEvents.splice(0);\n            timeout = nextTick(() => {\n              if (swiper.destroyed || !swiper.params) return;\n              swiper.slideToClosest(swiper.params.speed, true, undefined, snapToThreshold);\n            }, 0); // no delay; move on next tick\n          }\n          if (!timeout) {\n            // if we get here, then we haven't detected the end of a momentum scroll, so\n            // we'll consider a scroll \"complete\" when there haven't been any wheel events\n            // for 500ms.\n            timeout = nextTick(() => {\n              if (swiper.destroyed || !swiper.params) return;\n              const snapToThreshold = 0.5;\n              lastEventBeforeSnap = newEvent;\n              recentWheelEvents.splice(0);\n              swiper.slideToClosest(swiper.params.speed, true, undefined, snapToThreshold);\n            }, 500);\n          }\n        }\n\n        // Emit event\n        if (!ignoreWheelEvents) emit('scroll', e);\n\n        // Stop autoplay\n        if (swiper.params.autoplay && swiper.params.autoplay.disableOnInteraction) swiper.autoplay.stop();\n        // Return page scroll on edge positions\n        if (params.releaseOnEdges && (position === swiper.minTranslate() || position === swiper.maxTranslate())) {\n          return true;\n        }\n      }\n    }\n    if (e.preventDefault) e.preventDefault();else e.returnValue = false;\n    return false;\n  }\n  function events(method) {\n    let targetEl = swiper.el;\n    if (swiper.params.mousewheel.eventsTarget !== 'container') {\n      targetEl = document.querySelector(swiper.params.mousewheel.eventsTarget);\n    }\n    targetEl[method]('mouseenter', handleMouseEnter);\n    targetEl[method]('mouseleave', handleMouseLeave);\n    targetEl[method]('wheel', handle);\n  }\n  function enable() {\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.removeEventListener('wheel', handle);\n      return true;\n    }\n    if (swiper.mousewheel.enabled) return false;\n    events('addEventListener');\n    swiper.mousewheel.enabled = true;\n    return true;\n  }\n  function disable() {\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.addEventListener(event, handle);\n      return true;\n    }\n    if (!swiper.mousewheel.enabled) return false;\n    events('removeEventListener');\n    swiper.mousewheel.enabled = false;\n    return true;\n  }\n  on('init', () => {\n    if (!swiper.params.mousewheel.enabled && swiper.params.cssMode) {\n      disable();\n    }\n    if (swiper.params.mousewheel.enabled) enable();\n  });\n  on('destroy', () => {\n    if (swiper.params.cssMode) {\n      enable();\n    }\n    if (swiper.mousewheel.enabled) disable();\n  });\n  Object.assign(swiper.mousewheel, {\n    enable,\n    disable\n  });\n}\nexport { Mousewheel as default };", "map": {"version": 3, "names": ["a", "getWindow", "n", "nextTick", "f", "now", "Mousewheel", "_ref", "swiper", "extendParams", "on", "emit", "window", "mousewheel", "enabled", "releaseOnEdges", "invert", "forceToAxis", "sensitivity", "eventsTarget", "thresholdDel<PERSON>", "thresholdTime", "noMousewheelClass", "timeout", "lastScrollTime", "lastEventBeforeSnap", "recentWheelEvents", "normalize", "e", "PIXEL_STEP", "LINE_HEIGHT", "PAGE_HEIGHT", "sX", "sY", "pX", "pY", "detail", "wheelDelta", "wheelDeltaY", "wheelDeltaX", "axis", "HORIZONTAL_AXIS", "deltaY", "deltaX", "shift<PERSON>ey", "deltaMode", "spinX", "spinY", "pixelX", "pixelY", "handleMouseEnter", "mouseEntered", "handleMouseLeave", "animateSlider", "newEvent", "params", "delta", "direction", "isEnd", "loop", "animating", "slideNext", "raw", "isBeginning", "slidePrev", "Date", "getTime", "releaseScroll", "handle", "event", "disableParentSwiper", "target", "closest", "cssMode", "preventDefault", "targetEl", "el", "document", "querySelector", "targetElContainsTarget", "contains", "originalEvent", "rtlFactor", "rtlTranslate", "data", "isHorizontal", "Math", "abs", "positions", "getTranslate", "minTranslate", "maxTranslate", "nested", "stopPropagation", "freeMode", "time", "sign", "length", "shift", "prevEvent", "undefined", "push", "ignoreWheelEvents", "position", "wasBeginning", "wasEnd", "setTransition", "setTranslate", "updateProgress", "updateActiveIndex", "updateSlidesClasses", "loopFix", "byMousewheel", "sticky", "clearTimeout", "firstEvent", "splice", "snapToThreshold", "destroyed", "slideToClosest", "speed", "autoplay", "disableOnInteraction", "stop", "returnValue", "events", "method", "enable", "wrapperEl", "removeEventListener", "disable", "addEventListener", "Object", "assign", "default"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/swiper/modules/mousewheel.mjs"], "sourcesContent": ["import { a as getWindow } from '../shared/ssr-window.esm.mjs';\nimport { n as nextTick, f as now } from '../shared/utils.mjs';\n\n/* eslint-disable consistent-return */\nfunction Mousewheel(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const window = getWindow();\n  extendParams({\n    mousewheel: {\n      enabled: false,\n      releaseOnEdges: false,\n      invert: false,\n      forceToAxis: false,\n      sensitivity: 1,\n      eventsTarget: 'container',\n      thresholdDelta: null,\n      thresholdTime: null,\n      noMousewheelClass: 'swiper-no-mousewheel'\n    }\n  });\n  swiper.mousewheel = {\n    enabled: false\n  };\n  let timeout;\n  let lastScrollTime = now();\n  let lastEventBeforeSnap;\n  const recentWheelEvents = [];\n  function normalize(e) {\n    // Reasonable defaults\n    const PIXEL_STEP = 10;\n    const LINE_HEIGHT = 40;\n    const PAGE_HEIGHT = 800;\n    let sX = 0;\n    let sY = 0; // spinX, spinY\n    let pX = 0;\n    let pY = 0; // pixelX, pixelY\n\n    // Legacy\n    if ('detail' in e) {\n      sY = e.detail;\n    }\n    if ('wheelDelta' in e) {\n      sY = -e.wheelDelta / 120;\n    }\n    if ('wheelDeltaY' in e) {\n      sY = -e.wheelDeltaY / 120;\n    }\n    if ('wheelDeltaX' in e) {\n      sX = -e.wheelDeltaX / 120;\n    }\n\n    // side scrolling on FF with DOMMouseScroll\n    if ('axis' in e && e.axis === e.HORIZONTAL_AXIS) {\n      sX = sY;\n      sY = 0;\n    }\n    pX = sX * PIXEL_STEP;\n    pY = sY * PIXEL_STEP;\n    if ('deltaY' in e) {\n      pY = e.deltaY;\n    }\n    if ('deltaX' in e) {\n      pX = e.deltaX;\n    }\n    if (e.shiftKey && !pX) {\n      // if user scrolls with shift he wants horizontal scroll\n      pX = pY;\n      pY = 0;\n    }\n    if ((pX || pY) && e.deltaMode) {\n      if (e.deltaMode === 1) {\n        // delta in LINE units\n        pX *= LINE_HEIGHT;\n        pY *= LINE_HEIGHT;\n      } else {\n        // delta in PAGE units\n        pX *= PAGE_HEIGHT;\n        pY *= PAGE_HEIGHT;\n      }\n    }\n\n    // Fall-back if spin cannot be determined\n    if (pX && !sX) {\n      sX = pX < 1 ? -1 : 1;\n    }\n    if (pY && !sY) {\n      sY = pY < 1 ? -1 : 1;\n    }\n    return {\n      spinX: sX,\n      spinY: sY,\n      pixelX: pX,\n      pixelY: pY\n    };\n  }\n  function handleMouseEnter() {\n    if (!swiper.enabled) return;\n    swiper.mouseEntered = true;\n  }\n  function handleMouseLeave() {\n    if (!swiper.enabled) return;\n    swiper.mouseEntered = false;\n  }\n  function animateSlider(newEvent) {\n    if (swiper.params.mousewheel.thresholdDelta && newEvent.delta < swiper.params.mousewheel.thresholdDelta) {\n      // Prevent if delta of wheel scroll delta is below configured threshold\n      return false;\n    }\n    if (swiper.params.mousewheel.thresholdTime && now() - lastScrollTime < swiper.params.mousewheel.thresholdTime) {\n      // Prevent if time between scrolls is below configured threshold\n      return false;\n    }\n\n    // If the movement is NOT big enough and\n    // if the last time the user scrolled was too close to the current one (avoid continuously triggering the slider):\n    //   Don't go any further (avoid insignificant scroll movement).\n    if (newEvent.delta >= 6 && now() - lastScrollTime < 60) {\n      // Return false as a default\n      return true;\n    }\n    // If user is scrolling towards the end:\n    //   If the slider hasn't hit the latest slide or\n    //   if the slider is a loop and\n    //   if the slider isn't moving right now:\n    //     Go to next slide and\n    //     emit a scroll event.\n    // Else (the user is scrolling towards the beginning) and\n    // if the slider hasn't hit the first slide or\n    // if the slider is a loop and\n    // if the slider isn't moving right now:\n    //   Go to prev slide and\n    //   emit a scroll event.\n    if (newEvent.direction < 0) {\n      if ((!swiper.isEnd || swiper.params.loop) && !swiper.animating) {\n        swiper.slideNext();\n        emit('scroll', newEvent.raw);\n      }\n    } else if ((!swiper.isBeginning || swiper.params.loop) && !swiper.animating) {\n      swiper.slidePrev();\n      emit('scroll', newEvent.raw);\n    }\n    // If you got here is because an animation has been triggered so store the current time\n    lastScrollTime = new window.Date().getTime();\n    // Return false as a default\n    return false;\n  }\n  function releaseScroll(newEvent) {\n    const params = swiper.params.mousewheel;\n    if (newEvent.direction < 0) {\n      if (swiper.isEnd && !swiper.params.loop && params.releaseOnEdges) {\n        // Return true to animate scroll on edges\n        return true;\n      }\n    } else if (swiper.isBeginning && !swiper.params.loop && params.releaseOnEdges) {\n      // Return true to animate scroll on edges\n      return true;\n    }\n    return false;\n  }\n  function handle(event) {\n    let e = event;\n    let disableParentSwiper = true;\n    if (!swiper.enabled) return;\n\n    // Ignore event if the target or its parents have the swiper-no-mousewheel class\n    if (event.target.closest(`.${swiper.params.mousewheel.noMousewheelClass}`)) return;\n    const params = swiper.params.mousewheel;\n    if (swiper.params.cssMode) {\n      e.preventDefault();\n    }\n    let targetEl = swiper.el;\n    if (swiper.params.mousewheel.eventsTarget !== 'container') {\n      targetEl = document.querySelector(swiper.params.mousewheel.eventsTarget);\n    }\n    const targetElContainsTarget = targetEl && targetEl.contains(e.target);\n    if (!swiper.mouseEntered && !targetElContainsTarget && !params.releaseOnEdges) return true;\n    if (e.originalEvent) e = e.originalEvent; // jquery fix\n    let delta = 0;\n    const rtlFactor = swiper.rtlTranslate ? -1 : 1;\n    const data = normalize(e);\n    if (params.forceToAxis) {\n      if (swiper.isHorizontal()) {\n        if (Math.abs(data.pixelX) > Math.abs(data.pixelY)) delta = -data.pixelX * rtlFactor;else return true;\n      } else if (Math.abs(data.pixelY) > Math.abs(data.pixelX)) delta = -data.pixelY;else return true;\n    } else {\n      delta = Math.abs(data.pixelX) > Math.abs(data.pixelY) ? -data.pixelX * rtlFactor : -data.pixelY;\n    }\n    if (delta === 0) return true;\n    if (params.invert) delta = -delta;\n\n    // Get the scroll positions\n    let positions = swiper.getTranslate() + delta * params.sensitivity;\n    if (positions >= swiper.minTranslate()) positions = swiper.minTranslate();\n    if (positions <= swiper.maxTranslate()) positions = swiper.maxTranslate();\n\n    // When loop is true:\n    //     the disableParentSwiper will be true.\n    // When loop is false:\n    //     if the scroll positions is not on edge,\n    //     then the disableParentSwiper will be true.\n    //     if the scroll on edge positions,\n    //     then the disableParentSwiper will be false.\n    disableParentSwiper = swiper.params.loop ? true : !(positions === swiper.minTranslate() || positions === swiper.maxTranslate());\n    if (disableParentSwiper && swiper.params.nested) e.stopPropagation();\n    if (!swiper.params.freeMode || !swiper.params.freeMode.enabled) {\n      // Register the new event in a variable which stores the relevant data\n      const newEvent = {\n        time: now(),\n        delta: Math.abs(delta),\n        direction: Math.sign(delta),\n        raw: event\n      };\n\n      // Keep the most recent events\n      if (recentWheelEvents.length >= 2) {\n        recentWheelEvents.shift(); // only store the last N events\n      }\n\n      const prevEvent = recentWheelEvents.length ? recentWheelEvents[recentWheelEvents.length - 1] : undefined;\n      recentWheelEvents.push(newEvent);\n\n      // If there is at least one previous recorded event:\n      //   If direction has changed or\n      //   if the scroll is quicker than the previous one:\n      //     Animate the slider.\n      // Else (this is the first time the wheel is moved):\n      //     Animate the slider.\n      if (prevEvent) {\n        if (newEvent.direction !== prevEvent.direction || newEvent.delta > prevEvent.delta || newEvent.time > prevEvent.time + 150) {\n          animateSlider(newEvent);\n        }\n      } else {\n        animateSlider(newEvent);\n      }\n\n      // If it's time to release the scroll:\n      //   Return now so you don't hit the preventDefault.\n      if (releaseScroll(newEvent)) {\n        return true;\n      }\n    } else {\n      // Freemode or scrollContainer:\n\n      // If we recently snapped after a momentum scroll, then ignore wheel events\n      // to give time for the deceleration to finish. Stop ignoring after 500 msecs\n      // or if it's a new scroll (larger delta or inverse sign as last event before\n      // an end-of-momentum snap).\n      const newEvent = {\n        time: now(),\n        delta: Math.abs(delta),\n        direction: Math.sign(delta)\n      };\n      const ignoreWheelEvents = lastEventBeforeSnap && newEvent.time < lastEventBeforeSnap.time + 500 && newEvent.delta <= lastEventBeforeSnap.delta && newEvent.direction === lastEventBeforeSnap.direction;\n      if (!ignoreWheelEvents) {\n        lastEventBeforeSnap = undefined;\n        let position = swiper.getTranslate() + delta * params.sensitivity;\n        const wasBeginning = swiper.isBeginning;\n        const wasEnd = swiper.isEnd;\n        if (position >= swiper.minTranslate()) position = swiper.minTranslate();\n        if (position <= swiper.maxTranslate()) position = swiper.maxTranslate();\n        swiper.setTransition(0);\n        swiper.setTranslate(position);\n        swiper.updateProgress();\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n        if (!wasBeginning && swiper.isBeginning || !wasEnd && swiper.isEnd) {\n          swiper.updateSlidesClasses();\n        }\n        if (swiper.params.loop) {\n          swiper.loopFix({\n            direction: newEvent.direction < 0 ? 'next' : 'prev',\n            byMousewheel: true\n          });\n        }\n        if (swiper.params.freeMode.sticky) {\n          // When wheel scrolling starts with sticky (aka snap) enabled, then detect\n          // the end of a momentum scroll by storing recent (N=15?) wheel events.\n          // 1. do all N events have decreasing or same (absolute value) delta?\n          // 2. did all N events arrive in the last M (M=500?) msecs?\n          // 3. does the earliest event have an (absolute value) delta that's\n          //    at least P (P=1?) larger than the most recent event's delta?\n          // 4. does the latest event have a delta that's smaller than Q (Q=6?) pixels?\n          // If 1-4 are \"yes\" then we're near the end of a momentum scroll deceleration.\n          // Snap immediately and ignore remaining wheel events in this scroll.\n          // See comment above for \"remaining wheel events in this scroll\" determination.\n          // If 1-4 aren't satisfied, then wait to snap until 500ms after the last event.\n          clearTimeout(timeout);\n          timeout = undefined;\n          if (recentWheelEvents.length >= 15) {\n            recentWheelEvents.shift(); // only store the last N events\n          }\n\n          const prevEvent = recentWheelEvents.length ? recentWheelEvents[recentWheelEvents.length - 1] : undefined;\n          const firstEvent = recentWheelEvents[0];\n          recentWheelEvents.push(newEvent);\n          if (prevEvent && (newEvent.delta > prevEvent.delta || newEvent.direction !== prevEvent.direction)) {\n            // Increasing or reverse-sign delta means the user started scrolling again. Clear the wheel event log.\n            recentWheelEvents.splice(0);\n          } else if (recentWheelEvents.length >= 15 && newEvent.time - firstEvent.time < 500 && firstEvent.delta - newEvent.delta >= 1 && newEvent.delta <= 6) {\n            // We're at the end of the deceleration of a momentum scroll, so there's no need\n            // to wait for more events. Snap ASAP on the next tick.\n            // Also, because there's some remaining momentum we'll bias the snap in the\n            // direction of the ongoing scroll because it's better UX for the scroll to snap\n            // in the same direction as the scroll instead of reversing to snap.  Therefore,\n            // if it's already scrolled more than 20% in the current direction, keep going.\n            const snapToThreshold = delta > 0 ? 0.8 : 0.2;\n            lastEventBeforeSnap = newEvent;\n            recentWheelEvents.splice(0);\n            timeout = nextTick(() => {\n              if (swiper.destroyed || !swiper.params) return;\n              swiper.slideToClosest(swiper.params.speed, true, undefined, snapToThreshold);\n            }, 0); // no delay; move on next tick\n          }\n\n          if (!timeout) {\n            // if we get here, then we haven't detected the end of a momentum scroll, so\n            // we'll consider a scroll \"complete\" when there haven't been any wheel events\n            // for 500ms.\n            timeout = nextTick(() => {\n              if (swiper.destroyed || !swiper.params) return;\n              const snapToThreshold = 0.5;\n              lastEventBeforeSnap = newEvent;\n              recentWheelEvents.splice(0);\n              swiper.slideToClosest(swiper.params.speed, true, undefined, snapToThreshold);\n            }, 500);\n          }\n        }\n\n        // Emit event\n        if (!ignoreWheelEvents) emit('scroll', e);\n\n        // Stop autoplay\n        if (swiper.params.autoplay && swiper.params.autoplay.disableOnInteraction) swiper.autoplay.stop();\n        // Return page scroll on edge positions\n        if (params.releaseOnEdges && (position === swiper.minTranslate() || position === swiper.maxTranslate())) {\n          return true;\n        }\n      }\n    }\n    if (e.preventDefault) e.preventDefault();else e.returnValue = false;\n    return false;\n  }\n  function events(method) {\n    let targetEl = swiper.el;\n    if (swiper.params.mousewheel.eventsTarget !== 'container') {\n      targetEl = document.querySelector(swiper.params.mousewheel.eventsTarget);\n    }\n    targetEl[method]('mouseenter', handleMouseEnter);\n    targetEl[method]('mouseleave', handleMouseLeave);\n    targetEl[method]('wheel', handle);\n  }\n  function enable() {\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.removeEventListener('wheel', handle);\n      return true;\n    }\n    if (swiper.mousewheel.enabled) return false;\n    events('addEventListener');\n    swiper.mousewheel.enabled = true;\n    return true;\n  }\n  function disable() {\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.addEventListener(event, handle);\n      return true;\n    }\n    if (!swiper.mousewheel.enabled) return false;\n    events('removeEventListener');\n    swiper.mousewheel.enabled = false;\n    return true;\n  }\n  on('init', () => {\n    if (!swiper.params.mousewheel.enabled && swiper.params.cssMode) {\n      disable();\n    }\n    if (swiper.params.mousewheel.enabled) enable();\n  });\n  on('destroy', () => {\n    if (swiper.params.cssMode) {\n      enable();\n    }\n    if (swiper.mousewheel.enabled) disable();\n  });\n  Object.assign(swiper.mousewheel, {\n    enable,\n    disable\n  });\n}\n\nexport { Mousewheel as default };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,SAAS,QAAQ,8BAA8B;AAC7D,SAASC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,GAAG,QAAQ,qBAAqB;;AAE7D;AACA,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC,EAAE;IACFC;EACF,CAAC,GAAGJ,IAAI;EACR,MAAMK,MAAM,GAAGX,SAAS,CAAC,CAAC;EAC1BQ,YAAY,CAAC;IACXI,UAAU,EAAE;MACVC,OAAO,EAAE,KAAK;MACdC,cAAc,EAAE,KAAK;MACrBC,MAAM,EAAE,KAAK;MACbC,WAAW,EAAE,KAAK;MAClBC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE,WAAW;MACzBC,cAAc,EAAE,IAAI;MACpBC,aAAa,EAAE,IAAI;MACnBC,iBAAiB,EAAE;IACrB;EACF,CAAC,CAAC;EACFd,MAAM,CAACK,UAAU,GAAG;IAClBC,OAAO,EAAE;EACX,CAAC;EACD,IAAIS,OAAO;EACX,IAAIC,cAAc,GAAGnB,GAAG,CAAC,CAAC;EAC1B,IAAIoB,mBAAmB;EACvB,MAAMC,iBAAiB,GAAG,EAAE;EAC5B,SAASC,SAASA,CAACC,CAAC,EAAE;IACpB;IACA,MAAMC,UAAU,GAAG,EAAE;IACrB,MAAMC,WAAW,GAAG,EAAE;IACtB,MAAMC,WAAW,GAAG,GAAG;IACvB,IAAIC,EAAE,GAAG,CAAC;IACV,IAAIC,EAAE,GAAG,CAAC,CAAC,CAAC;IACZ,IAAIC,EAAE,GAAG,CAAC;IACV,IAAIC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAEZ;IACA,IAAI,QAAQ,IAAIP,CAAC,EAAE;MACjBK,EAAE,GAAGL,CAAC,CAACQ,MAAM;IACf;IACA,IAAI,YAAY,IAAIR,CAAC,EAAE;MACrBK,EAAE,GAAG,CAACL,CAAC,CAACS,UAAU,GAAG,GAAG;IAC1B;IACA,IAAI,aAAa,IAAIT,CAAC,EAAE;MACtBK,EAAE,GAAG,CAACL,CAAC,CAACU,WAAW,GAAG,GAAG;IAC3B;IACA,IAAI,aAAa,IAAIV,CAAC,EAAE;MACtBI,EAAE,GAAG,CAACJ,CAAC,CAACW,WAAW,GAAG,GAAG;IAC3B;;IAEA;IACA,IAAI,MAAM,IAAIX,CAAC,IAAIA,CAAC,CAACY,IAAI,KAAKZ,CAAC,CAACa,eAAe,EAAE;MAC/CT,EAAE,GAAGC,EAAE;MACPA,EAAE,GAAG,CAAC;IACR;IACAC,EAAE,GAAGF,EAAE,GAAGH,UAAU;IACpBM,EAAE,GAAGF,EAAE,GAAGJ,UAAU;IACpB,IAAI,QAAQ,IAAID,CAAC,EAAE;MACjBO,EAAE,GAAGP,CAAC,CAACc,MAAM;IACf;IACA,IAAI,QAAQ,IAAId,CAAC,EAAE;MACjBM,EAAE,GAAGN,CAAC,CAACe,MAAM;IACf;IACA,IAAIf,CAAC,CAACgB,QAAQ,IAAI,CAACV,EAAE,EAAE;MACrB;MACAA,EAAE,GAAGC,EAAE;MACPA,EAAE,GAAG,CAAC;IACR;IACA,IAAI,CAACD,EAAE,IAAIC,EAAE,KAAKP,CAAC,CAACiB,SAAS,EAAE;MAC7B,IAAIjB,CAAC,CAACiB,SAAS,KAAK,CAAC,EAAE;QACrB;QACAX,EAAE,IAAIJ,WAAW;QACjBK,EAAE,IAAIL,WAAW;MACnB,CAAC,MAAM;QACL;QACAI,EAAE,IAAIH,WAAW;QACjBI,EAAE,IAAIJ,WAAW;MACnB;IACF;;IAEA;IACA,IAAIG,EAAE,IAAI,CAACF,EAAE,EAAE;MACbA,EAAE,GAAGE,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACtB;IACA,IAAIC,EAAE,IAAI,CAACF,EAAE,EAAE;MACbA,EAAE,GAAGE,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACtB;IACA,OAAO;MACLW,KAAK,EAAEd,EAAE;MACTe,KAAK,EAAEd,EAAE;MACTe,MAAM,EAAEd,EAAE;MACVe,MAAM,EAAEd;IACV,CAAC;EACH;EACA,SAASe,gBAAgBA,CAAA,EAAG;IAC1B,IAAI,CAAC1C,MAAM,CAACM,OAAO,EAAE;IACrBN,MAAM,CAAC2C,YAAY,GAAG,IAAI;EAC5B;EACA,SAASC,gBAAgBA,CAAA,EAAG;IAC1B,IAAI,CAAC5C,MAAM,CAACM,OAAO,EAAE;IACrBN,MAAM,CAAC2C,YAAY,GAAG,KAAK;EAC7B;EACA,SAASE,aAAaA,CAACC,QAAQ,EAAE;IAC/B,IAAI9C,MAAM,CAAC+C,MAAM,CAAC1C,UAAU,CAACO,cAAc,IAAIkC,QAAQ,CAACE,KAAK,GAAGhD,MAAM,CAAC+C,MAAM,CAAC1C,UAAU,CAACO,cAAc,EAAE;MACvG;MACA,OAAO,KAAK;IACd;IACA,IAAIZ,MAAM,CAAC+C,MAAM,CAAC1C,UAAU,CAACQ,aAAa,IAAIhB,GAAG,CAAC,CAAC,GAAGmB,cAAc,GAAGhB,MAAM,CAAC+C,MAAM,CAAC1C,UAAU,CAACQ,aAAa,EAAE;MAC7G;MACA,OAAO,KAAK;IACd;;IAEA;IACA;IACA;IACA,IAAIiC,QAAQ,CAACE,KAAK,IAAI,CAAC,IAAInD,GAAG,CAAC,CAAC,GAAGmB,cAAc,GAAG,EAAE,EAAE;MACtD;MACA,OAAO,IAAI;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI8B,QAAQ,CAACG,SAAS,GAAG,CAAC,EAAE;MAC1B,IAAI,CAAC,CAACjD,MAAM,CAACkD,KAAK,IAAIlD,MAAM,CAAC+C,MAAM,CAACI,IAAI,KAAK,CAACnD,MAAM,CAACoD,SAAS,EAAE;QAC9DpD,MAAM,CAACqD,SAAS,CAAC,CAAC;QAClBlD,IAAI,CAAC,QAAQ,EAAE2C,QAAQ,CAACQ,GAAG,CAAC;MAC9B;IACF,CAAC,MAAM,IAAI,CAAC,CAACtD,MAAM,CAACuD,WAAW,IAAIvD,MAAM,CAAC+C,MAAM,CAACI,IAAI,KAAK,CAACnD,MAAM,CAACoD,SAAS,EAAE;MAC3EpD,MAAM,CAACwD,SAAS,CAAC,CAAC;MAClBrD,IAAI,CAAC,QAAQ,EAAE2C,QAAQ,CAACQ,GAAG,CAAC;IAC9B;IACA;IACAtC,cAAc,GAAG,IAAIZ,MAAM,CAACqD,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IAC5C;IACA,OAAO,KAAK;EACd;EACA,SAASC,aAAaA,CAACb,QAAQ,EAAE;IAC/B,MAAMC,MAAM,GAAG/C,MAAM,CAAC+C,MAAM,CAAC1C,UAAU;IACvC,IAAIyC,QAAQ,CAACG,SAAS,GAAG,CAAC,EAAE;MAC1B,IAAIjD,MAAM,CAACkD,KAAK,IAAI,CAAClD,MAAM,CAAC+C,MAAM,CAACI,IAAI,IAAIJ,MAAM,CAACxC,cAAc,EAAE;QAChE;QACA,OAAO,IAAI;MACb;IACF,CAAC,MAAM,IAAIP,MAAM,CAACuD,WAAW,IAAI,CAACvD,MAAM,CAAC+C,MAAM,CAACI,IAAI,IAAIJ,MAAM,CAACxC,cAAc,EAAE;MAC7E;MACA,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EACA,SAASqD,MAAMA,CAACC,KAAK,EAAE;IACrB,IAAIzC,CAAC,GAAGyC,KAAK;IACb,IAAIC,mBAAmB,GAAG,IAAI;IAC9B,IAAI,CAAC9D,MAAM,CAACM,OAAO,EAAE;;IAErB;IACA,IAAIuD,KAAK,CAACE,MAAM,CAACC,OAAO,CAAC,IAAIhE,MAAM,CAAC+C,MAAM,CAAC1C,UAAU,CAACS,iBAAiB,EAAE,CAAC,EAAE;IAC5E,MAAMiC,MAAM,GAAG/C,MAAM,CAAC+C,MAAM,CAAC1C,UAAU;IACvC,IAAIL,MAAM,CAAC+C,MAAM,CAACkB,OAAO,EAAE;MACzB7C,CAAC,CAAC8C,cAAc,CAAC,CAAC;IACpB;IACA,IAAIC,QAAQ,GAAGnE,MAAM,CAACoE,EAAE;IACxB,IAAIpE,MAAM,CAAC+C,MAAM,CAAC1C,UAAU,CAACM,YAAY,KAAK,WAAW,EAAE;MACzDwD,QAAQ,GAAGE,QAAQ,CAACC,aAAa,CAACtE,MAAM,CAAC+C,MAAM,CAAC1C,UAAU,CAACM,YAAY,CAAC;IAC1E;IACA,MAAM4D,sBAAsB,GAAGJ,QAAQ,IAAIA,QAAQ,CAACK,QAAQ,CAACpD,CAAC,CAAC2C,MAAM,CAAC;IACtE,IAAI,CAAC/D,MAAM,CAAC2C,YAAY,IAAI,CAAC4B,sBAAsB,IAAI,CAACxB,MAAM,CAACxC,cAAc,EAAE,OAAO,IAAI;IAC1F,IAAIa,CAAC,CAACqD,aAAa,EAAErD,CAAC,GAAGA,CAAC,CAACqD,aAAa,CAAC,CAAC;IAC1C,IAAIzB,KAAK,GAAG,CAAC;IACb,MAAM0B,SAAS,GAAG1E,MAAM,CAAC2E,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;IAC9C,MAAMC,IAAI,GAAGzD,SAAS,CAACC,CAAC,CAAC;IACzB,IAAI2B,MAAM,CAACtC,WAAW,EAAE;MACtB,IAAIT,MAAM,CAAC6E,YAAY,CAAC,CAAC,EAAE;QACzB,IAAIC,IAAI,CAACC,GAAG,CAACH,IAAI,CAACpC,MAAM,CAAC,GAAGsC,IAAI,CAACC,GAAG,CAACH,IAAI,CAACnC,MAAM,CAAC,EAAEO,KAAK,GAAG,CAAC4B,IAAI,CAACpC,MAAM,GAAGkC,SAAS,CAAC,KAAK,OAAO,IAAI;MACtG,CAAC,MAAM,IAAII,IAAI,CAACC,GAAG,CAACH,IAAI,CAACnC,MAAM,CAAC,GAAGqC,IAAI,CAACC,GAAG,CAACH,IAAI,CAACpC,MAAM,CAAC,EAAEQ,KAAK,GAAG,CAAC4B,IAAI,CAACnC,MAAM,CAAC,KAAK,OAAO,IAAI;IACjG,CAAC,MAAM;MACLO,KAAK,GAAG8B,IAAI,CAACC,GAAG,CAACH,IAAI,CAACpC,MAAM,CAAC,GAAGsC,IAAI,CAACC,GAAG,CAACH,IAAI,CAACnC,MAAM,CAAC,GAAG,CAACmC,IAAI,CAACpC,MAAM,GAAGkC,SAAS,GAAG,CAACE,IAAI,CAACnC,MAAM;IACjG;IACA,IAAIO,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAC5B,IAAID,MAAM,CAACvC,MAAM,EAAEwC,KAAK,GAAG,CAACA,KAAK;;IAEjC;IACA,IAAIgC,SAAS,GAAGhF,MAAM,CAACiF,YAAY,CAAC,CAAC,GAAGjC,KAAK,GAAGD,MAAM,CAACrC,WAAW;IAClE,IAAIsE,SAAS,IAAIhF,MAAM,CAACkF,YAAY,CAAC,CAAC,EAAEF,SAAS,GAAGhF,MAAM,CAACkF,YAAY,CAAC,CAAC;IACzE,IAAIF,SAAS,IAAIhF,MAAM,CAACmF,YAAY,CAAC,CAAC,EAAEH,SAAS,GAAGhF,MAAM,CAACmF,YAAY,CAAC,CAAC;;IAEzE;IACA;IACA;IACA;IACA;IACA;IACA;IACArB,mBAAmB,GAAG9D,MAAM,CAAC+C,MAAM,CAACI,IAAI,GAAG,IAAI,GAAG,EAAE6B,SAAS,KAAKhF,MAAM,CAACkF,YAAY,CAAC,CAAC,IAAIF,SAAS,KAAKhF,MAAM,CAACmF,YAAY,CAAC,CAAC,CAAC;IAC/H,IAAIrB,mBAAmB,IAAI9D,MAAM,CAAC+C,MAAM,CAACqC,MAAM,EAAEhE,CAAC,CAACiE,eAAe,CAAC,CAAC;IACpE,IAAI,CAACrF,MAAM,CAAC+C,MAAM,CAACuC,QAAQ,IAAI,CAACtF,MAAM,CAAC+C,MAAM,CAACuC,QAAQ,CAAChF,OAAO,EAAE;MAC9D;MACA,MAAMwC,QAAQ,GAAG;QACfyC,IAAI,EAAE1F,GAAG,CAAC,CAAC;QACXmD,KAAK,EAAE8B,IAAI,CAACC,GAAG,CAAC/B,KAAK,CAAC;QACtBC,SAAS,EAAE6B,IAAI,CAACU,IAAI,CAACxC,KAAK,CAAC;QAC3BM,GAAG,EAAEO;MACP,CAAC;;MAED;MACA,IAAI3C,iBAAiB,CAACuE,MAAM,IAAI,CAAC,EAAE;QACjCvE,iBAAiB,CAACwE,KAAK,CAAC,CAAC,CAAC,CAAC;MAC7B;MAEA,MAAMC,SAAS,GAAGzE,iBAAiB,CAACuE,MAAM,GAAGvE,iBAAiB,CAACA,iBAAiB,CAACuE,MAAM,GAAG,CAAC,CAAC,GAAGG,SAAS;MACxG1E,iBAAiB,CAAC2E,IAAI,CAAC/C,QAAQ,CAAC;;MAEhC;MACA;MACA;MACA;MACA;MACA;MACA,IAAI6C,SAAS,EAAE;QACb,IAAI7C,QAAQ,CAACG,SAAS,KAAK0C,SAAS,CAAC1C,SAAS,IAAIH,QAAQ,CAACE,KAAK,GAAG2C,SAAS,CAAC3C,KAAK,IAAIF,QAAQ,CAACyC,IAAI,GAAGI,SAAS,CAACJ,IAAI,GAAG,GAAG,EAAE;UAC1H1C,aAAa,CAACC,QAAQ,CAAC;QACzB;MACF,CAAC,MAAM;QACLD,aAAa,CAACC,QAAQ,CAAC;MACzB;;MAEA;MACA;MACA,IAAIa,aAAa,CAACb,QAAQ,CAAC,EAAE;QAC3B,OAAO,IAAI;MACb;IACF,CAAC,MAAM;MACL;;MAEA;MACA;MACA;MACA;MACA,MAAMA,QAAQ,GAAG;QACfyC,IAAI,EAAE1F,GAAG,CAAC,CAAC;QACXmD,KAAK,EAAE8B,IAAI,CAACC,GAAG,CAAC/B,KAAK,CAAC;QACtBC,SAAS,EAAE6B,IAAI,CAACU,IAAI,CAACxC,KAAK;MAC5B,CAAC;MACD,MAAM8C,iBAAiB,GAAG7E,mBAAmB,IAAI6B,QAAQ,CAACyC,IAAI,GAAGtE,mBAAmB,CAACsE,IAAI,GAAG,GAAG,IAAIzC,QAAQ,CAACE,KAAK,IAAI/B,mBAAmB,CAAC+B,KAAK,IAAIF,QAAQ,CAACG,SAAS,KAAKhC,mBAAmB,CAACgC,SAAS;MACtM,IAAI,CAAC6C,iBAAiB,EAAE;QACtB7E,mBAAmB,GAAG2E,SAAS;QAC/B,IAAIG,QAAQ,GAAG/F,MAAM,CAACiF,YAAY,CAAC,CAAC,GAAGjC,KAAK,GAAGD,MAAM,CAACrC,WAAW;QACjE,MAAMsF,YAAY,GAAGhG,MAAM,CAACuD,WAAW;QACvC,MAAM0C,MAAM,GAAGjG,MAAM,CAACkD,KAAK;QAC3B,IAAI6C,QAAQ,IAAI/F,MAAM,CAACkF,YAAY,CAAC,CAAC,EAAEa,QAAQ,GAAG/F,MAAM,CAACkF,YAAY,CAAC,CAAC;QACvE,IAAIa,QAAQ,IAAI/F,MAAM,CAACmF,YAAY,CAAC,CAAC,EAAEY,QAAQ,GAAG/F,MAAM,CAACmF,YAAY,CAAC,CAAC;QACvEnF,MAAM,CAACkG,aAAa,CAAC,CAAC,CAAC;QACvBlG,MAAM,CAACmG,YAAY,CAACJ,QAAQ,CAAC;QAC7B/F,MAAM,CAACoG,cAAc,CAAC,CAAC;QACvBpG,MAAM,CAACqG,iBAAiB,CAAC,CAAC;QAC1BrG,MAAM,CAACsG,mBAAmB,CAAC,CAAC;QAC5B,IAAI,CAACN,YAAY,IAAIhG,MAAM,CAACuD,WAAW,IAAI,CAAC0C,MAAM,IAAIjG,MAAM,CAACkD,KAAK,EAAE;UAClElD,MAAM,CAACsG,mBAAmB,CAAC,CAAC;QAC9B;QACA,IAAItG,MAAM,CAAC+C,MAAM,CAACI,IAAI,EAAE;UACtBnD,MAAM,CAACuG,OAAO,CAAC;YACbtD,SAAS,EAAEH,QAAQ,CAACG,SAAS,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM;YACnDuD,YAAY,EAAE;UAChB,CAAC,CAAC;QACJ;QACA,IAAIxG,MAAM,CAAC+C,MAAM,CAACuC,QAAQ,CAACmB,MAAM,EAAE;UACjC;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACAC,YAAY,CAAC3F,OAAO,CAAC;UACrBA,OAAO,GAAG6E,SAAS;UACnB,IAAI1E,iBAAiB,CAACuE,MAAM,IAAI,EAAE,EAAE;YAClCvE,iBAAiB,CAACwE,KAAK,CAAC,CAAC,CAAC,CAAC;UAC7B;UAEA,MAAMC,SAAS,GAAGzE,iBAAiB,CAACuE,MAAM,GAAGvE,iBAAiB,CAACA,iBAAiB,CAACuE,MAAM,GAAG,CAAC,CAAC,GAAGG,SAAS;UACxG,MAAMe,UAAU,GAAGzF,iBAAiB,CAAC,CAAC,CAAC;UACvCA,iBAAiB,CAAC2E,IAAI,CAAC/C,QAAQ,CAAC;UAChC,IAAI6C,SAAS,KAAK7C,QAAQ,CAACE,KAAK,GAAG2C,SAAS,CAAC3C,KAAK,IAAIF,QAAQ,CAACG,SAAS,KAAK0C,SAAS,CAAC1C,SAAS,CAAC,EAAE;YACjG;YACA/B,iBAAiB,CAAC0F,MAAM,CAAC,CAAC,CAAC;UAC7B,CAAC,MAAM,IAAI1F,iBAAiB,CAACuE,MAAM,IAAI,EAAE,IAAI3C,QAAQ,CAACyC,IAAI,GAAGoB,UAAU,CAACpB,IAAI,GAAG,GAAG,IAAIoB,UAAU,CAAC3D,KAAK,GAAGF,QAAQ,CAACE,KAAK,IAAI,CAAC,IAAIF,QAAQ,CAACE,KAAK,IAAI,CAAC,EAAE;YACnJ;YACA;YACA;YACA;YACA;YACA;YACA,MAAM6D,eAAe,GAAG7D,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;YAC7C/B,mBAAmB,GAAG6B,QAAQ;YAC9B5B,iBAAiB,CAAC0F,MAAM,CAAC,CAAC,CAAC;YAC3B7F,OAAO,GAAGpB,QAAQ,CAAC,MAAM;cACvB,IAAIK,MAAM,CAAC8G,SAAS,IAAI,CAAC9G,MAAM,CAAC+C,MAAM,EAAE;cACxC/C,MAAM,CAAC+G,cAAc,CAAC/G,MAAM,CAAC+C,MAAM,CAACiE,KAAK,EAAE,IAAI,EAAEpB,SAAS,EAAEiB,eAAe,CAAC;YAC9E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACT;UAEA,IAAI,CAAC9F,OAAO,EAAE;YACZ;YACA;YACA;YACAA,OAAO,GAAGpB,QAAQ,CAAC,MAAM;cACvB,IAAIK,MAAM,CAAC8G,SAAS,IAAI,CAAC9G,MAAM,CAAC+C,MAAM,EAAE;cACxC,MAAM8D,eAAe,GAAG,GAAG;cAC3B5F,mBAAmB,GAAG6B,QAAQ;cAC9B5B,iBAAiB,CAAC0F,MAAM,CAAC,CAAC,CAAC;cAC3B5G,MAAM,CAAC+G,cAAc,CAAC/G,MAAM,CAAC+C,MAAM,CAACiE,KAAK,EAAE,IAAI,EAAEpB,SAAS,EAAEiB,eAAe,CAAC;YAC9E,CAAC,EAAE,GAAG,CAAC;UACT;QACF;;QAEA;QACA,IAAI,CAACf,iBAAiB,EAAE3F,IAAI,CAAC,QAAQ,EAAEiB,CAAC,CAAC;;QAEzC;QACA,IAAIpB,MAAM,CAAC+C,MAAM,CAACkE,QAAQ,IAAIjH,MAAM,CAAC+C,MAAM,CAACkE,QAAQ,CAACC,oBAAoB,EAAElH,MAAM,CAACiH,QAAQ,CAACE,IAAI,CAAC,CAAC;QACjG;QACA,IAAIpE,MAAM,CAACxC,cAAc,KAAKwF,QAAQ,KAAK/F,MAAM,CAACkF,YAAY,CAAC,CAAC,IAAIa,QAAQ,KAAK/F,MAAM,CAACmF,YAAY,CAAC,CAAC,CAAC,EAAE;UACvG,OAAO,IAAI;QACb;MACF;IACF;IACA,IAAI/D,CAAC,CAAC8C,cAAc,EAAE9C,CAAC,CAAC8C,cAAc,CAAC,CAAC,CAAC,KAAK9C,CAAC,CAACgG,WAAW,GAAG,KAAK;IACnE,OAAO,KAAK;EACd;EACA,SAASC,MAAMA,CAACC,MAAM,EAAE;IACtB,IAAInD,QAAQ,GAAGnE,MAAM,CAACoE,EAAE;IACxB,IAAIpE,MAAM,CAAC+C,MAAM,CAAC1C,UAAU,CAACM,YAAY,KAAK,WAAW,EAAE;MACzDwD,QAAQ,GAAGE,QAAQ,CAACC,aAAa,CAACtE,MAAM,CAAC+C,MAAM,CAAC1C,UAAU,CAACM,YAAY,CAAC;IAC1E;IACAwD,QAAQ,CAACmD,MAAM,CAAC,CAAC,YAAY,EAAE5E,gBAAgB,CAAC;IAChDyB,QAAQ,CAACmD,MAAM,CAAC,CAAC,YAAY,EAAE1E,gBAAgB,CAAC;IAChDuB,QAAQ,CAACmD,MAAM,CAAC,CAAC,OAAO,EAAE1D,MAAM,CAAC;EACnC;EACA,SAAS2D,MAAMA,CAAA,EAAG;IAChB,IAAIvH,MAAM,CAAC+C,MAAM,CAACkB,OAAO,EAAE;MACzBjE,MAAM,CAACwH,SAAS,CAACC,mBAAmB,CAAC,OAAO,EAAE7D,MAAM,CAAC;MACrD,OAAO,IAAI;IACb;IACA,IAAI5D,MAAM,CAACK,UAAU,CAACC,OAAO,EAAE,OAAO,KAAK;IAC3C+G,MAAM,CAAC,kBAAkB,CAAC;IAC1BrH,MAAM,CAACK,UAAU,CAACC,OAAO,GAAG,IAAI;IAChC,OAAO,IAAI;EACb;EACA,SAASoH,OAAOA,CAAA,EAAG;IACjB,IAAI1H,MAAM,CAAC+C,MAAM,CAACkB,OAAO,EAAE;MACzBjE,MAAM,CAACwH,SAAS,CAACG,gBAAgB,CAAC9D,KAAK,EAAED,MAAM,CAAC;MAChD,OAAO,IAAI;IACb;IACA,IAAI,CAAC5D,MAAM,CAACK,UAAU,CAACC,OAAO,EAAE,OAAO,KAAK;IAC5C+G,MAAM,CAAC,qBAAqB,CAAC;IAC7BrH,MAAM,CAACK,UAAU,CAACC,OAAO,GAAG,KAAK;IACjC,OAAO,IAAI;EACb;EACAJ,EAAE,CAAC,MAAM,EAAE,MAAM;IACf,IAAI,CAACF,MAAM,CAAC+C,MAAM,CAAC1C,UAAU,CAACC,OAAO,IAAIN,MAAM,CAAC+C,MAAM,CAACkB,OAAO,EAAE;MAC9DyD,OAAO,CAAC,CAAC;IACX;IACA,IAAI1H,MAAM,CAAC+C,MAAM,CAAC1C,UAAU,CAACC,OAAO,EAAEiH,MAAM,CAAC,CAAC;EAChD,CAAC,CAAC;EACFrH,EAAE,CAAC,SAAS,EAAE,MAAM;IAClB,IAAIF,MAAM,CAAC+C,MAAM,CAACkB,OAAO,EAAE;MACzBsD,MAAM,CAAC,CAAC;IACV;IACA,IAAIvH,MAAM,CAACK,UAAU,CAACC,OAAO,EAAEoH,OAAO,CAAC,CAAC;EAC1C,CAAC,CAAC;EACFE,MAAM,CAACC,MAAM,CAAC7H,MAAM,CAACK,UAAU,EAAE;IAC/BkH,MAAM;IACNG;EACF,CAAC,CAAC;AACJ;AAEA,SAAS5H,UAAU,IAAIgI,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}