{"ast": null, "code": "import * as React from 'react';\nexport class BsPrefixComponent extends React.Component {}\n\n// Need to use this instead of typeof Component to get proper type checking.\n\nexport function getOverlayDirection(placement, isRTL) {\n  let bsDirection = placement;\n  if (placement === 'left') {\n    bsDirection = isRTL ? 'end' : 'start';\n  } else if (placement === 'right') {\n    bsDirection = isRTL ? 'start' : 'end';\n  }\n  return bsDirection;\n}", "map": {"version": 3, "names": ["React", "BsPrefixComponent", "Component", "getOverlayDirection", "placement", "isRTL", "bsDirection"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/react-bootstrap/esm/helpers.js"], "sourcesContent": ["import * as React from 'react';\nexport class BsPrefixComponent extends React.Component {}\n\n// Need to use this instead of typeof Component to get proper type checking.\n\nexport function getOverlayDirection(placement, isRTL) {\n  let bsDirection = placement;\n  if (placement === 'left') {\n    bsDirection = isRTL ? 'end' : 'start';\n  } else if (placement === 'right') {\n    bsDirection = isRTL ? 'start' : 'end';\n  }\n  return bsDirection;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,iBAAiB,SAASD,KAAK,CAACE,SAAS,CAAC;;AAEvD;;AAEA,OAAO,SAASC,mBAAmBA,CAACC,SAAS,EAAEC,KAAK,EAAE;EACpD,IAAIC,WAAW,GAAGF,SAAS;EAC3B,IAAIA,SAAS,KAAK,MAAM,EAAE;IACxBE,WAAW,GAAGD,KAAK,GAAG,KAAK,GAAG,OAAO;EACvC,CAAC,MAAM,IAAID,SAAS,KAAK,OAAO,EAAE;IAChCE,WAAW,GAAGD,KAAK,GAAG,OAAO,GAAG,KAAK;EACvC;EACA,OAAOC,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}