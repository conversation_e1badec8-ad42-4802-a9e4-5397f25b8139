{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\LaravelReact\\\\frontend-ui\\\\src\\\\home\\\\Banner.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Link } from \"react-router-dom\";\nimport SelectedCategory from \"../components/SelectedCategory\";\nimport { get } from \"../utilis/apiService\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst title = /*#__PURE__*/_jsxDEV(\"h2\", {\n  children: [\"Search Our \", /*#__PURE__*/_jsxDEV(\"span\", {\n    children: \"Thousand\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 16\n  }, this), \" of Books\"]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 7,\n  columnNumber: 3\n}, this);\nconst desc = \"We have the largest collection of Books From Every Categories\";\nconst bannerList = [{\n  iconName: \"icofont-users-alt-4\",\n  text: \"1.5 Million Customers\"\n}, {\n  iconName: \"icofont-notification\",\n  text: \"More then 2000 Merchants\"\n}, {\n  iconName: \"icofont-globe\",\n  text: \"Buy Anything Online\"\n}];\nconst Banner = () => {\n  _s();\n  const [searchInput, setSearchInput] = useState(\"\");\n  const [products, setProducts] = useState([]);\n  const [filteredProduct, setFilteredProduct] = useState([]);\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        const result = await get(\"books\");\n        console.log(\"API Response:\", result);\n\n        // Make sure we're using the data array from the response\n        const bookData = result.data || [];\n        setProducts(bookData);\n        setFilteredProduct(bookData);\n      } catch (error) {\n        console.error(\"Error fetching products:\", error);\n      }\n    };\n    fetchProducts();\n  }, []);\n\n  // Search functionality\n  const handleSearch = e => {\n    const searchTerm = e.target.value;\n    setSearchInput(searchTerm);\n\n    // Filter products based on search term - adjusting for your API's property names\n    const filtered = products.filter(product => (product.Title || product.title || \"\").toLowerCase().includes(searchTerm.toLowerCase()));\n    setFilteredProduct(filtered);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"banner-section style-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"banner-content\",\n        children: [title, /*#__PURE__*/_jsxDEV(\"form\", {\n          children: [/*#__PURE__*/_jsxDEV(SelectedCategory, {\n            select: \"all\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"search\",\n            id: \"search\",\n            placeholder: \"Search Your Favorite Book\",\n            value: searchInput,\n            onChange: handleSearch\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"icofont-search\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: desc\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"lab-ul\",\n          children: searchInput && filteredProduct.map((product, i) => /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: `/shop/${product.BookID || product.id}`,\n              children: product.Title || product.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 19\n            }, this)\n          }, i, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n};\n_s(Banner, \"WGPz6Wz/VgCPSTMF0pbI96r/U+w=\");\n_c = Banner;\nexport default Banner;\nvar _c;\n$RefreshReg$(_c, \"Banner\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "SelectedCategory", "get", "jsxDEV", "_jsxDEV", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "desc", "bannerList", "iconName", "text", "Banner", "_s", "searchInput", "setSearchInput", "products", "setProducts", "filteredProduct", "setFilteredProduct", "fetchProducts", "result", "console", "log", "bookData", "data", "error", "handleSearch", "e", "searchTerm", "target", "value", "filtered", "filter", "product", "Title", "toLowerCase", "includes", "className", "select", "type", "name", "id", "placeholder", "onChange", "map", "i", "to", "BookID", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/home/<USER>"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { Link } from \"react-router-dom\";\nimport SelectedCategory from \"../components/SelectedCategory\";\nimport { get } from \"../utilis/apiService\";\n\nconst title = (\n  <h2>\n    Search Our <span>Thousand</span> of Books\n  </h2>\n);\n\nconst desc = \"We have the largest collection of Books From Every Categories\";\nconst bannerList = [\n  {\n    iconName: \"icofont-users-alt-4\",\n    text: \"1.5 Million Customers\",\n  },\n  {\n    iconName: \"icofont-notification\",\n    text: \"More then 2000 Merchants\",\n  },\n  {\n    iconName: \"icofont-globe\",\n    text: \"Buy Anything Online\",\n  },\n];\n\nconst Banner = () => {\n  const [searchInput, setSearchInput] = useState(\"\");\n  const [products, setProducts] = useState([]);\n  const [filteredProduct, setFilteredProduct] = useState([]);\n\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        const result = await get(\"books\");\n        console.log(\"API Response:\", result);\n\n        // Make sure we're using the data array from the response\n        const bookData = result.data || [];\n        setProducts(bookData);\n        setFilteredProduct(bookData);\n      } catch (error) {\n        console.error(\"Error fetching products:\", error);\n      }\n    };\n\n    fetchProducts();\n  }, []);\n\n  // Search functionality\n  const handleSearch = (e) => {\n    const searchTerm = e.target.value;\n    setSearchInput(searchTerm);\n\n    // Filter products based on search term - adjusting for your API's property names\n    const filtered = products.filter((product) =>\n      (product.Title || product.title || \"\")\n        .toLowerCase()\n        .includes(searchTerm.toLowerCase())\n    );\n\n    setFilteredProduct(filtered);\n  };\n\n  return (\n    <div className=\"banner-section style-4\">\n      <div className=\"container\">\n        <div className=\"banner-content\">\n          {title}\n          <form>\n            <SelectedCategory select={\"all\"} />\n            <input\n              type=\"text\"\n              name=\"search\"\n              id=\"search\"\n              placeholder=\"Search Your Favorite Book\"\n              value={searchInput}\n              onChange={handleSearch}\n            />\n            <button type=\"submit\">\n              <i className=\"icofont-search\"></i>\n            </button>\n          </form>\n          <p>{desc}</p>\n          <ul className=\"lab-ul\">\n            {searchInput &&\n              filteredProduct.map((product, i) => (\n                <li key={i}>\n                  <Link to={`/shop/${product.BookID || product.id}`}>\n                    {product.Title || product.title}\n                  </Link>\n                </li>\n              ))}\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Banner;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,SAASC,GAAG,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,KAAK,gBACTD,OAAA;EAAAE,QAAA,GAAI,aACS,eAAAF,OAAA;IAAAE,QAAA,EAAM;EAAQ;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC,aAClC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAI,CACL;AAED,MAAMC,IAAI,GAAG,+DAA+D;AAC5E,MAAMC,UAAU,GAAG,CACjB;EACEC,QAAQ,EAAE,qBAAqB;EAC/BC,IAAI,EAAE;AACR,CAAC,EACD;EACED,QAAQ,EAAE,sBAAsB;EAChCC,IAAI,EAAE;AACR,CAAC,EACD;EACED,QAAQ,EAAE,eAAe;EACzBC,IAAI,EAAE;AACR,CAAC,CACF;AAED,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAE1DC,SAAS,CAAC,MAAM;IACd,MAAMwB,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF,MAAMC,MAAM,GAAG,MAAMtB,GAAG,CAAC,OAAO,CAAC;QACjCuB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEF,MAAM,CAAC;;QAEpC;QACA,MAAMG,QAAQ,GAAGH,MAAM,CAACI,IAAI,IAAI,EAAE;QAClCR,WAAW,CAACO,QAAQ,CAAC;QACrBL,kBAAkB,CAACK,QAAQ,CAAC;MAC9B,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdJ,OAAO,CAACI,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;IACF,CAAC;IAEDN,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAMC,UAAU,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK;IACjChB,cAAc,CAACc,UAAU,CAAC;;IAE1B;IACA,MAAMG,QAAQ,GAAGhB,QAAQ,CAACiB,MAAM,CAAEC,OAAO,IACvC,CAACA,OAAO,CAACC,KAAK,IAAID,OAAO,CAAChC,KAAK,IAAI,EAAE,EAClCkC,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACR,UAAU,CAACO,WAAW,CAAC,CAAC,CACtC,CAAC;IAEDjB,kBAAkB,CAACa,QAAQ,CAAC;EAC9B,CAAC;EAED,oBACE/B,OAAA;IAAKqC,SAAS,EAAC,wBAAwB;IAAAnC,QAAA,eACrCF,OAAA;MAAKqC,SAAS,EAAC,WAAW;MAAAnC,QAAA,eACxBF,OAAA;QAAKqC,SAAS,EAAC,gBAAgB;QAAAnC,QAAA,GAC5BD,KAAK,eACND,OAAA;UAAAE,QAAA,gBACEF,OAAA,CAACH,gBAAgB;YAACyC,MAAM,EAAE;UAAM;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnCN,OAAA;YACEuC,IAAI,EAAC,MAAM;YACXC,IAAI,EAAC,QAAQ;YACbC,EAAE,EAAC,QAAQ;YACXC,WAAW,EAAC,2BAA2B;YACvCZ,KAAK,EAAEjB,WAAY;YACnB8B,QAAQ,EAAEjB;UAAa;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACFN,OAAA;YAAQuC,IAAI,EAAC,QAAQ;YAAArC,QAAA,eACnBF,OAAA;cAAGqC,SAAS,EAAC;YAAgB;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACPN,OAAA;UAAAE,QAAA,EAAIK;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACbN,OAAA;UAAIqC,SAAS,EAAC,QAAQ;UAAAnC,QAAA,EACnBW,WAAW,IACVI,eAAe,CAAC2B,GAAG,CAAC,CAACX,OAAO,EAAEY,CAAC,kBAC7B7C,OAAA;YAAAE,QAAA,eACEF,OAAA,CAACJ,IAAI;cAACkD,EAAE,EAAE,SAASb,OAAO,CAACc,MAAM,IAAId,OAAO,CAACQ,EAAE,EAAG;cAAAvC,QAAA,EAC/C+B,OAAO,CAACC,KAAK,IAAID,OAAO,CAAChC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC,GAHAuC,CAAC;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIN,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACM,EAAA,CAxEID,MAAM;AAAAqC,EAAA,GAANrC,MAAM;AA0EZ,eAAeA,MAAM;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}