{"ast": null, "code": "import useEffect from './useIsomorphicEffect';\nimport { useState } from 'react';\nconst matchersByWindow = new WeakMap();\nconst getMatcher = (query, targetWindow) => {\n  if (!query || !targetWindow) return undefined;\n  const matchers = matchersByWindow.get(targetWindow) || new Map();\n  matchersByWindow.set(targetWindow, matchers);\n  let mql = matchers.get(query);\n  if (!mql) {\n    mql = targetWindow.matchMedia(query);\n    mql.refCount = 0;\n    matchers.set(mql.media, mql);\n  }\n  return mql;\n};\n/**\n * Match a media query and get updates as the match changes. The media string is\n * passed directly to `window.matchMedia` and run as a Layout Effect, so initial\n * matches are returned before the browser has a chance to paint.\n *\n * ```tsx\n * function Page() {\n *   const isWide = useMediaQuery('min-width: 1000px')\n *\n *   return isWide ? \"very wide\" : 'not so wide'\n * }\n * ```\n *\n * Media query lists are also reused globally, hook calls for the same query\n * will only create a matcher once under the hood.\n *\n * @param query A media query\n * @param targetWindow The window to match against, uses the globally available one as a default.\n */\nexport default function useMediaQuery(query, targetWindow = typeof window === 'undefined' ? undefined : window) {\n  const mql = getMatcher(query, targetWindow);\n  const [matches, setMatches] = useState(() => mql ? mql.matches : false);\n  useEffect(() => {\n    let mql = getMatcher(query, targetWindow);\n    if (!mql) {\n      return setMatches(false);\n    }\n    let matchers = matchersByWindow.get(targetWindow);\n    const handleChange = () => {\n      setMatches(mql.matches);\n    };\n    mql.refCount++;\n    mql.addListener(handleChange);\n    handleChange();\n    return () => {\n      mql.removeListener(handleChange);\n      mql.refCount--;\n      if (mql.refCount <= 0) {\n        matchers == null ? void 0 : matchers.delete(mql.media);\n      }\n      mql = undefined;\n    };\n  }, [query]);\n  return matches;\n}", "map": {"version": 3, "names": ["useEffect", "useState", "matchersByWindow", "WeakMap", "getMatcher", "query", "targetWindow", "undefined", "matchers", "get", "Map", "set", "mql", "matchMedia", "refCount", "media", "useMediaQuery", "window", "matches", "setMatches", "handleChange", "addListener", "removeListener", "delete"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/@restart/hooks/esm/useMediaQuery.js"], "sourcesContent": ["import useEffect from './useIsomorphicEffect';\nimport { useState } from 'react';\nconst matchersByWindow = new WeakMap();\nconst getMatcher = (query, targetWindow) => {\n  if (!query || !targetWindow) return undefined;\n  const matchers = matchersByWindow.get(targetWindow) || new Map();\n  matchersByWindow.set(targetWindow, matchers);\n  let mql = matchers.get(query);\n  if (!mql) {\n    mql = targetWindow.matchMedia(query);\n    mql.refCount = 0;\n    matchers.set(mql.media, mql);\n  }\n  return mql;\n};\n/**\n * Match a media query and get updates as the match changes. The media string is\n * passed directly to `window.matchMedia` and run as a Layout Effect, so initial\n * matches are returned before the browser has a chance to paint.\n *\n * ```tsx\n * function Page() {\n *   const isWide = useMediaQuery('min-width: 1000px')\n *\n *   return isWide ? \"very wide\" : 'not so wide'\n * }\n * ```\n *\n * Media query lists are also reused globally, hook calls for the same query\n * will only create a matcher once under the hood.\n *\n * @param query A media query\n * @param targetWindow The window to match against, uses the globally available one as a default.\n */\nexport default function useMediaQuery(query, targetWindow = typeof window === 'undefined' ? undefined : window) {\n  const mql = getMatcher(query, targetWindow);\n  const [matches, setMatches] = useState(() => mql ? mql.matches : false);\n  useEffect(() => {\n    let mql = getMatcher(query, targetWindow);\n    if (!mql) {\n      return setMatches(false);\n    }\n    let matchers = matchersByWindow.get(targetWindow);\n    const handleChange = () => {\n      setMatches(mql.matches);\n    };\n    mql.refCount++;\n    mql.addListener(handleChange);\n    handleChange();\n    return () => {\n      mql.removeListener(handleChange);\n      mql.refCount--;\n      if (mql.refCount <= 0) {\n        matchers == null ? void 0 : matchers.delete(mql.media);\n      }\n      mql = undefined;\n    };\n  }, [query]);\n  return matches;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,uBAAuB;AAC7C,SAASC,QAAQ,QAAQ,OAAO;AAChC,MAAMC,gBAAgB,GAAG,IAAIC,OAAO,CAAC,CAAC;AACtC,MAAMC,UAAU,GAAGA,CAACC,KAAK,EAAEC,YAAY,KAAK;EAC1C,IAAI,CAACD,KAAK,IAAI,CAACC,YAAY,EAAE,OAAOC,SAAS;EAC7C,MAAMC,QAAQ,GAAGN,gBAAgB,CAACO,GAAG,CAACH,YAAY,CAAC,IAAI,IAAII,GAAG,CAAC,CAAC;EAChER,gBAAgB,CAACS,GAAG,CAACL,YAAY,EAAEE,QAAQ,CAAC;EAC5C,IAAII,GAAG,GAAGJ,QAAQ,CAACC,GAAG,CAACJ,KAAK,CAAC;EAC7B,IAAI,CAACO,GAAG,EAAE;IACRA,GAAG,GAAGN,YAAY,CAACO,UAAU,CAACR,KAAK,CAAC;IACpCO,GAAG,CAACE,QAAQ,GAAG,CAAC;IAChBN,QAAQ,CAACG,GAAG,CAACC,GAAG,CAACG,KAAK,EAAEH,GAAG,CAAC;EAC9B;EACA,OAAOA,GAAG;AACZ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASI,aAAaA,CAACX,KAAK,EAAEC,YAAY,GAAG,OAAOW,MAAM,KAAK,WAAW,GAAGV,SAAS,GAAGU,MAAM,EAAE;EAC9G,MAAML,GAAG,GAAGR,UAAU,CAACC,KAAK,EAAEC,YAAY,CAAC;EAC3C,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,MAAMW,GAAG,GAAGA,GAAG,CAACM,OAAO,GAAG,KAAK,CAAC;EACvElB,SAAS,CAAC,MAAM;IACd,IAAIY,GAAG,GAAGR,UAAU,CAACC,KAAK,EAAEC,YAAY,CAAC;IACzC,IAAI,CAACM,GAAG,EAAE;MACR,OAAOO,UAAU,CAAC,KAAK,CAAC;IAC1B;IACA,IAAIX,QAAQ,GAAGN,gBAAgB,CAACO,GAAG,CAACH,YAAY,CAAC;IACjD,MAAMc,YAAY,GAAGA,CAAA,KAAM;MACzBD,UAAU,CAACP,GAAG,CAACM,OAAO,CAAC;IACzB,CAAC;IACDN,GAAG,CAACE,QAAQ,EAAE;IACdF,GAAG,CAACS,WAAW,CAACD,YAAY,CAAC;IAC7BA,YAAY,CAAC,CAAC;IACd,OAAO,MAAM;MACXR,GAAG,CAACU,cAAc,CAACF,YAAY,CAAC;MAChCR,GAAG,CAACE,QAAQ,EAAE;MACd,IAAIF,GAAG,CAACE,QAAQ,IAAI,CAAC,EAAE;QACrBN,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACe,MAAM,CAACX,GAAG,CAACG,KAAK,CAAC;MACxD;MACAH,GAAG,GAAGL,SAAS;IACjB,CAAC;EACH,CAAC,EAAE,CAACF,KAAK,CAAC,CAAC;EACX,OAAOa,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}