.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* App Layout */
.app-wrapper {
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* Main Content */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

/* Header */
.header {
  height: 64px;
  background-color: #fff;
  border-bottom: 1px solid #e5e9f2;
  display: flex;
  align-items: center;
  padding: 0 1.5rem;
}

.header-content {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.user-info:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.user-avatar {
  width: 32px;
  height: 32px;
  background-color: #ff6b6b;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 0.875rem;
}

.user-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #2a3042;
}

/* Content Area */
.content-area {
  flex: 1;
  padding: 1.5rem;
  background-color: #f8f9fa;
}

/* Responsive */
@media (max-width: 768px) {
  .header {
    padding: 0 1rem;
  }

  .content-area {
    padding: 1rem;
  }
}
