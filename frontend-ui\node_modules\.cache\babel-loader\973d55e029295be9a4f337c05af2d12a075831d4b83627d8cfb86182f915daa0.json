{"ast": null, "code": "import { g as getSlideTransformEl, c as createElement } from './utils.mjs';\nfunction createShadow(suffix, slideEl, side) {\n  const shadowClass = `swiper-slide-shadow${side ? `-${side}` : ''}${suffix ? ` swiper-slide-shadow-${suffix}` : ''}`;\n  const shadowContainer = getSlideTransformEl(slideEl);\n  let shadowEl = shadowContainer.querySelector(`.${shadowClass.split(' ').join('.')}`);\n  if (!shadowEl) {\n    shadowEl = createElement('div', shadowClass.split(' '));\n    shadowContainer.append(shadowEl);\n  }\n  return shadowEl;\n}\nexport { createShadow as c };", "map": {"version": 3, "names": ["g", "getSlideTransformEl", "c", "createElement", "createShadow", "suffix", "slideEl", "side", "shadowClass", "shadow<PERSON><PERSON><PERSON>", "shadowEl", "querySelector", "split", "join", "append"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/swiper/shared/create-shadow.mjs"], "sourcesContent": ["import { g as getSlideTransformEl, c as createElement } from './utils.mjs';\n\nfunction createShadow(suffix, slideEl, side) {\n  const shadowClass = `swiper-slide-shadow${side ? `-${side}` : ''}${suffix ? ` swiper-slide-shadow-${suffix}` : ''}`;\n  const shadowContainer = getSlideTransformEl(slideEl);\n  let shadowEl = shadowContainer.querySelector(`.${shadowClass.split(' ').join('.')}`);\n  if (!shadowEl) {\n    shadowEl = createElement('div', shadowClass.split(' '));\n    shadowContainer.append(shadowEl);\n  }\n  return shadowEl;\n}\n\nexport { createShadow as c };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,aAAa,QAAQ,aAAa;AAE1E,SAASC,YAAYA,CAACC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAE;EAC3C,MAAMC,WAAW,GAAG,sBAAsBD,IAAI,GAAG,IAAIA,IAAI,EAAE,GAAG,EAAE,GAAGF,MAAM,GAAG,wBAAwBA,MAAM,EAAE,GAAG,EAAE,EAAE;EACnH,MAAMI,eAAe,GAAGR,mBAAmB,CAACK,OAAO,CAAC;EACpD,IAAII,QAAQ,GAAGD,eAAe,CAACE,aAAa,CAAC,IAAIH,WAAW,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;EACpF,IAAI,CAACH,QAAQ,EAAE;IACbA,QAAQ,GAAGP,aAAa,CAAC,KAAK,EAAEK,WAAW,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC;IACvDH,eAAe,CAACK,MAAM,CAACJ,QAAQ,CAAC;EAClC;EACA,OAAOA,QAAQ;AACjB;AAEA,SAASN,YAAY,IAAIF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}