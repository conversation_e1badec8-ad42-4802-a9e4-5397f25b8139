{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\source\\\\LaravelReact\\\\frontend-ui\\\\src\\\\home\\\\Home.jsx\";\nimport React from \"react\";\nimport Banner from \"./Banner\";\nimport HomeCategory from \"./HomeCategory\";\n// import CategoryShowCase from './CategoryShowCase';\nimport Register from \"./Register\";\n// import Footer from '../components/Footer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Banner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HomeCategory, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "Banner", "HomeCategory", "Register", "jsxDEV", "_jsxDEV", "Home", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/src/home/<USER>"], "sourcesContent": ["import React from \"react\";\nimport Banner from \"./Banner\";\nimport HomeCategory from \"./HomeCategory\";\n// import CategoryShowCase from './CategoryShowCase';\nimport Register from \"./Register\";\n// import Footer from '../components/Footer';\n\nconst Home = () => {\n  return (\n    <div>\n      <Banner />\n      <HomeCategory />\n      {/* <CategoryShowCase/> */}\n      <Register />\n      {/* <Footer/> */}\n    </div>\n  );\n};\n\nexport default Home;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,YAAY,MAAM,gBAAgB;AACzC;AACA,OAAOC,QAAQ,MAAM,YAAY;AACjC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,oBACED,OAAA;IAAAE,QAAA,gBACEF,OAAA,CAACJ,MAAM;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVN,OAAA,CAACH,YAAY;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEhBN,OAAA,CAACF,QAAQ;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAET,CAAC;AAEV,CAAC;AAACC,EAAA,GAVIN,IAAI;AAYV,eAAeA,IAAI;AAAC,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}