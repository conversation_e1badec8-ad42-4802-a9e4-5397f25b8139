{"ast": null, "code": "export const streamChunk = function* (chunk, chunkSize) {\n  let len = chunk.byteLength;\n  if (!chunkSize || len < chunkSize) {\n    yield chunk;\n    return;\n  }\n  let pos = 0;\n  let end;\n  while (pos < len) {\n    end = pos + chunkSize;\n    yield chunk.slice(pos, end);\n    pos = end;\n  }\n};\nexport const readBytes = async function* (iterable, chunkSize) {\n  for await (const chunk of readStream(iterable)) {\n    yield* streamChunk(chunk, chunkSize);\n  }\n};\nconst readStream = async function* (stream) {\n  if (stream[Symbol.asyncIterator]) {\n    yield* stream;\n    return;\n  }\n  const reader = stream.getReader();\n  try {\n    for (;;) {\n      const {\n        done,\n        value\n      } = await reader.read();\n      if (done) {\n        break;\n      }\n      yield value;\n    }\n  } finally {\n    await reader.cancel();\n  }\n};\nexport const trackStream = (stream, chunkSize, onProgress, onFinish) => {\n  const iterator = readBytes(stream, chunkSize);\n  let bytes = 0;\n  let done;\n  let _onFinish = e => {\n    if (!done) {\n      done = true;\n      onFinish && onFinish(e);\n    }\n  };\n  return new ReadableStream({\n    async pull(controller) {\n      try {\n        const {\n          done,\n          value\n        } = await iterator.next();\n        if (done) {\n          _onFinish();\n          controller.close();\n          return;\n        }\n        let len = value.byteLength;\n        if (onProgress) {\n          let loadedBytes = bytes += len;\n          onProgress(loadedBytes);\n        }\n        controller.enqueue(new Uint8Array(value));\n      } catch (err) {\n        _onFinish(err);\n        throw err;\n      }\n    },\n    cancel(reason) {\n      _onFinish(reason);\n      return iterator.return();\n    }\n  }, {\n    highWaterMark: 2\n  });\n};", "map": {"version": 3, "names": ["streamChunk", "chunk", "chunkSize", "len", "byteLength", "pos", "end", "slice", "readBytes", "iterable", "readStream", "stream", "Symbol", "asyncIterator", "reader", "<PERSON><PERSON><PERSON><PERSON>", "done", "value", "read", "cancel", "trackStream", "onProgress", "onFinish", "iterator", "bytes", "_onFinish", "e", "ReadableStream", "pull", "controller", "next", "close", "loadedBytes", "enqueue", "Uint8Array", "err", "reason", "return", "highWaterMark"], "sources": ["C:/Users/<USER>/source/LaravelReact/frontend-ui/node_modules/axios/lib/helpers/trackStream.js"], "sourcesContent": ["\nexport const streamChunk = function* (chunk, chunkSize) {\n  let len = chunk.byteLength;\n\n  if (!chunkSize || len < chunkSize) {\n    yield chunk;\n    return;\n  }\n\n  let pos = 0;\n  let end;\n\n  while (pos < len) {\n    end = pos + chunkSize;\n    yield chunk.slice(pos, end);\n    pos = end;\n  }\n}\n\nexport const readBytes = async function* (iterable, chunkSize) {\n  for await (const chunk of readStream(iterable)) {\n    yield* streamChunk(chunk, chunkSize);\n  }\n}\n\nconst readStream = async function* (stream) {\n  if (stream[Symbol.asyncIterator]) {\n    yield* stream;\n    return;\n  }\n\n  const reader = stream.getReader();\n  try {\n    for (;;) {\n      const {done, value} = await reader.read();\n      if (done) {\n        break;\n      }\n      yield value;\n    }\n  } finally {\n    await reader.cancel();\n  }\n}\n\nexport const trackStream = (stream, chunkSize, onProgress, onFinish) => {\n  const iterator = readBytes(stream, chunkSize);\n\n  let bytes = 0;\n  let done;\n  let _onFinish = (e) => {\n    if (!done) {\n      done = true;\n      onFinish && onFinish(e);\n    }\n  }\n\n  return new ReadableStream({\n    async pull(controller) {\n      try {\n        const {done, value} = await iterator.next();\n\n        if (done) {\n         _onFinish();\n          controller.close();\n          return;\n        }\n\n        let len = value.byteLength;\n        if (onProgress) {\n          let loadedBytes = bytes += len;\n          onProgress(loadedBytes);\n        }\n        controller.enqueue(new Uint8Array(value));\n      } catch (err) {\n        _onFinish(err);\n        throw err;\n      }\n    },\n    cancel(reason) {\n      _onFinish(reason);\n      return iterator.return();\n    }\n  }, {\n    highWaterMark: 2\n  })\n}\n"], "mappings": "AACA,OAAO,MAAMA,WAAW,GAAG,UAAAA,CAAWC,KAAK,EAAEC,SAAS,EAAE;EACtD,IAAIC,GAAG,GAAGF,KAAK,CAACG,UAAU;EAE1B,IAAI,CAACF,SAAS,IAAIC,GAAG,GAAGD,SAAS,EAAE;IACjC,MAAMD,KAAK;IACX;EACF;EAEA,IAAII,GAAG,GAAG,CAAC;EACX,IAAIC,GAAG;EAEP,OAAOD,GAAG,GAAGF,GAAG,EAAE;IAChBG,GAAG,GAAGD,GAAG,GAAGH,SAAS;IACrB,MAAMD,KAAK,CAACM,KAAK,CAACF,GAAG,EAAEC,GAAG,CAAC;IAC3BD,GAAG,GAAGC,GAAG;EACX;AACF,CAAC;AAED,OAAO,MAAME,SAAS,GAAG,gBAAAA,CAAiBC,QAAQ,EAAEP,SAAS,EAAE;EAC7D,WAAW,MAAMD,KAAK,IAAIS,UAAU,CAACD,QAAQ,CAAC,EAAE;IAC9C,OAAOT,WAAW,CAACC,KAAK,EAAEC,SAAS,CAAC;EACtC;AACF,CAAC;AAED,MAAMQ,UAAU,GAAG,gBAAAA,CAAiBC,MAAM,EAAE;EAC1C,IAAIA,MAAM,CAACC,MAAM,CAACC,aAAa,CAAC,EAAE;IAChC,OAAOF,MAAM;IACb;EACF;EAEA,MAAMG,MAAM,GAAGH,MAAM,CAACI,SAAS,CAAC,CAAC;EACjC,IAAI;IACF,SAAS;MACP,MAAM;QAACC,IAAI;QAAEC;MAAK,CAAC,GAAG,MAAMH,MAAM,CAACI,IAAI,CAAC,CAAC;MACzC,IAAIF,IAAI,EAAE;QACR;MACF;MACA,MAAMC,KAAK;IACb;EACF,CAAC,SAAS;IACR,MAAMH,MAAM,CAACK,MAAM,CAAC,CAAC;EACvB;AACF,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGA,CAACT,MAAM,EAAET,SAAS,EAAEmB,UAAU,EAAEC,QAAQ,KAAK;EACtE,MAAMC,QAAQ,GAAGf,SAAS,CAACG,MAAM,EAAET,SAAS,CAAC;EAE7C,IAAIsB,KAAK,GAAG,CAAC;EACb,IAAIR,IAAI;EACR,IAAIS,SAAS,GAAIC,CAAC,IAAK;IACrB,IAAI,CAACV,IAAI,EAAE;MACTA,IAAI,GAAG,IAAI;MACXM,QAAQ,IAAIA,QAAQ,CAACI,CAAC,CAAC;IACzB;EACF,CAAC;EAED,OAAO,IAAIC,cAAc,CAAC;IACxB,MAAMC,IAAIA,CAACC,UAAU,EAAE;MACrB,IAAI;QACF,MAAM;UAACb,IAAI;UAAEC;QAAK,CAAC,GAAG,MAAMM,QAAQ,CAACO,IAAI,CAAC,CAAC;QAE3C,IAAId,IAAI,EAAE;UACTS,SAAS,CAAC,CAAC;UACVI,UAAU,CAACE,KAAK,CAAC,CAAC;UAClB;QACF;QAEA,IAAI5B,GAAG,GAAGc,KAAK,CAACb,UAAU;QAC1B,IAAIiB,UAAU,EAAE;UACd,IAAIW,WAAW,GAAGR,KAAK,IAAIrB,GAAG;UAC9BkB,UAAU,CAACW,WAAW,CAAC;QACzB;QACAH,UAAU,CAACI,OAAO,CAAC,IAAIC,UAAU,CAACjB,KAAK,CAAC,CAAC;MAC3C,CAAC,CAAC,OAAOkB,GAAG,EAAE;QACZV,SAAS,CAACU,GAAG,CAAC;QACd,MAAMA,GAAG;MACX;IACF,CAAC;IACDhB,MAAMA,CAACiB,MAAM,EAAE;MACbX,SAAS,CAACW,MAAM,CAAC;MACjB,OAAOb,QAAQ,CAACc,MAAM,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE;IACDC,aAAa,EAAE;EACjB,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}